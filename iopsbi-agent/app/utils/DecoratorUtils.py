# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/5/19 11:41
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : DecoratorUtils.py
# @project : iopsbi-agent
# @desc : 装饰器工具类
import functools
import logging
import json
from typing import Optional, Callable, Any

from fastapi import Request

from app.schemas.Items import CommonResp

logger = logging.getLogger("app")


def io_decorator_function(original_fun: Optional[Callable] = None):
    """
    io装饰器函数，用于打印出入参
    :param original_fun:
    :return:
    """

    @functools.wraps(original_fun)
    def wrapper(*args: Any, **kwargs: Any):
        logger.info(f"函数名称:{original_fun.__name__}, 入参:args={args}, kwargs={kwargs}")
        try:
            result = original_fun(*args, **kwargs)
        except Exception as e:
            logger.error(f"请求接口时异常:{type(e).__name__} - {str(e)}")
            commonResp = CommonResp()
            commonResp.fail_resp('500', '接口请求异常')
            result = commonResp

        logger.info(f"函数名称:{original_fun.__name__}, 出参:resp={json.dumps(result.__dict__, ensure_ascii=False)}")
        return result

    return wrapper
