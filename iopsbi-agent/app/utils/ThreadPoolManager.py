# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/6/18 15:48
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : ThreadPoolManager.py
# @project : iopsbi-agent
import concurrent.futures
import contextvars
from app.utils.apollo_config_utils import get_value

MAX_WORKER_THREADS = get_value("thread.pool.max.worker.num", 20)


# 自定义线程池，支持传递上下文
class ContextAwareThreadPoolExecutor(concurrent.futures.ThreadPoolExecutor):

    def submit(self, fn, *args, ** kwargs):
        ctx = contextvars.copy_context()
        return super().submit(
            lambda: ctx.run(fn, *args, ** kwargs)
        )


class ThreadPoolManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._pool = ContextAwareThreadPoolExecutor(max_workers=MAX_WORKER_THREADS)
        return cls._instance

    @property
    def pool(self):
        return self._pool

    def shutdown(self):
        self._pool.shutdown(wait=True)



# def task(i, t):
#     print(f"task {i} \n")
#     return i*t
#
# manager = ThreadPoolManager()
# pool = manager.pool
# futures = [pool.submit(task, i, i) for i in range(30)]
# print([f.result() for f in futures])