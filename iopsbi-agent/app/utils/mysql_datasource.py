# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/7/3 15:24
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : mysql_datasource.py.py
# @project : iopsbi-agent
import time
import traceback
from typing import Optional, Union, List, Dict

from dbutils.pooled_db import PooledDB
import mysql.connector
import logging
import os
from app.config.env_var import DATASOURCE_CONFIG
import threading
from mysql.connector import Error
from app.utils.MysqlUtils import MysqlUtils

logger = logging.getLogger("app")


def build_connection_pool(host: str,
                          port: int,
                          user: str,
                          password: str,
                          database: str,
                          pool_size: int = 5,
                          max_overflow: int = 10,
                          timeout: int = 30,
                          charset: str = 'utf8mb4') -> PooledDB:
    """
    初始化连接池
    :param host:
    :param port:
    :param user:
    :param password:
    :param charset:
    :param database:
    :param pool_size: 常驻连接数量
    :param max_overflow: 最大溢出连接数
    :param timeout: 连接超时时间（秒）
    """

    return PooledDB(
        creator=mysql.connector,
        mincached=pool_size,
        maxcached=pool_size + max_overflow,
        host=host,
        port=port,
        user=user,
        password=password,
        database=database,
        charset=charset,
        connect_timeout=timeout
    )


def _get_mysql_config(database_name: str) -> dict:
    logger.info(f"获取mysql数据源配置信息时，当前数据库为:{database_name}")
    env_datasource_cfg = DATASOURCE_CONFIG[database_name]
    if env_datasource_cfg is None:
        logger.error(f"获取mysql数据源配置信息时，当前数据库为:{database_name}，未找到对应的数据源配置，请检查配置")
        raise Exception(f"获取mysql数据源配置信息时，当前数据库为:{database_name}，未找到对应的数据源配置，请检查配置")

    env = os.getenv("GLOBAL_ENV", "dev")
    if env == "stg":
        env = os.getenv("GLOBAL_SYS_IDC", "stg3")
    logger.info(f"获取mysql数据源配置信息时，当前环境变量为:{env}")
    datasource_cfg = env_datasource_cfg[env]
    return {
        "host": datasource_cfg["mysql_host"],
        "port": datasource_cfg["mysql_port"],
        "user": datasource_cfg["mysql_user"],
        "password": datasource_cfg["mysql_password"],
        "database": datasource_cfg["mysql_database"],
        "pool_size": datasource_cfg["mysql_pool_size"],
        "max_overflow": datasource_cfg["mysql_max_overflow"]
    }


lock = threading.Lock()
all_mysql_pools = {}


def _get_mysql_pool(database_name: str) -> PooledDB:
    """
    获取mysql连接池
    :return:
    """
    global all_mysql_pools
    with lock:
        logger.info(f"获取mysql连接池时，当前数据库为:{database_name}")
        mysql_pool = all_mysql_pools.get(database_name, None)
        if mysql_pool is None:
            config = _get_mysql_config(database_name)
            mysql_pool = build_connection_pool(**config)
            all_mysql_pools[database_name] = mysql_pool
        return mysql_pool


class MysqlDatasource:
    def __init__(self, database_name: str):
        self.pool = _get_mysql_pool(database_name)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_all()

    def _get_connection(self):
        """从连接池获取连接"""
        return self.pool.connection()

    def execute_query(self,
                      sql: str,
                      fetch_type: str = 'all',
                      retries: int = 2) -> Optional[Union[List[Dict], Dict, int]]:
        """
        执行 SQL 查询
        :param sql: 要执行的 SQL 语句
        :param params: 查询参数（支持单条或批量）
        :param fetch_type: all/one/count
        :param retries: 失败重试次数
        :return: 根据 fetch_type 返回不同结果
        """
        connection = None
        cursor = None
        attempt = 0

        while attempt <= retries:
            try:
                connection = self._get_connection()
                cursor = connection.cursor(dictionary=True, buffered=True)

                start_time = time.time()
                cursor.execute(sql)

                # 获取结果
                if fetch_type == 'all':
                    result = cursor.fetchall()
                elif fetch_type == 'one':
                    result = cursor.fetchone()
                elif fetch_type == 'count':
                    result = cursor.rowcount
                else:
                    result = None

                # 记录执行时间
                cost = round((time.time() - start_time) * 1000, 2)
                logger.info(f'SQL executed in {cost}ms')

                return result

            except Error as e:
                logger.error(f"Attempt {attempt} failed, 异常信息:{e}, traceback: {traceback.format_exc()}")
                if connection:
                    connection.rollback()
                attempt += 1
                if attempt > retries:
                    raise
                time.sleep(1)  # 失败后等待重试

            finally:
                if cursor:
                    cursor.close()
                if connection:
                    connection.close()

    def execute_insert(self, sql: str):
        """
        执行 插入SQL 查询
        :param sql: 要执行的 SQL 语句
        """
        connection = None
        cursor = None
        try:
            connection = self._get_connection()
            cursor = connection.cursor(dictionary=True, buffered=True)
            start_time = time.time()
            cursor.execute(sql)

            # 记录执行时间
            cost = round((time.time() - start_time) * 1000, 2)
            logger.info(f'SQL executed in {cost}ms')
            insert_cnt = cursor.rowcount
            logger.info(f"插入数据行数:{insert_cnt}")

            connection.commit()
            return insert_cnt

        except Error as e:
            logger.error(f"插入sql执行异常 sql:{sql}, 异常信息:{e}, traceback: {traceback.format_exc()}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def transactional(self, queries: List[dict]) -> bool:
        """
        执行事务操作
        :param queries: 包含多个SQL操作的字典列表
        Example:
        queries = [
            {"sql": "UPDATE...", "params": (...)},
            {"sql": "INSERT...", "params": (...)}
        ]
        """
        connection = None
        try:
            connection = self._get_connection()

            cursor = connection.cursor()
            for query in queries:
                cursor.execute(query['sql'], query.get('params'))

            connection.commit()
            return True
        except Error as e:
            logger.info(f"Transaction failed: 异常信息:{e}, traceback: {traceback.format_exc()}")
            if connection:
                connection.rollback()
            return False
        finally:
            if connection:
                connection.close()

    def close_all(self):
        """关闭所有连接（通常不需要手动调用）"""
        self.pool.close()


if __name__ == '__main__':
    ipss_report_datasource = MysqlDatasource("ipss_report")
    sql = "select 1=1"
    result = ipss_report_datasource.execute_query(sql)
    print(result)
