import json
import logging
import time
from typing import Literal

from langgraph.types import Command

from app.agent.operation.graph.node_types import State

logger = logging.getLogger("app")

'''重选人群包节点'''
def reselect_crowd_node(state:State)->  Command[Literal["__end__"]]:
    data = {
        "event": "node",
        "node_type": "table",
        "node_id": "xz_welcome_card",
        "conversation_id": state.get("conversation_id"),
        "message_id": state.get("message_no"),
        "created_at": int(time.time() * 1000),
        "data": {"node_id": "xz_welcome_card"}
    }
    return Command(
        update={
            "output": json.dumps(data, ensure_ascii=False),
        },
        goto="__end__",
    )