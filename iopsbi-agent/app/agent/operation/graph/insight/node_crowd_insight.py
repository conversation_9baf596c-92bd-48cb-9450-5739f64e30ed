import base64
import json
import logging
import traceback
import zlib
from typing import Literal
from langgraph.types import Command

from app.agent.operation.tools.exceptions import AgentValidationError
from app.agent.operation.graph.node_types import State
from app.agent.operation.graph.nodes import conf, _post_invoke
from app.handlers.agent_chat_dialog_handler import save_dialog_msg_critical_data, query_dialog_msg_critical_data
from app.schemas.Items import DialogMsgCriticalDataSaveReq, DialogMsgCriticalDataQryReq

logger = logging.getLogger("app")

'''确定洞察人群节点'''
def ensure_insight_node(state:State)->  Command[Literal["__end__"]]:
    # 调用接口进行查询纬度画像
    payload = json.loads(state.get('input', '{}')).get('observationIndex')
    payload['msgNo'] = state.get('message_no')
    if payload is None:
        records = query_dialog_msg_critical_data(
            DialogMsgCriticalDataQryReq(session_no=state.get("conversation_id"), key_list=["observationIndex"]))
        payload = json.loads(zlib.decompress(base64.b64decode(records[-1]["value"])).decode('utf-8')) if records and "value" in records[-1] else {}
    # 判空检查 observationIndex 和 nodeList
    if not payload:
        raise AgentValidationError("未获取到缺少必要的节点参数:nodeList", code=400)
    url = "https://" + conf['workflow.request.domain_name'] + "/iops/agent/ai/insight/dimensionInsightQuery"
    # 把msg_no 放入payload中 透传
    data = _post_invoke(url, payload,"维度洞察分析")
    msg_data = {'index_analysis_result': data , "canvas_details" : payload}
    logger.info(f"ensure_insight_node data: {data}")
    # 保存关键节点param数据
    try: # 保存关键节点数据
        # 对 payload 进行 base64 编码
        payload_json = json.dumps(payload, ensure_ascii=False)
        payload_b64 = base64.b64encode(zlib.compress(payload_json.encode('utf-8'))).decode('utf-8')
        save_dialog_msg_critical_data(DialogMsgCriticalDataSaveReq(
            session_no=state.get("conversation_id"),
            critical_data=[{
                "msg_no": state.get("message_no"),
                "data": [{
                    "key": "observationIndex",
                    "value": payload_b64
                }]
            }]
        ))
    except Exception as e:
        logger.error(f"保存关键节点数据失败: {e}, traceback: {traceback.format_exc()}")
    return Command(
        update={
            "output": json.dumps({"message_no": state.get("message_no"),
                                  "message": "洞察画布&指标分析结果已于页面展示，请于页面查看",
                                  "node_id": "insight_canvas_display"}, ensure_ascii=False),
            "msg_data": json.dumps(msg_data, ensure_ascii=False)
        },
        goto="__end__",
    )


if __name__ == "__main__":
    # 构造测试 State
    class DummyState(dict):
        def get(self, key, default=None):
            return super().get(key, default)


    test_state = DummyState({
        "input": json.dumps({"observationIndex": {"nodeList": [{"id": 1, "name": "test"}]}}),
        "conversation_id": "test_session_001",
        "message_no": "msg_001"
    })
    result = ensure_insight_node(test_state)
    print(result)
