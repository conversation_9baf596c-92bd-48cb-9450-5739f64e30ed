# -*- coding: utf-8 -*-
import json
import logging
import re
import traceback
import uuid
from typing import Literal

from langchain_core.messages import HumanMessage
from langgraph.types import Command

from app.agent.operation.graph.crowd_analyze import label_merge
from app.agent.operation.graph.node_types import State
from app.agent.operation.graph.nodes import conf, _post_invoke
from app.agent.common.llm.model_invoke import invoke_message
from app.agent.operation.prompt.template import apply_dynamic_template
from app.agent.common.rag.rag_invoke import invoke_deep_bank_knowledge
from app.agent.operation.tools.exceptions import AgentValidationError
from app.handlers.agent_chat_dialog_handler import save_dialog_msg_critical_data, query_dialog_msg_critical_data
from app.handlers.label_relation_query_handler import query_relation_labels_stat_info
from app.schemas.Items import RelationLabelStatInfoQueryReq, DialogMsgCriticalDataSaveReq, DialogMsgCriticalDataQryReq

logger = logging.getLogger("app")

def split_input_node(state: State) ->  Command[Literal["__end__"]]:
    __output = None
    __crowdNo, __rule = _get_crowd_params(state)
    if not __crowdNo and __rule is None:
        records = query_dialog_msg_critical_data(
            DialogMsgCriticalDataQryReq(session_no=state.get("conversation_id"), key_list=["crowdResult"]))
        for record in records or []:
            if record["key"] == "crowdResult":
                __output = record["value"]
        # 如果存在历史记录，则直接返回
        if __output is not None and len(__output) > 2:
            logger.info(f"从历史记录中获取到人群包信息: {__output}")
            return Command(
                update={"output": __output},goto="__end__")
    # 调用分词接口
    url = "https://" + conf['workflow.request.domain_name'] +"/iops/agent/ai/text/segment"
    payload = {
        "text":  state['query']
    }
    segments = []
    data = _post_invoke(url, payload,"分词接口")
    if data:
        segments = data.get('segments')
    # append result to messages
    return Command(
        update={
            "messages": [
                HumanMessage(
                    content=json.dumps(segments),
                    name="split_input_node",
                )
            ],
            "result": json.dumps(segments),
        },
        goto="model_analyze_crowd_node",
    )


'''调用大模型分析接口，返回数据'''
def model_analyze_crowd_node(state:State)->  Command[Literal["__end__"]]:
    try:
        # 调用模型分析接口
        _input = state['query']
        messages = apply_dynamic_template("llm_split", user_input=_input)
        res = invoke_message(messages)
        llm_split = res.split(",")
        #llm_split.append([]) #传入内容本身，防止误识别C232423
        logger.info(f"split the  words: {llm_split}")
        # 解析返回结果 如何符合C/N 格式，则直接返回人群包编号，否则解析label标签
        pattern = r'^[NC]\d{3,9}$'
        for seg in llm_split:
            seg = seg.strip()
            if len(seg) > 1 and re.match(pattern, seg) is not None:
                return Command(
                    update={
                        "messages": state["messages"] + [
                            HumanMessage(
                                content = json.dumps(llm_split),
                                name = "model_analyze_crowd_node",
                            )
                        ],
                        "result": json.dumps({"crowdNo": seg}),
                    },
                    goto="crowd_info_node",
                )
        #如果没有匹配到C/N格式，则解析label标签
        merge = json.loads(state["result"]) if state.get("result") and len(state["result"]) > 2 else []
        merge.extend(llm_split)
        # 匹配人群包的标签 对分词结果进行去重 校验
        unique_labels = list(set(merge))
        labels = []
        for seg in unique_labels:
            if len(seg)>2 and len(seg)<10:
                res = invoke_deep_bank_knowledge("运营_标签0626",seg)
                if res is not None and len(res) > 0 : #获取标签key
                    docs_sorted = sorted(res, key=lambda x: x.similarity, reverse=True)
                    top5 = docs_sorted[:5]
                    for doc in top5:
                        # 解析返回的文档内容，提取标签
                        match = re.search(r'Answer:\s*(.*)', doc.page_content)
                        if match and match.group(1).strip() not in labels:
                            labels.append(match.group(1).strip())
        return Command(
                update={
                    "messages": state["messages"] + [
                        HumanMessage(
                            content = json.dumps(labels),
                            name = "model_analyze_crowd_node",
                        )
                    ],
                    "result": json.dumps({"crowd_labels": labels}),# 返回标签信息
                },
                goto="crowd_label_node",
            )
    except Exception as e:
        logger.error(f"model_analyze_crowd_node 执行异常: {e}\n{traceback.format_exc()}")
        return Command(
            update={
                "messages": state.get("messages", []) + [
                    HumanMessage(
                        content="模型分析节点执行异常",
                        name="model_analyze_crowd_node",
                    )
                ],
                "output": json.dumps({"error": str(e)}),
            },
            goto="__end__",
        )

def crowd_info_node(state:State)->  Command[Literal["__end__"]]:
    _crowdNo, _crowdRule = _get_crowd_params(state)
    logger.info(f"获取人群包编号crowdNo: {_crowdNo}, crowdRule: {_crowdRule}")
    # 调用人群包信息接口
    url = "https://"+ conf['workflow.request.domain_name']+"/iops/agent/ai/insight/rootInsightSave"
    payload = {
        "bagClass": 1,
        "userId": state["user_info"]["user_id"],
        "username": state["user_info"]["user_name"],
        "msgNo": uuid.uuid4().hex,
        "sessionNo": uuid.uuid4().hex,
    }
    _crowd_desc = {}
    _template = ""
    if _crowdNo is not None and len(_crowdNo) > 0:
        payload["crowdRule"] = _crowdNo
        payload["crowdType"] = 1
        _crowd_desc["crowdType"] = 1
        _template = "好的，提取到{crowdNo}这个人群包，由{user_name}创建名称为 【{crowdName}】的标签类型，您要对此人群包进行洞察分析，请先选择具体的维度标签和观测指标"
    elif _crowdRule is not None and len(_crowdRule) > 0:
        payload["crowdRule"] = json.dumps(_crowdRule)
        payload["crowdType"] = 2
        _crowd_desc["crowdType"] = 2
        _template = "好的，已提取到这个人群包，您要对此人群包进行洞察分析，请先选择具体的维度标签和观测指标"
    data = _post_invoke(url, payload,"人群包编号")
    if data is not None:
        for item in data:
            if item["key"] == "failMsg":
                raise AgentValidationError(item["value"], code=400)
            _crowd_desc[item["key"]] = item["value"]
        logger.info(f"crowd data: {data}")
    template = (_template.format(crowdNo=_crowd_desc.get("crowdNo",""), user_name=_crowd_desc.get("createUser",""), crowdName=_crowd_desc.get("crowdName","")))
    __output=json.dumps({"crowd": _crowd_desc,"message":template, "node_id": "clustering_dimension_selection","crowdRule": _crowdRule},
                                 ensure_ascii=False)
    # 保存结果信息信息
    save_dialog_msg_critical_data(DialogMsgCriticalDataSaveReq(
        session_no=state.get("conversation_id"),
        critical_data=[{"msg_no": state.get("message_no"),
                        "data": [{"key": "crowdResult", "value": __output}]}]
    ))
    return Command(
        update={
            "messages": [
                HumanMessage(
                    content=json.dumps(
                        {"crowdNo": _crowdNo, "crowd": _crowd_desc, "crowdRule": _crowdRule},
                        ensure_ascii=False),
                    name="crowd_info_node",
                )
            ],
            "output": __output,
        },
        goto="__end__",
    )


def _get_crowd_params(state):
    # 优化：统一解析，减少重复判断
    _crowdNo = ""
    _crowdRule = None
    # 1. 优先从 result 获取
    result_dict = json.loads(state.get("result", "{}"))
    _crowdNo = json.loads(state.get("input", "{}")).get("crowdNo", "")
    _crowdRule = json.loads(state.get("input", "{}")).get("crowdRule", None)
    # 2. 若未获取到，再从 input 获取
    if not _crowdNo:
        _crowdNo = result_dict.get("crowdNo", "")
    if not _crowdRule:
        _crowdRule = result_dict.get("crowdRule", None)
    return _crowdNo, _crowdRule


'''根据标签获取人群'''
def crowd_label_node(state:State)->  Command[Literal["__end__"]]:
    # 获取标签相关信息
    crowd_labels = json.loads(state["result"])["crowd_labels"]
    logger.info(f"获取标签信息: {crowd_labels}")
    res = label_merge(crowd_labels,state["user_info"]["user_name"])
    # 获取到标签信息后 查询标签取值
    commonRsp = query_relation_labels_stat_info(RelationLabelStatInfoQueryReq(labelKeys=json.loads(res["labels"])))
    values = commonRsp.data
    if commonRsp.flag == "S":
        values = commonRsp.data
    # 调用模型生成数据
    messages = apply_dynamic_template("infer_crowd", labels=res["labels"], dblabels=res["dblabels"],
                                      suggest_labels_all=res["suggest_labels_all"], text=values,user_input=state.get("query", ""))
    res = invoke_message(messages)
    crowd = res.replace('\n```', '').replace("```json\n", "")
    crowd = json.loads(crowd)
    logger.info(f"模型生成数据_人群包根节点数据: {crowd}")
    return Command(
        update={
            "messages": [
                HumanMessage(
                    content=json.dumps(crowd),
                    name="crowd_label_node",
                )
            ],
            "result": json.dumps({"crowdRule": crowd}),
        },
        goto="crowd_info_node",
    )
