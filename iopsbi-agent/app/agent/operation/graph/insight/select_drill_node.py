'''确定洞察人群节点'''
import json
from typing import Literal

from langgraph.types import Command

from app.agent.operation.graph.node_types import State

'''下钻节点选择'''


def execute(state: State) -> Command[Literal["__end__"]]:
    # 根据点击的节点信息进行下钻选择
    drillNodeParam = json.loads(state.get('input', '{}')).get("drillNodeParam",'{}')

    return Command(
        update={
            "output": json.dumps({
                "node_id": "customer_segment_drilldown_dimension_selection",
                "drillNodeParam" : drillNodeParam
            }, ensure_ascii=False)
        },
        goto="__end__",
    )
