from datetime import datetime
from typing import Optional
from app.agent.operation.tools.event.ai_message_entity import AIMessageEvent
from langchain_core.messages import AIMessageChunk


def _convert_event_to_message(event: dict, global_variable : dict) -> Optional[AIMessageEvent]:
    """将原始事件转换为AIMessageEvent对象"""
    try:
        # 处理流式消息事件
        if event.get("event") == "on_chat_model_stream":
            return _handle_stream_event(event, global_variable)

        # 处理结束事件
        elif event.get("event") == "on_chat_model_end":
            return _handle_end_event(event, global_variable)

    except (KeyError, AttributeError) as e:
        print(f"事件转换失败: {e}")
    return None


def _handle_stream_event(event: dict, global_variable : dict) -> Optional[AIMessageEvent]:
    """处理流式消息事件"""
    if not isinstance(event['data'].get('chunk'), AIMessageChunk):
        return None

    chunk = event['data']['chunk']
    return AIMessageEvent(
        event="message",
        conversation_id = global_variable['conversation_id'],
        message_id=event["run_id"],
        created_at=_current_timestamp(),
        id=chunk.id,
        answer=chunk.content,
        agent_id=global_variable.get("agent_id", "")
    )


def _handle_end_event(event: dict, global_variable : dict) -> Optional[AIMessageEvent]:
    """处理结束事件"""
    if not isinstance(event['data'].get('output'), AIMessageChunk):
        return None

    output = event['data']['output']
    return AIMessageEvent(
        event="message_end",
        conversation_id=global_variable['conversation_id'],
        message_id=event["run_id"],
        created_at=_current_timestamp(),
        id=output.id,
        answer=output.content,
        agent_id=global_variable.get("agent_id", "")
    )


def _current_timestamp() -> int:
    """获取当前时间戳（毫秒）"""
    return int(datetime.now().timestamp() * 1000)