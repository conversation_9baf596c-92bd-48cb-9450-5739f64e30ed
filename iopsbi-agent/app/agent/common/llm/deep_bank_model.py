# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT
import os
import time
from enum import Enum
from typing import Any

from deepbank_adk import Client, DeepbankChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

import logging

from app.agent.common.agent import AgentName
from app.utils.apollo_config_utils import get_value

logger = logging.getLogger("app")

# 缓存模型客户端，避免重复初始化
_deep_bank_model_cache: dict[str, Any] = {}

# 缓存模型客户端，key值为agentName,value为模型名称和model的字典
_model_cache_by_name: dict[str, dict[str, Any]] = {}


class ModelType(str, Enum):
    DEEPSEEK_R1 = '360/deepseek-r1'
    DEEPSEEK_R1_0528 = '360/deepseek-r1-0528'
    DEEPSEEK = '360/deepseek-v3-250324'
    QWEN2_VL_7B = 'deepbank/qwen2.5-vl-7b'
    QWEN3_32B = '360/qwen3-32b'
    QWNE3_235B_A22B = '360/qwen3-235b-a22b'
    QWEN3_CODER_480B_A35B = '360/360-qwen3-coder-480b-a35b'
    QWEN2dot5_7B = 'deepbank/qwen2.5-7b'
    QWEN2dot5_32B = 'deepbank/qwen2.5-32b'
    QWEN2dot5_72B = 'deepbank/qwen2.5-72b'
    QWEN2dot5_VL_72B = '360/qwen2.5-vl-72b-instruct'
    KIMI_K2 = '360/360-kimi-k2-instruct'


def get_deep_bank_config():
    env = os.getenv("GLOBAL_ENV", "dev")
    debug_mode = env != "prd"
    logger.info(f"当前环境为debug_mode:{env}, {debug_mode}")
    configs = {}
    for agent_name in AgentName:
        prefix = f"{agent_name.value}.deep_bank"
        configs.update({
            f"{prefix}.api_key": get_value(f"{prefix}.api_key"),
            f"{prefix}.debug_mode": debug_mode,
            f"{prefix}.model_name": get_value(f"{prefix}.model_name"),
            f"{prefix}.agent_id": get_value(f"{prefix}.agent_id"),
            f"{prefix}.knowledge.similarity_threshold": get_value(f"{prefix}.knowledge.similarity_threshold"),
            f"{prefix}.knowledge.top_n": get_value(f"{prefix}.knowledge.top_n"),
        })
    return configs


def _init_deep_bank_model(conf: dict[str, Any], agent_name: str) -> Any:
    """初始化 DeepBank 模型客户端实例。"""
    _deep_bank_model_cache.clear()
    prefix = f"{agent_name}.deep_bank"
    client = Client.build(conf[f"{prefix}.api_key"], debug_mode=conf[f"{prefix}.debug_mode"])
    llm_connections = client.models.llm_connections
    for conn in llm_connections:
        print(conn)
        logger.info(conn)
    return client.models.chat_openai(conf[f"{prefix}.model_name"])


def _init_deep_bank_model_by_name(conf: dict[str, Any], agent_name: str) -> Any:
    """初始化 DeepBank 模型客户端实例。"""
    if agent_name in _model_cache_by_name:
        del _model_cache_by_name[agent_name]
    prefix = f"{agent_name}.deep_bank"
    client = Client.build(conf[f"{prefix}.api_key"], debug_mode=conf[f"{prefix}.debug_mode"])
    llm_connections = client.models.llm_connections
    agent_model_dict = {}
    for conn in llm_connections:
        print(conn)
        logger.info(conn)
        model_type = conn.model
        llm = client.models.chat_openai(model_type)
        agent_model_dict[model_type] = llm
    _model_cache_by_name[agent_name] = agent_model_dict
    return agent_model_dict


def get_deep_bank_model(agent_name: str) -> Any:
    """
    获取 DeepBank 模型实例，使用缓存以提高性能。
    """

    if agent_name in _deep_bank_model_cache:
        return _deep_bank_model_cache[agent_name]

    model = _init_deep_bank_model(get_deep_bank_config(), agent_name)
    _deep_bank_model_cache[agent_name] = model
    return model


def reset_deep_bank_model(agent_name: str, model_name: str):
    if agent_name in _deep_bank_model_cache:
        _deep_bank_model_cache.pop(agent_name)
    logger.info(f"重置 DeepBank 模型缓存: {agent_name}")
    prefix = f"{agent_name}.deep_bank"
    conf = get_deep_bank_config()
    client = Client.build(conf[f"{prefix}.api_key"], debug_mode=conf[f"{prefix}.debug_mode"])
    llm_connections = client.models.llm_connections
    for conn in llm_connections:
        if conn.name == model_name:
            _deep_bank_model_cache[agent_name] = client.models.chat_openai(conf[f"{prefix}.model_name"])


def get_deep_bank_model_by_name(agent_name: str, model_name: str, use_default_not_exist=True) -> DeepbankChatOpenAI:
    """
    获取 DeepBank 模型实例，使用缓存以提高性能。
    """

    if agent_name in _model_cache_by_name and model_name in _model_cache_by_name[agent_name]:
        return _model_cache_by_name[agent_name][model_name]
    else:
        agent_model_dict = _init_deep_bank_model_by_name(get_deep_bank_config(), agent_name)
        if model_name in agent_model_dict:
            return agent_model_dict[model_name]
        else:
            if use_default_not_exist and agent_model_dict:
                default_model_name = get_deep_bank_config()[f"{agent_name}.deep_bank.model_name"]
                if default_model_name in agent_model_dict:
                    return agent_model_dict[default_model_name]
                else:
                    return next(iter(agent_model_dict.values()))
            else:
                raise RuntimeError(f"模型 {model_name} 不存在")


def get_deep_bank_tracer(agent_name: str = AgentName.AGENT_OP.value, **kwargs) -> Any:
    if f"{agent_name}.tracer" in _deep_bank_model_cache:
        return _deep_bank_model_cache[f"{agent_name}.tracer"]
    conf = get_deep_bank_config()
    client = Client.build(conf[f"{agent_name}.deep_bank.api_key"],
                          debug_mode=conf[f"{agent_name}.deep_bank.debug_mode"])
    tracer = client.telemetry.langgraph_tracer(**kwargs)
    _deep_bank_model_cache[f"{agent_name}.tracer"] = tracer
    return tracer


def chat_with_deep_bank(content: str, agent_name: str) -> str:
    """
    与 DeepBank 模型对话。

    参数:
        prompt (str): 提示内容。

    返回:
        str: 模型返回的响应。
    """
    try:
        # 获取模型并调用
        start_time = time.time()  # 记录开始时间
        # 打印耗时信息
        model = get_deep_bank_model(agent_name)
        ai_message = model.invoke(content)
        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算耗时
        logger.info(f"模型调用耗时: {elapsed_time:.4f}秒")
        return ai_message

    except Exception as e:
        _init_deep_bank_model(get_deep_bank_config(), agent_name)  # 所有未知错误类型都尝试重制一下模型客户端
        raise RuntimeError(f"调用 DeepBank 模型失败: {e}")


def chat_with_deep_bank_by_llm(content: str, agent_name: str, llm: object) -> object:
    """
    与 DeepBank 模型对话。

    参数:
        prompt (str): 提示内容。

    返回:
        str: 模型返回的响应。
    """
    try:
        # 获取模型并调用
        start_time = time.time()  # 记录开始时间
        # 打印耗时信息
        ai_response = llm.invoke(content)
        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算耗时
        logger.info(f"模型调用耗时: {elapsed_time:.4f}秒")
        return ai_response

    except Exception as e:
        _init_deep_bank_model(get_deep_bank_config(), agent_name)  # 所有未知错误类型都尝试重制一下模型客户端
        _init_deep_bank_model_by_name(get_deep_bank_config(), agent_name)  # 所有未知错误类型都尝试重制一下模型客户端
        raise RuntimeError(f"调用 DeepBank 模型失败: {e}")


def chat_message_with_deep_bank(message_list: list, agent_name: str) -> str:
    """
    messages = [
        ("system", "You are a helpful assistant that translates English to French. Translate the user sentence."),
        ("human", "I love programming."),
    ]
    """
    try:
        # 获取模型并调用
        start_time = time.time()  # 记录开始时间

        # 打印耗时信息
        llm = get_deep_bank_model(agent_name)  # 获取模型
        ai_message = llm.invoke(message_list)  # 调用模型

        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算耗时
        logger.info(f"模型调用耗时: {elapsed_time:.4f}秒")
        return ai_message.content  # 返回模型的回应
    except ConnectionError as e:
        logger.error(f"模型连接失败: {e}")  # 记录日志
        raise RuntimeError(f"模型连接失败: {e}")  # 更具描述性的信息
    except TimeoutError as e:
        logger.error(f"模型调用超时: {e}")  # 记录日志
        raise RuntimeError(f"模型调用超时: {e}")
    except AttributeError as e:
        logger.error(f"模型属性错误: {e}")  # 记录日志
        raise RuntimeError(f"模型属性错误: {e}")
    except Exception as e:
        logger.error(f"调用 DeepBank 模型失败: {e}")  # 记录日志
        _init_deep_bank_model(get_deep_bank_config(), agent_name)
        raise RuntimeError(f"调用 DeepBank 模型失败: {e}")  # 捕获所有其他异常

    finally:
        # 可以在这里记录一些调试信息，或者做清理工作
        logger.debug("模型调用结束")


def chat_message_template_with_deep_bank(prompt_template: ChatPromptTemplate, input_message: object,
                                         agent_name: str) -> str:
    """
prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            "You are a helpful assistant that translates {input_language} to {output_language}.",
        ),
        ("human", "{input}"),
    ]
)
    {
        "input_language": "English",
        "output_language": "German",
        "input": "I love programming.",
    }
    """
    try:
        # 获取模型并调用
        start_time = time.time()  # 记录开始时间
        llm = get_deep_bank_model(agent_name)  # 获取模型
        chain = prompt_template | llm
        ai = chain.invoke(input_message)
        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算耗时
        logger.info(f"模型调用耗时: {elapsed_time:.4f}秒")
        return ai.content  # 返回模型的回应
    except ConnectionError as e:
        logger.error(f"模型连接失败: {e}")  # 记录日志
        raise RuntimeError(f"模型连接失败: {e}")  # 更具描述性的信息
    except TimeoutError as e:
        logger.error(f"模型调用超时: {e}")  # 记录日志
        raise RuntimeError(f"模型调用超时: {e}")
    except AttributeError as e:
        logger.error(f"模型属性错误: {e}")  # 记录日志
        raise RuntimeError(f"模型属性错误: {e}")
    except Exception as e:
        logger.error(f"调用 DeepBank 模型失败: {e}")  # 记录日志
        _init_deep_bank_model(get_deep_bank_config(), agent_name)
        raise RuntimeError(f"调用 DeepBank 模型失败: {e}")  # 捕获所有其他异常

    finally:
        # 可以在这里记录一些调试信息，或者做清理工作
        logger.debug("模型调用结束")


def chat_message_template_with_deep_bank_by_llm(prompt_template: ChatPromptTemplate, input_message: object,
                                                agent_name: str, llm: DeepbankChatOpenAI) -> object:
    """
    prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            "You are a helpful assistant that translates {input_language} to {output_language}.",
        ),
        ("human", "{input}"),
    ]
)
    {
        "input_language": "English",
        "output_language": "German",
        "input": "I love programming.",
    }
    """
    try:
        # 获取模型并调用
        start_time = time.time()  # 记录开始时间
        prompt = prompt_template.invoke(input_message)
        ai_response = llm.invoke(prompt)
        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算耗时
        logger.info(f"模型调用耗时: {elapsed_time:.4f}秒")
        return ai_response  # 返回模型的回应
    except ConnectionError as e:
        logger.error(f"模型连接失败: {e}")  # 记录日志
        raise RuntimeError(f"模型连接失败: {e}")  # 更具描述性的信息
    except TimeoutError as e:
        logger.error(f"模型调用超时: {e}")  # 记录日志
        raise RuntimeError(f"模型调用超时: {e}")
    except AttributeError as e:
        logger.error(f"模型属性错误: {e}")  # 记录日志
        raise RuntimeError(f"模型属性错误: {e}")
    except Exception as e:
        logger.exception(f"调用 DeepBank 模型失败: {e}")  # 记录日志
        _init_deep_bank_model(get_deep_bank_config(), agent_name)  # 所有未知错误类型都尝试重制一下模型客户端
        _init_deep_bank_model_by_name(get_deep_bank_config(), agent_name)  # 所有未知错误类型都尝试重制一下模型客户端
        raise RuntimeError(f"调用 DeepBank 模型失败: {e}")  # 捕获所有其他异常

    finally:
        # 可以在这里记录一些调试信息，或者做清理工作
        logger.debug("模型调用结束")
