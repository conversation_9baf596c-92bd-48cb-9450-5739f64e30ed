class DocumentMetadata:
    def __init__(
        self,
        favicon: str = None,
        source: str = None,
        doc_url: str = None,
        doc_keyword: str = None,
        doc_name: str = None,
        highlight: str = None,
        similarity: float = None,
        doc_id: str = None,
        engine: str = None,
        page_content : str = None
    ):
        self.favicon = favicon
        self.source = source
        self.doc_url = doc_url
        self.doc_keyword = doc_keyword
        self.doc_name = doc_name
        self.highlight = highlight
        self.similarity = similarity
        self.doc_id = doc_id
        self.engine = engine
        self.page_content = page_content

    def to_dict(self):
        return {
            "favicon": self.favicon,
            "source": self.source,
            "doc_url": self.doc_url,
            "doc_keyword": self.doc_keyword,
            "doc_name": self.doc_name,
            "highlight": self.highlight,
            "similarity": self.similarity,
            "doc_id": self.doc_id,
            "engine": self.engine,
            "page_content": self.page_content,
        }

    def __repr__(self):
        return f"DocumentMetadata({self.to_dict()})"
