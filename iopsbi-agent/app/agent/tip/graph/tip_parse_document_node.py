import json
import logging
from dataclasses import dataclass
from enum import Enum

from deepbank_adk.frontend import InteractionContext
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnableConfig
from langgraph.graph import MessagesState
from langgraph.types import Send
from typing import Annotated, TypedDict

from app.agent.common.agent import AgentName
from app.agent.common.llm.deep_bank_model import get_deep_bank_model_by_name, ModelType, \
    chat_message_template_with_deep_bank_by_llm
from app.agent.common.utils.json_utils import extract_json_from_string
from app.agent.tip.prompt.tip_parse_document_promopt import analysis_headings_prompt, extract_exist_headings_prompt, \
    parse_common_structure_enum_prompt, parse_interface_prompt
from app.agent.tip.services.tip_api_chema_service import get_api_schema_base_info_by_type, save_schema_info
from app.agent.tip.utils.markdown_parser import MarkdownParser

logger = logging.getLogger("app")


class TipParseDocumentState(MessagesState):
    file_name: Annotated[str, "文件名称"]

    file_suffix: Annotated[str, "文件后缀"]

    partner_code: Annotated[str, "渠道编码"]

    partner_desc: Annotated[str, "渠道描述"]

    force_update: Annotated[bool, "是否强制更新"]

    file_markdown_content: Annotated[str, "文档markdown格式内容"]

    all_headings: Annotated[list, "文档中所有的标题"]

    file_valid: Annotated[bool, "文件是否有效"]

    interface_name_arr: Annotated[list, "接口名称"]

    common_structure_arr: Annotated[list, "公共结构名称"]

    enum_arr: Annotated[list, "枚举名称"]

    exist_interface_name_arr: Annotated[list, "接口名称"]

    exist_common_structure_arr: Annotated[list, "公共结构名称"]

    exist_enum_arr: Annotated[list, "枚举名称"]

    parse_interface_content_arr: Annotated[list, "需要解析的接口文档内容"]

    parse_common_structure_content_arr: Annotated[list, "需要解析的公共结构内容"]

    parse_enum_content_arr: Annotated[list, "需要解析的枚举内容"]

    all_common_structure_arr: Annotated[list, "所有的公共结构名称"]

    all_enum_arr: Annotated[list, "所有的枚举名称"]


class TipSingleParseState(TypedDict):
    file_name: Annotated[str, "文件名称"]

    file_suffix: Annotated[str, "文件后缀"]

    partner_code: Annotated[str, "渠道编码"]

    partner_desc: Annotated[str, "渠道描述"]

    force_update: Annotated[bool, "是否强制更新"]

    content: Annotated[str, "文档内容"]

    exist_interface_name_arr: Annotated[list, "接口名称"]

    exist_common_structure_arr: Annotated[list, "公共结构名称"]

    exist_enum_arr: Annotated[list, "枚举名称"]

    all_common_structure_arr: Annotated[list, "所有的公共结构名称"]

    all_enum_arr: Annotated[list, "所有的枚举名称"]


@dataclass
class TipParseDocumentNodeEnum(Enum):
    FILE_VALID = ("file_valid", "文件有效性校验", "custom")
    ANALYSIS_HEADINGS = ("analysis_headings", "标题分析", "custom")
    EXTRACT_PARSE_CONTENT = ("extract_parse_content", "提取需要解析的文档内容", "custom")
    PARSE_COMMON_STRUCTURE_AND_ENUM = ("parse_common_structure_and_enum", "解析公共结构和枚举", "custom")
    GET_ALL_COMMON_STRUCT_ENUM = ("get_all_common_struct_enum", "获取所有公共结构和枚举", "custom")
    PARSE_INTERFACE = ("parse_interface", "解析接口", "custom")
    PARSE_RESULT = ("parse_result", "解析结果", "custom")

    def __init__(self, code, desc, output_mode):
        self.code = code
        self.desc = desc
        self.output_mode = output_mode

    @classmethod
    def get_by_code(cls, code):
        for member in cls:
            if member.code == code:
                return member
        return None

    @classmethod
    def is_stream_mode(cls, code):
        tip_code_generate_node_enum = cls.get_by_code(code)
        if tip_code_generate_node_enum:
            return "stream" == tip_code_generate_node_enum.output_mode
        else:
            return False


async def file_valid(state: TipParseDocumentState, config: RunnableConfig) -> TipParseDocumentState:
    ctx: InteractionContext = config["configurable"]["ctx"]
    await ctx.stream_message("文件有效性校验开始")
    if state["all_headings"] is None or state["file_markdown_content"] is None:
        state["file_valid"] = False
        await ctx.send_error("文件内容不正确,请重新上传", "文件内容错误")
    else:
        state["file_valid"] = True
    await ctx.stream_message(f"文件有效性校验结束，文件是否有效:{state['file_valid']}")
    return state


async def analysis_headings(state: TipParseDocumentState, config: RunnableConfig) -> TipParseDocumentState:
    ctx: InteractionContext = config["configurable"]["ctx"]
    await ctx.stream_message("文件标题分析开始")
    prompt_template = ChatPromptTemplate(
        [
            ("system", analysis_headings_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    messages = {
        "messages": [HumanMessage(content=json.dumps(state["all_headings"], ensure_ascii=False))],
    }
    llm = get_deep_bank_model_by_name(AgentName.AGENT_TIP.value, ModelType.QWEN3_CODER_480B_A35B.value)
    llm.temperature = 0
    response = chat_message_template_with_deep_bank_by_llm(prompt_template, messages, AgentName.AGENT_TIP.value, llm)
    response_data = json.loads(extract_json_from_string(response.content))
    state["interface_name_arr"] = []
    state["common_structure_arr"] = []
    state["enum_arr"] = []
    if response_data is not None:
        if "interface_name" in response_data:
            state["interface_name_arr"] = response_data["interface_name"]
        if "common_structure" in response_data:
            state["common_structure_arr"] = response_data["common_structure"]
        if "enum" in response_data:
            state["enum_arr"] = response_data["enum"]
    await ctx.stream_message(f"文件标题分析结束,接口标题列表:{state["interface_name_arr"]},公共结构体标题列表:{state["common_structure_arr"]},枚举标题列表:{state["enum_arr"]}")
    return state


async def extract_parse_content(state: TipParseDocumentState, config: RunnableConfig) -> TipParseDocumentState:
    ctx: InteractionContext = config["configurable"]["ctx"]
    await ctx.stream_message("文档内容提取开始")
    prompt_template = ChatPromptTemplate(
        [
            ("system", extract_exist_headings_prompt)
        ]
    )
    exist_interface_data = get_api_schema_base_info_by_type(state["partner_code"], "interface")
    exist_common_structure_data = get_api_schema_base_info_by_type(state["partner_code"], "common_structure")
    exist_enum_data = get_api_schema_base_info_by_type(state["partner_code"], "enum")
    messages = {
        "exist_interface_name_arr": exist_interface_data,
        "interface_name_arr": state["interface_name_arr"],
        "exist_common_structure_name_arr": exist_common_structure_data,
        "common_structure_name_arr": state["common_structure_arr"],
        "exist_enum_name_arr": exist_enum_data,
        "enum_name_arr": state["enum_arr"],
    }
    llm = get_deep_bank_model_by_name(AgentName.AGENT_TIP.value, ModelType.QWEN3_CODER_480B_A35B.value)
    response = chat_message_template_with_deep_bank_by_llm(prompt_template, messages, AgentName.AGENT_TIP.value, llm)
    response_data = json.loads(extract_json_from_string(response.content))
    if response_data is not None:
        if "interface_name" in response_data:
            state["exist_interface_name_arr"] = response_data["interface_name"]
        if "common_structure" in response_data:
            state["exist_common_structure_arr"] = response_data["common_structure"]
        if "enum" in response_data:
            state["exist_enum_arr"] = response_data["enum"]

    parser = MarkdownParser(state["file_markdown_content"])
    target_headings = state["interface_name_arr"] + state["common_structure_arr"] + state["enum_arr"]
    sections_dict = parser.get_sections_document_blocks_dict(target_headings)
    parse_interface_content_arr = get_content(state["interface_name_arr"], sections_dict)
    parse_common_structure_content_arr = get_content(state["common_structure_arr"], sections_dict)
    parse_enum_content_arr = get_content(state["enum_arr"], sections_dict)
    state["parse_interface_content_arr"] = parse_interface_content_arr
    state["parse_common_structure_content_arr"] = parse_common_structure_content_arr
    state["parse_enum_content_arr"] = parse_enum_content_arr
    await ctx.stream_message("文档内容提取结束")
    return state


def get_content(content_name_arr: list, content_map: dict) -> list:
    content_arr = []
    char_count = 0
    content = ""
    pre_char_count = 0
    pre_content = ""
    for content_name in content_name_arr:
        if char_count > 0:
            pre_char_count = char_count
            pre_content = content
            char_count = 0
            content = ""
        content_detail_arr = content_map[content_name]
        for content_detail in content_detail_arr:
            char_count += content_detail["char_count"]
            content += "\n\n\n" + content_detail["content"]
            if char_count > 2000:
                if pre_char_count > 0:
                    content_arr.append(pre_content)
                    pre_char_count = 0
                    pre_content = ""
                content_arr.append(content)
                char_count = 0
                content = ""

        char_count = pre_char_count + char_count
        content = pre_content + "\n\n\n" + content
        pre_char_count = 0
        pre_content = ""
        if char_count > 2000:
            content_arr.append(content)
            char_count = 0
            content = ""
    if char_count > 0:
        content_arr.append(content)
    return content_arr


async def continue_parse_common_structure_enum(state: TipParseDocumentState, config: RunnableConfig) -> list[Send]:
    ctx: InteractionContext = config["configurable"]["ctx"]
    send_list = [Send("parse_common_structure_and_enum", {
        "file_name": state["file_name"],
        "file_suffix": state["file_suffix"],
        "partner_code": state["partner_code"],
        "partner_desc": state["partner_desc"],
        "exist_common_structure_arr": state["exist_common_structure_arr"],
        "exist_enum_arr": state["exist_enum_arr"],
        "force_update": state["force_update"],
        "content": content,
    }) for content in (state["parse_common_structure_content_arr"] + state["parse_enum_content_arr"])]
    await ctx.stream_message(f"公共结构和枚举解析开始,共{len(send_list)}任务")
    return send_list


async def parse_common_structure_and_enum(state: TipSingleParseState):
    logger.info(
        f"PRO 文档内容公共结构和枚举解析,partnerCode:{state["partner_code"]},file_name:{state["file_name"]},content:{state["content"]}")
    llm = get_deep_bank_model_by_name(AgentName.AGENT_TIP.value, ModelType.QWEN3_CODER_480B_A35B.value)
    prompt_template = ChatPromptTemplate(
        [
            ("system", parse_common_structure_enum_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    messages = {
        "messages": [HumanMessage(content=state["content"])],
        "exist_common_structure_arr": state["exist_common_structure_arr"],
        "exist_enum_arr": state["exist_enum_arr"]
    }
    response = chat_message_template_with_deep_bank_by_llm(prompt_template, messages, AgentName.AGENT_TIP.value, llm)
    response_data = json.loads(extract_json_from_string(response.content))
    interfaces = response_data["interfaces"]
    for interface in interfaces:
        save_schema_info(state["file_name"], state["file_suffix"], state["partner_code"], state["partner_desc"],
                         interface, "interface", state["force_update"])

    common_structures = response_data["common_structures"]
    for common_structure in common_structures:
        save_schema_info(state["file_name"], state["file_suffix"], state["partner_code"], state["partner_desc"],
                         common_structure, "common_structure", state["force_update"])

    enums = response_data["enums"]
    for enum in enums:
        save_schema_info(state["file_name"], state["file_suffix"], state["partner_code"], state["partner_desc"],
                         enum, "enum", state["force_update"])
    return {}


async def get_all_common_struct_enum(state: TipParseDocumentState, config: RunnableConfig) -> TipParseDocumentState:
    ctx: InteractionContext = config["configurable"]["ctx"]
    await ctx.stream_message("公共结构和枚举解析结束")
    all_common_structure_data = get_api_schema_base_info_by_type(state["partner_code"], "common_structure")
    all_enum_data = get_api_schema_base_info_by_type(state["partner_code"], "enum")
    state["all_common_structure_arr"] = all_common_structure_data
    state["all_enum_arr"] = all_enum_data
    return state


async def continue_parse_interface(state: TipParseDocumentState, config: RunnableConfig) -> list[Send]:
    ctx: InteractionContext = config["configurable"]["ctx"]
    send_list = [Send("parse_interface", {
        "file_name": state["file_name"],
        "file_suffix": state["file_suffix"],
        "partner_code": state["partner_code"],
        "partner_desc": state["partner_desc"],
        "force_update": state["force_update"],
        "all_common_structure_arr": state["all_common_structure_arr"],
        "all_enum_arr": state["all_enum_arr"],
        "content": content,
    }) for content in state["parse_interface_content_arr"]]
    await ctx.stream_message(f"接口解析开始,共{len(send_list)}任务")
    return send_list


async def parse_interface(state: TipSingleParseState):
    logger.info(
        f"PRO 文档接口内容解析,partnerCode:{state["partner_code"]},file_name:{state["file_name"]},content:{state["content"]}")
    llm = get_deep_bank_model_by_name(AgentName.AGENT_TIP.value, ModelType.QWEN3_CODER_480B_A35B.value)
    prompt_template = ChatPromptTemplate(
        [
            ("system", parse_interface_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    messages = {
        "messages": [HumanMessage(content=state["content"])],
        "all_common_structure_arr": state["all_common_structure_arr"],
        "all_enum_arr": state["all_enum_arr"],
    }
    response = chat_message_template_with_deep_bank_by_llm(prompt_template, messages, AgentName.AGENT_TIP.value, llm)
    response_data = json.loads(extract_json_from_string(response.content))
    interfaces = response_data["interfaces"]
    for interface in interfaces:
        save_schema_info(state["file_name"], state["file_suffix"], state["partner_code"], state["partner_desc"],
                         interface, "interface", state["force_update"])

    common_structures = response_data["common_structures"]
    for common_structure in common_structures:
        save_schema_info(state["file_name"], state["file_suffix"], state["partner_code"], state["partner_desc"],
                         common_structure, "common_structure", state["force_update"])

    enums = response_data["enums"]
    for enum in enums:
        save_schema_info(state["file_name"], state["file_suffix"], state["partner_code"], state["partner_desc"],
                         enum, "enum", state["force_update"])
    return {}


async def parse_result(state: TipSingleParseState, config: RunnableConfig):
    ctx: InteractionContext = config["configurable"]["ctx"]
    ctx.stream_message(f"接口解析完成")
    return state