import logging
from dataclasses import dataclass
from enum import Enum
from typing import Annotated

from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langgraph.graph import MessagesState
from langgraph.prebuilt import create_react_agent

from app.agent.common.agent import Agent<PERSON>ame
from app.agent.common.llm.deep_bank_model import get_deep_bank_model_by_name, ModelType, \
    chat_message_template_with_deep_bank_by_llm
from app.agent.tip.prompt.tip_report_prompt import general_prompt

logger = logging.getLogger("app")


@dataclass
class ReportAgentState(MessagesState):

    question: Annotated[str, "用户问题"]

    answer: Annotated[str, "本次提问的回答"]

    partner_base_info_list: Annotated[list, "匹配的渠道基本信息"]


@dataclass
class ReportNodeEnum(Enum):
    GENERAL = ("general", "通用", "stream")
    ANALYSIS = ("analysis", "分析", "stream")

    def __init__(self, code, desc, output_mode):
        self.code = code
        self.desc = desc
        self.output_mode = output_mode

    @classmethod
    def get_by_code(cls, code):
        for member in cls:
            if member.code == code:
                return member
        return None

    @classmethod
    def is_stream_mode(cls, code):
        tip_chat_node_enum = cls.get_by_code(code)
        if tip_chat_node_enum:
            return "stream" == tip_chat_node_enum.output_mode
        else:
            return False


async def general(state: ReportAgentState) -> ReportAgentState:
    max_iterations = 5
    recursion_limit = 2 * max_iterations + 1
    messages = {
        "messages": state["messages"],
    }
    tools = []
    llm = get_deep_bank_model_by_name(AgentName.AGENT_TIP.value, ModelType.DEEPSEEK.value)
    agent = create_react_agent(model=llm, prompt=general_prompt, tools=tools)
    agent_with_limit = agent.with_config(recursion_limit= recursion_limit)
    for mode,response in agent_with_limit.astream(messages):
        logger.info(f"report agent mode: {mode}, response: {response}")
    return state