import logging

import requests
import json

from app.utils.apollo_config_utils import get_value

logger = logging.getLogger("app")

def get_api_schema_base_info_by_type(partner_code: str, type: str):
    tip_service_domain = get_value("tip_service_domain")
    tip_service_invoke_token = get_value("tip_service_invoke_token")
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": tip_service_invoke_token,  # 身份验证令牌
        }

        params = {
            "partnerCode": partner_code,
            "type": type
        }
        # 发送 GET 请求
        response = requests.get(tip_service_domain + "/agent/api/partner/schema/base/info/bytype", headers=headers,
                                params=params)

        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data and json_data["code"] == 200:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None


def save_schema_info_invoke(payload: dict):
    tip_service_domain = get_value("tip_service_domain")
    tip_service_invoke_token = get_value("tip_service_invoke_token")
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": tip_service_invoke_token,  # 身份验证令牌
        }
        # 发送 GET 请求
        response = requests.post(tip_service_domain + "/agent/api/partner/schema/save", None, payload, headers=headers)

        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data and json_data["code"] == 200:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            return None
    except requests.exceptions.RequestException as e:
        return None


def save_schema_info(document_name: str, suffix: str, partner_code: str,
                     partner_desc: str, schema_info: dict, type: str, force_update: bool):
    name = schema_info["name"]
    desc = schema_info["desc"]
    if "link_common_structures" in schema_info:
        link_common_structures = schema_info["link_common_structures"]
    else:
        link_common_structures = []

    if "link_enums" in schema_info:
        link_enums = schema_info["link_enums"]
    else:
        link_enums = []
    link_schema_info = {
        "link_common_structures": link_common_structures,
        "link_enums": link_enums
    }
    content = json.dumps(schema_info, ensure_ascii=False)
    link_schema_info_str = json.dumps(link_schema_info, ensure_ascii=False)
    if name and type and partner_code and partner_desc:
        payload = {
            "documentName": document_name,
            "suffix": suffix,
            "partnerCode": partner_code,
            "partnerDesc": partner_desc,
            "name": name,
            "desc": desc,
            "type": type,
            "content": content,
            "linkSchemaInfo": link_schema_info_str,
            "forceUpdate": force_update
        }
        save_schema_info_invoke(payload)


def get_document_content(partner_code: str, document_name: str):
    tip_service_domain = get_value("tip_service_domain")
    tip_service_invoke_token = get_value("tip_service_invoke_token")
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": tip_service_invoke_token,  # 身份验证令牌
        }

        params = {
            "partnerCode": partner_code,
            "documentName": document_name
        }
        # 发送 GET 请求
        response = requests.get(tip_service_domain + "/agent/api/partner/schema/document", params, headers=headers)

        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data and json_data["code"] == 200:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            return None
    except requests.exceptions.RequestException as e:
        return None


def save_document_content(partner_code: str, partner_desc: str, suffix: str, document_name: str, content: str):
    tip_service_domain = get_value("tip_service_domain")
    tip_service_invoke_token = get_value("tip_service_invoke_token")
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": tip_service_invoke_token,  # 身份验证令牌
        }

        payload = {
            "partnerCode": partner_code,
            "partnerDesc": partner_desc,
            "suffix": suffix,
            "documentName": document_name,
            "content": content
        }
        # 发送 GET 请求
        response = requests.post(tip_service_domain + "/agent/api/partner/schema/save/document", None, payload, headers=headers)

        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data and json_data["code"] == 200:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            return None
    except requests.exceptions.RequestException as e:
        return None
