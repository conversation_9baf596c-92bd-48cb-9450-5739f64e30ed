# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/6/18 16:51
# <AUTHOR> 罗君辉
# @File : agent_chat_dialog_msg.py
# @project : iops
# @describe : Agent对话消息维护路由
from fastapi import APIRouter
from app.handlers.agent_chat_dialog_handler import (
    generate_dialog_session,
    query_dialog_session,
    update_dialog_session_name,
    generate_msg_no,
    save_dialog_msg,
    query_dialog_msg,
    query_dialog_msg_data,
    save_dialog_msg_critical_data,
    query_dialog_msg_critical_data,
    delete_dialog,
    update_msg_card_data
)

agent_chat_dialog_router = APIRouter(prefix="/iopsbi/chat/dialog", tags=["items"])

agent_chat_dialog_router.add_api_route(
    path="/generate_dialog_session",
    endpoint=generate_dialog_session,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/query_dialog_session",
    endpoint=query_dialog_session,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/update_dialog_session_name",
    endpoint=update_dialog_session_name,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/generate_msg_no",
    endpoint=generate_msg_no,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/save_dialog_msg",
    endpoint=save_dialog_msg,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/query_dialog_msg",
    endpoint=query_dialog_msg,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/query_dialog_msg_data",
    endpoint=query_dialog_msg_data,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/save_dialog_msg_critical_data",
    endpoint=save_dialog_msg_critical_data,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/query_dialog_msg_critical_data",
    endpoint=query_dialog_msg_critical_data,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/delete_dialog",
    endpoint=delete_dialog,
    methods=["POST"]
)

agent_chat_dialog_router.add_api_route(
    path="/update_msg_card_data",
    endpoint=update_msg_card_data,
    methods=["POST"]
)
