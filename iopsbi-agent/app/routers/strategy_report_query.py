# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/5/18 16:51
# <AUTHOR> 罗君辉
# @File : strategy_report_query.py
# @project : iops
# @describe : 策略报告查询接口定义

from fastapi import APIRouter
from app.handlers.strategy_report_query_handler import (
    query_crowd_index_sql,
    statistics_crowd_index_info,
    preview_crowd_count,
    tree_optimize
)

srq_router = APIRouter(prefix="/iopsbi/srq", tags=["items"])

srq_router.add_api_route(
    path="/query_crowd_index_sql",
    endpoint=query_crowd_index_sql,
    methods=["POST"]
)

srq_router.add_api_route(
    path="/statistics_crowd_index_info",
    endpoint=statistics_crowd_index_info,
    methods=["POST"]
)

srq_router.add_api_route(
    path="/preview_crowd_count",
    endpoint=preview_crowd_count,
    methods=["POST"]
)

srq_router.add_api_route(
    path="/tree_optimize",
    endpoint=tree_optimize,
    methods=["POST"]
)



