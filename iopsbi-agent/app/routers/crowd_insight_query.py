# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/6/18 15:34
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : crowd_insight_query.py
# @project : iopsbi-agent
from fastapi import APIRouter

from app.handlers.ai_bucket_rec_handler import ai_bucket_rec_query, ai_bucket_rec_callback
from app.handlers.crowd_insight_handler import (
    crowd_insight_index_calcu,
    query_usable_indic_list
)

cih_router = APIRouter(prefix="/iopsbi/cih", tags=["items"])

cih_router.add_api_route(
    path="/crowd_insight_index_calcu",
    endpoint=crowd_insight_index_calcu,
    methods=["POST"]
)

cih_router.add_api_route(
    path="/query_usable_indic",
    endpoint=query_usable_indic_list,
    methods=["POST"]
)

cih_router.add_api_route(
    path="/dimension_ai_rec",
    endpoint=ai_bucket_rec_query,
    methods=["POST"]
)


cih_router.add_api_route(
    path="/dimension_ai_callback",
    endpoint=ai_bucket_rec_callback,
    methods=["POST"]
)