# !/opt/homebrew/bin/python3
# -*- coding:utf-8 -*-
# @Time : 2025/5/26 16:07
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : label_relation_query_handler.py
# @project : iopsbi-agent
from fastapi import APIRouter
from app.handlers.label_relation_query_handler import (
    query_relation_labels,
    query_relation_labels_stat_info
)

qrl_router = APIRouter(prefix="/iopsbi/qrl", tags=["items"])

qrl_router.add_api_route(
    path="/query_relation_labels",
    endpoint=query_relation_labels,
    methods=["POST"]
)

qrl_router.add_api_route(
    path="/query_stats_info",
    endpoint=query_relation_labels_stat_info,
    methods=["POST"]
)
