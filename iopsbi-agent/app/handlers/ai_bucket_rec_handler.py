import json
import logging

import requests
from datetime import datetime, timedelta
from app.handlers.agent_chat_dialog_handler import save_msg_critical_data_temp, query_msg_critical_data_temp, \
	safe_json_dumps, update_msg_card_data, delete_msg_critical_data_temp
from app.schemas.Items import AiBucketRecReq, DialogMsgTmpDataQryReq, CommonResp, AiBucketRecRes, DialogMsgDataUpdateReq

logger = logging.getLogger("app")


_operate_convert_map = {
	"eq": "=",
	"gt": ">",
	"lt": "<",
	"ge": ">=",
	"le": "<=",
	"ne": "!=",
	"=": "=",
	">": ">",
	"<": "<",
	">=": ">=",
	"<=": "<=",
}

def ai_bucket_rec_query(req: AiBucketRecReq):
	"""
		Handles the AI bucket recommendation logic.
	"""
	logger.info(f"Executing AI bucket recommendation handler, req={json.dumps(req.__dict__, ensure_ascii=False)}")
	common_resp = CommonResp()
	# save tmp msg
	try:
		save_msg_critical_data_temp(DialogMsgTmpDataQryReq(session_no=req.conversation_id, msg_no=req.msg_no, content=safe_json_dumps(req.__dict__)))
		# 调用AI推荐接口
		payload = _invoke_ai_bucket_rec(req.observationIndex, f"{req.conversation_id}:{req.msg_no}",req.sql)
		print(json.dumps(payload, ensure_ascii=False))
		#发送post请求
		url = "http://10.236.19.16:8000/bucket/task"
		response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'}, timeout=10)
		if response.status_code != 200:
			logger.error(f"ai推荐接口请求失败，状态码: {response.status_code}, 响应内容: {response.text} , url: {url}, 请求体: {json.dumps(payload, ensure_ascii=False)}")
			raise Exception(f"智能体查询ai推荐异常，HTTP状态码: {response.status_code}")
		resp_json = response.json()
		if resp_json.get("code") != 200:
			logger.error(f"ai推荐接口请求失败，响应内容: {resp_json} , url: {url}, 请求体: {json.dumps(payload, ensure_ascii=False)}")
			raise Exception(f"智能体查询ai推荐异常，响应内容: {resp_json}")
		common_resp.success_resp(True)
	except Exception as e:
		logger.error(f"调用ai推荐接口失败, 错误={str(e)}")
		#delete_msg_critical_data_temp(DialogMsgTmpDataQryReq(session_no=req.conversation_id, msg_no=req.msg_no))
		common_resp.fail_resp("500", str(e))
	return common_resp


def ai_bucket_rec_callback(req: AiBucketRecRes):
	logger.info(f"Executing AI bucket recommendation callback, req={json.dumps(req.__dict__, ensure_ascii=False)}")
	common_resp = CommonResp()
	# save tmp msg
	try:
		#handle callback data
		session_no = req.req_no.split(":")[0]
		msg_no = req.req_no.split(":")[1]
		data = query_msg_critical_data_temp(DialogMsgTmpDataQryReq(session_no = session_no, msg_no=msg_no))
		logger.info(f"获取到临时消息, data={data}")
		message = data.get(msg_no)
		if message:
			labels = message['observationIndex']['labelConfig']
			for item in req.data:
				dim_name = item["dimension_name"]
				# 匹配labels中的labelKey或labelName
				for label in labels:
					if label.get("labelKey") == dim_name:
						# 更新label内容，例如更新thresholds和values
						if item['is_behavior']:# 更新行为标签的thresholds和values
							childs = label.get("child", [])
							for child in childs:
								if child.get("labelKey") == item["metric_name"]:
									child["labelValue"] = [f"0-{item['thresholds'][0]}", f"{item['thresholds'][0]}-10000000"] if len(item["thresholds"]) == 1 else [f"{start}-{end}" for start, end in zip([0] + item["thresholds"], item["thresholds"] + [10000000])]
									label["graph"] = item["values"]
									label["points"]=item['thresholds']
						else: # 更新属性标签的thresholds和values
							label["labelValue"] = [f"0-{item['thresholds'][0]}", f"{item['thresholds'][0]}-10000000"] if len(item["thresholds"]) == 1 else [f"{start}-{end}" for start, end in zip([0] + item["thresholds"], item["thresholds"] + [10000000])]
							label["graph"] = item["values"]
							label["points"] = item['thresholds']
		else:
			common_resp.fail_resp("500","获取临时消息不存在")
		# 更新对话消息
		update_msg_card_data(DialogMsgDataUpdateReq(session_no = session_no, msg_no=msg_no,msg_data = json.dumps(message, ensure_ascii=False)))
		# 删除临时消息
		delete_msg_critical_data_temp(DialogMsgTmpDataQryReq(session_no = session_no, msg_no=msg_no))
		common_resp.success_resp(True)
	except Exception as e:
		logger.error(f"调用ai推荐接口失败, 错误={str(e)}")
		common_resp.fail_resp(False)
	return common_resp



def _invoke_ai_bucket_rec(observationIndex: dict,req_no: str,sql:str) -> dict:
	#获取圈人群sql
	logger.info(f"Invoking AI bucket recommendation with request: {json.dumps(observationIndex, ensure_ascii=False)}")
	payload = {"req_no":req_no}
	start_date = datetime.strptime(observationIndex["timeDimensionStart"], "%Y-%m-%d")
	end_date = datetime.strptime(observationIndex["timeDimensionEnd"], "%Y-%m-%d")
	date_list = [(start_date + timedelta(days=i)).strftime("%Y-%m-%d") for i in range((end_date - start_date).days + 1)]
	data = {"crowd_constraint" : sql, "time_dimensions": date_list}
	dimensions = []
	label_configs = observationIndex.get("labelConfig", [])
	for label in label_configs:
		item = {}
		if not label.get("isBehavior", False):
			# 处理属性标签的逻辑
			item["dimension_name"] = label.get("labelKey")
			item["variable_name"] = label.get("labelKey")
			item["is_behavior"] = False
			item["variable_type"] = "double"
			item["ai_recommend"] = label.get("isAiSplit", False)
			item["threshold"] = ""
			item["constraints"] = []
		else:
			# 处理行为标签的逻辑
			item["is_behavior"] = True
			item["dimension_name"] = label.get("labelKey")
			item["variable_type"] = "array"
			item["threshold"] = ""
			childs = label.get("child", [])
			conditions = []
			for child in childs:
				if child.get("isAiSplit",False):
					item["ai_recommend"] = True
					item["variable_name"] = child.get("labelKey")
				else:
					conditions.append({
						"name": child.get("labelKey"),
						"relation": _convert_operate_and_value(child)["operate"],
						"value": _convert_operate_and_value(child)["label_value"],
					})
			if conditions:
				item["constraints"] = conditions
		if item.get("ai_recommend",False):
			dimensions.append(item)
		data["dimensions"] = dimensions
		payload["data"] = data
	logger.info(f"调用ai推荐纬度请求参数:{json.dumps(payload, ensure_ascii=False)}")
	return payload


def _convert_operate_and_value(child):
	operate = child.get("operate")
	label_value = child.get("labelValue")
	operate = _operate_convert_map.get(operate, operate) # 转换操作符
	if operate in ["=", "in"]:
		if isinstance(label_value, str) and "," in label_value:
			return {"operate": "in", "label_value": [v.strip() for v in label_value.split(",")]}
		if isinstance(label_value, list) and len(label_value) > 1:
			return {"operate": "in", "label_value": label_value}
	return {"operate": operate, "label_value": label_value}

if __name__ == "__main__":
	data = {
		"req_no": "w22222:test1111",
		"task_id": "1232132312",
		"data": [
			{
				"dimension_name": "vip_customer_assist_tag",
				"is_behavior": False,
				"metric_name": "vip_customer_assist_tag",
				"thresholds": [27],
				"values": [1, 2, 6, 10, 15, 27, 60, 80, 88, 100]
			},
			{
				"dimension_name": "jr_rfs_model_io_info",
				"is_behavior": True,
				"metric_name": "score",
				"thresholds": [1000,5000,6000],
				"values": [2000, 3000, 4000, 5000, 10000, 110000]
			}
		]
	}
	ai_bucket_rec_callback(AiBucketRecRes(**data))