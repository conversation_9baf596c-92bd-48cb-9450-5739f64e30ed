# 使用官方 Python 镜像作为基础镜像
#FROM python:3.9-slim
#FROM harbor.qihoo.net/finloan-infra-library/python:3.11-bookworm-202408071115
FROM harbor.qihoo.net/finloan-infra-library/framework-python3.12-bw:250630111254819-a721a60
# 设置工作目录
RUN mkdir -p /home/<USER>/iopsbi-agent/code /home/<USER>/iopsbi-agent/logs
WORKDIR /home/<USER>/iopsbi-agent
COPY ./iopsbi-agent /home/<USER>/iopsbi-agent/code
#COPY ./py_com /home/<USER>/iopsbi-agent/py_com
# 指定容器时区
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone

RUN pip3 config set global.index-url http://nexus3.daikuan.qihoo.net/nexus/repository/python-public/simple \
    && pip3 config set global.trusted-host nexus3.daikuan.qihoo.net \
    && pip3 install -r /home/<USER>/iopsbi-agent/code/requirements.txt
#CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
RUN chmod 755 /home/<USER>/iopsbi-agent/code/start.sh
ENTRYPOINT ["/home/<USER>/iopsbi-agent/code/start.sh"]