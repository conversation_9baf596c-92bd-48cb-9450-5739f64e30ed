haier_document = '''
# API 业务接口文档  

# 目录  

# 目录  

修订记录. 4  
1 文档说明. 5  
1.1 编写目的. 5  
1.2 阅读对象. 5  
1.3 术语.. 5  
1.4 环境信息. .6  
1.5 接入必要信息 6  

# 2 报文与加密签名. 8  

2.1 密钥颁发. .8  
2.2 签名规则. .8  
3 接口定义. .9  
3.0 交互流程. ..9  
3.1 授信流程(加解密一).... ..9  
3.2 用信流程(加解密一).. .19  
3.3 代扣流程(加解密二).. .25  
3.4 还款流程(加解密三). .29  
3.5 通用接口(加解密三). .48  
4 文件.. .68  
放款对账. .68  
还款计划.. ..68  
还款记录. .68  
买断文件. .68  
5 返回码描述.. ..69  
5.1 授信&支用流程. 69  

# 修订记录  

<html><body><table><tr><td>文档版本</td><td>修订内容</td><td>修订人</td><td>修订时间</td></tr><tr><td>V2.0</td><td>创建文档</td><td>林鹏</td><td>2023-06-19</td></tr><tr><td>V2.0.1</td><td>增加授信流程和支用流程的返回码描述</td><td>杨承伟</td><td>2023-07-06</td></tr></table></body></html>  

# 1 文档说明  

# 1.1 编写目的  

本文档描述了海尔给第三方的交互协议，系统开发人员需要严格遵守本协议进行开发，完成对接工作。  

# 1.2 阅读对象  

本文档的阅读对象为：  

第三方开发人员。  
海尔开发人员。  

# 1.3 术语  

<html><body><table><tr><td>名称</td><td>说明</td></tr><tr><td>MD5</td><td>即Message-DigestAlgorithm5（信息-摘要算法5），用于确保信息传输完 整一致，是计算机广泛使用的杂凑算法之一（又译摘要算法、哈希算法）。如 果只有发起者和接收者知道MD5密钥，那么这就对两者间发送的数据提供了身 份验证和完整性保证。</td></tr><tr><td>RSA</td><td>RSA公开密钥密码体制。所谓的公开密钥密码体制就是使用不同的加密密钥与 解密密钥，是一种“由已知加密密钥推导出解密密钥在计算上是不可行的”密 码体制。</td></tr></table></body></html>  

# 1.4 环境信息  

# 1.4.1 调用接口地址  

# 1.5 接入必要信息  

# 1.5.1 接入准备  

海尔和海尔都需要生成一对RSA 公私钥，并将公钥提供对方。  
生成密钥时，密钥长度为512 位。  

# 1.5.2 请求过程  

请求海尔的报文http header 请求头如下：content-type 为 application/json;charset=utf-8。X-Signature   签名字段。  

请求海尔的报文http body 如下所示： {"applyNo":"*********0","tradeCode":"LP4002101","data":"J 业务报文"}  

# 具体过程：  

1. 海尔请求海尔时：海尔使用海尔的公钥对的业务报文做 gzip 压缩再加密(data 字  
段，其它的字段不处理)，将压缩且加密后的字符串填充到data 字段中。用海尔的私钥对  
整个 body 计算签名，填充到请求头的 X-Signature 中。2. 将上述报文组装成示例的json 格式后，上送海尔。3. 海尔网关收到请求，先根据 applyNo 里的流水号，判断是否重复交易，如重复交  
易，将返回异常。再使用海尔的公钥对整报文进行验签，如计算的签名与 X-Signature  
上送的签名一致，则通过，并作后续处理。再使用海尔私钥，对data 字段内容进行解密、  
解压，得到原始报文。4. 海尔网关处理通过后，转发下游系统处理。下游系统处理完成后返回海尔网关。海  
尔网关使用海尔的公钥对的业务报文做 gzip 压缩再加密(data 字段，其它的字段不处理)，  
将压缩且加密后的字符串填充到 data 字段中。用海尔的私钥对整个 body 计算签名，填  
充到请求头的X-Signature 中。最终按上述示例字段格式返回海尔。5. 海尔收到返回报文后，用海尔的公钥验签，如整个 body 计算的签名与 X-  
Signature 上送的签名一致，则通过，并作后续业务处理。再用海尔的私钥对 body 中  

data 字段内容进行解压解密得到原始返回报文。  

# 2 报文与加密签名  

# 2.1 密钥颁发  

RSA 密钥由请求发送方颁发，请求发送方保留私钥，提供公钥给请求接收方。  
海尔也提供一对RSA 密钥，海尔保留私钥，提供公钥给合作方。  

# 2.2 签名规则  

见海尔demo  

# 3 接口定义  

# 3.0 交互流程  

流程-授信  

接口名 文档名.接口 数据流转  
客户撞库接口 --> LP40029 海尔 -> 360  
ocr，人脸 --> sftp 海尔 -> 360  
授信审核申请接口    --> 3.1.1 海尔 -> 360  
授信结果通知接口    --> 3.1.2 360 -> 海尔  
授信结果查询接口    --> 3.1.3 海尔 -> 360  
额度/贷款审批状态推送   --> LP40008  海尔 -> 360（不接）  
授信最终审批状态查询   --> LP10010  360 -> 海尔 （不接）  
注：授信终态失败，360 必须处理成终态失败  

# 流程-支用  

支用审核申请接口    --> 3.2.1 海尔 -> 360支用结果通知接口    --> 3.2.2 360 -> 海尔支用结果查询接口    --> 3.2.3 海尔 -> 360额度/贷款审批状态推送   --> LP40008  海尔 -> 360贷款最终审批状态查询   --> LP20005  360 -> 海尔  

# 流程-还款  

全量还款结果推送  --> 还款通知   海尔 -> 360代扣申请    --> 3.3.1 代扣申请 海尔 -> 360代扣查询 --> 3.3.2 代扣查询 海尔 -> 360代扣回调 --> 3.3.3 代扣查询 360 -> 海尔主动还款    --> LP30043  360 -> 海尔主动还款    --> LP30041  360 -> 海尔主动还款    --> LP30042  360 -> 海尔  

# 3.1 授信流程(加解密一)  

# 3.1.1 授信审核申请接口  

接口说明：用户在海尔填写资料之后，提交给融担方进行审核，接口由融担方实现，海尔  

调用。  

请求报文  


<html><body><table><tr><td colspan="5"></td></tr><tr><td colspan="6">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>7.10中的接口号</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>是</td><td>本接口传01</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>是</td><td>融担机构在海尔的定义</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>标识</td><td>flag</td><td>String</td><td>是</td><td>0、额度申请</td></tr><tr><td>用户Id</td><td>userld</td><td>String</td><td>否</td><td>2、额度信息修改 不用填</td></tr><tr><td>额度申请流水号</td><td>appISeq</td><td>String</td><td>是</td><td>保证唯一</td></tr><tr><td>姓名</td><td>apptCustName</td><td>String</td><td>是</td><td>申请人本人姓名</td></tr><tr><td>身份证号</td><td>apptIdNo</td><td>String</td><td>是</td><td>申请人本人身份证号</td></tr><tr><td>手机号</td><td>indivMobile</td><td>String</td><td>是</td><td>申请人本人手机号</td></tr><tr><td>现居住省</td><td>liveProvince</td><td>String</td><td>否</td><td>按照海尔code码上传</td></tr><tr><td>现居住市</td><td>liveCity</td><td>String</td><td>否</td><td>按照海尔code码上传</td></tr><tr><td>现居地址区</td><td>liveArea</td><td>String</td><td>否</td><td>按照海尔code码上传</td></tr><tr><td>现居地址</td><td>liveAddr</td><td>String</td><td>否</td><td>详细地址</td></tr><tr><td>个人邮箱</td><td>apptEmail</td><td>String</td><td>否</td><td></td></tr><tr><td>学历</td><td>educationLevel</td><td>String</td><td>否</td><td></td></tr><tr><td>婚姻状况</td><td>maritalStatus</td><td>String</td><td>否</td><td></td></tr><tr><td>工作信息</td><td>jobInfo</td><td>Object</td><td>否</td><td>详情参考joblnfo描述</td></tr><tr><td>银行卡信息</td><td>bankCardInfo</td><td>Object</td><td>否</td><td>详情参考bankCardlnfo 描述</td></tr><tr><td>联系人信息</td><td>contactList</td><td>List</td><td>否</td><td>详情参考contactList描述</td></tr><tr><td>活体信息</td><td>livingInfo</td><td>Object</td><td>否</td><td>详情参考 livinglnfo 描述</td></tr><tr><td>身份证OCR信息</td><td>idlnfo</td><td>Object</td><td>是</td><td>详情参考idlnfo 描述</td></tr><tr><td>风控附加信息</td><td>riskExtInfo</td><td>Map<String,Obje</td><td>否</td><td>用于双方沟通的风控附加信息，详情</td></tr><tr><td>contactList描述</td><td></td><td>ctv</td><td></td><td>参考 riskExtInfo</td></tr><tr><td colspan="5"></td></tr><tr><td>联系人姓名</td><td>relName</td><td>String</td><td>否</td><td></td></tr><tr><td>与申请人关系</td><td>relRelation</td><td>String</td><td>否</td><td>01 父母；02 子女及兄弟姐妹；06 夫 妻；07本人；99其他</td></tr><tr><td>联系人电话</td><td>relMobile</td><td>String</td><td>否</td><td></td></tr><tr><td>所在单位</td><td>relEmpName</td><td>String</td><td>否</td><td></td></tr></table></body></html>  

<html><body><table><tr><td colspan="6"></td></tr><tr><td>联系人居住地址</td><td>relAddr</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td colspan="6">bankCardinfo描述</td></tr><tr><td>银行卡类型</td><td>bankCardType</td><td>String</td><td></td><td>否</td><td>1：储蓄卡</td></tr><tr><td>银行名称</td><td>bankName</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td>银行卡号</td><td>bankCardNo</td><td>String</td><td>香</td><td></td><td></td></tr><tr><td>银行卡姓名</td><td>bankCardName bankCardBindingM</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td>银行卡预留手机号</td><td>obileNo</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td colspan="6"></td></tr><tr><td>joblnfo描述 工作单位</td><td>workUnits</td><td>String</td><td></td><td>香</td><td></td></tr><tr><td>工作电话</td><td>workPhone</td><td>String</td><td></td><td>否</td><td></td></tr><tr><td>职业</td><td>apptProfessional</td><td>String</td><td></td><td>否</td><td></td></tr><tr><td>月收入</td><td>monthlylncome</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td colspan="6">活体信息描述</td></tr><tr><td>图片类型</td><td> imageType</td><td>String</td><td></td><td>否</td><td></td></tr><tr><td>活体正面照</td><td>livinglmageOne</td><td>String</td><td></td><td>香</td><td></td></tr><tr><td>动作照1 动作照2</td><td>livinglmageTwo</td><td>String String</td><td>否</td><td>香</td><td></td></tr><tr><td>动作照3</td><td>livinglmageThree</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td>环境照</td><td>livinglmageFour</td><td>String</td><td>否</td><td></td><td></td></tr><tr><td></td><td>livinglmageFive</td><td>List<faceRecolnf</td><td></td><td></td><td></td></tr><tr><td>人脸分数数组</td><td>faceRecolnfoList</td><td>o></td><td>是</td><td></td><td>详情参考faceRecolnfo描述</td></tr><tr><td colspan="6">faceRecolnfo描述</td></tr><tr><td>活体识别渠道</td><td>faceCheckChannel</td><td>String</td><td></td><td>是 政通</td><td>01：爰金03、06：旷视02:国</td></tr><tr><td>活体识别类型</td><td>faceCheckType</td><td>String</td><td></td><td>是</td><td>01：有源；02：无源</td></tr><tr><td>活体识别分数</td><td>faceCheckScore</td><td>String</td><td></td><td>是</td><td>0-100的小数，精确到小数点 后两位 03\06 传此字段，传的数据格式为</td></tr><tr><td>活体置信度阈值</td><td>faceCheckThreshol ds</td><td>String</td><td></td><td>否 }</td><td>{ "le-3":62.168713, "le-5":74.39926, "le-4":69.31534, "1e-6":78.038055 01、02不传此字段</td></tr><tr><td colspan="6">Idlnfo描述</td></tr><tr><td>姓名</td><td>name</td><td>String</td><td>是</td><td></td><td></td></tr><tr><td>性别</td><td>sex</td><td>String</td><td>是</td><td></td><td></td></tr><tr><td>民族</td><td>nation</td><td>String</td><td>是</td><td></td><td></td></tr><tr><td>身份证号</td><td>idNo</td><td>String</td><td>是</td><td></td><td></td></tr><tr><td>户口所在地</td><td>address</td><td>String</td><td>是</td><td></td><td></td></tr><tr><td>签发机关</td><td>issueAgency</td><td>String</td><td>是</td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td>签发日</td><td>issueDate</td><td>String</td><td>是</td><td>格式：yyyymmdd，长期则传 长期</td></tr><tr><td>到期日</td><td>expireDate</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">riskExtlnfo描述</td></tr><tr><td>客户标签</td><td>customerLabel</td><td>String</td><td>否</td><td>有值是存量捞回</td></tr><tr><td>建议额度</td><td>suggestedLimit</td><td>String</td><td>否</td><td></td></tr><tr><td>预留字段2</td><td>reservedField2</td><td>String</td><td>否</td><td></td></tr><tr><td>预留字段3</td><td>reservedField3</td><td>String</td><td>否</td><td></td></tr></table></body></html>  

返回报文：  


<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>额度申请 流水号</td><td>applSeq</td><td>String</td><td>是</td><td>海尔的额度申请流水号</td></tr><tr><td>融担方授 信申请流 水号</td><td>guaranteeApplSeq</td><td>String</td><td>是</td><td>融担方的额度申请流水号，确保 唯一</td></tr><tr><td>返回码</td><td>returnCode</td><td>String</td><td>是</td><td></td></tr><tr><td>返回码描 述</td><td>returnDes</td><td>String</td><td>是</td><td></td></tr></table></body></html>  

# 报文样例  

# 请求报文  

{   
"head": { "cooprCode": "ORG", "tradeTime": "10:47:12", "sysFlag": "11", "channelNo": "A1", "serno": "ef53b8ca60c54f41b08aad9c71f3bab2", "tradeCode": "01", "tradeDate": "2022-09-23", "tradeType": "01"   
},   
"body": { "speSeq": "50000001", "applSeq": "98760987",  

"flag": "0", "creditTyp": "01", "liveAddr": "营盘镇大田村", "liveProvince": "530000", "userId": "C202209230000281700000", "liveArea": "530921", "indivMobile": "18800000148", "liveCity": "530900", "livingInfo": { "faceRecoInfoList": [ { "faceCheckType": "01", "faceCheckThresholds":  "{\"1e-6\":78.038055,\"1e-5\":74.39926,\"1e4\":69.31534,\"1e-3\":62.168713}", "faceCheckChannel": "06", "faceCheckScore": "92.72" } ] }, "apptCustName": "张三", "apptIdNo": "533500000009062000" } } 响应报文 { "head": { "retMsg": "处理成功", "retFlag": "00000" }, "body": { "guaranteeApplSeq": "77000038", "returnCode": "00000", "returnDes": "申请成功", "applSeq": "98760987" } }  

# 3.1.2 授信结果通知接口  

接口说明：接口由海尔实现，融担方调用。融担方在完成额度申请审核后，需调用该接口通知海尔审核结果。  

接口地址：由外联提供提供给三方  

# 请求数据样例:  

{"serno":"*********0","tradeCode":"XXX","channelNo":"","data":"J  

# son 加密内容"}  

# 请求报文格式：  

<html><body><table><tr><td colspan="5">请求参数</td></tr><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32 位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定传：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>是</td><td>本接口传01</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>否</td><td></td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>7.10中的接口号</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>是</td><td>融担机构在海尔的定 义</td></tr><tr><td colspan="5"></td></tr><tr><td>body 参数名 applyResult</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td></td><td>String</td><td>是</td><td>授信申请结果</td><td>-1:无记录 0：授信中 1:授信通过 2:授信拒绝 3:额度失效</td></tr><tr><td>applSeq</td><td>String</td><td>是</td><td>海尔额度申请流水号</td><td></td></tr><tr><td>guarantee AppISeq</td><td>string</td><td>香</td><td>融担方额度申请流水</td><td></td></tr><tr><td>creditTime</td><td>String</td><td>是</td><td>号 授信时间</td><td>格式 yyyyMMddHHmms</td></tr><tr><td>accountStatus</td><td>String</td><td>否</td><td>账户状态 授信通过时必填</td><td>s "1":活动 "2":注销 "3":锁定 "4":冻结</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>"5":已关闭</td></tr><tr><td>creditLimit</td><td>decimal(11,2)</td><td>否</td><td>授信额度 授信通过必填</td><td>5000，单位：元</td></tr><tr><td>limitType</td><td>String</td><td>否</td><td>额度类型 授信通过必填</td><td>CIRCLE:循环额度 UNCIRCLE:非循环额 度 SINGLE_CIRCLE:单 次循环额度</td></tr><tr><td>termPriceList</td><td>String</td><td>香</td><td>期次定价列表</td><td>默认返回CIRCLE start</td></tr><tr><td>term</td><td>int</td><td>否</td><td>期限，授信通过时必 填</td><td>期次，如 3,6,9,12</td></tr><tr><td>approvePrice</td><td>decimal(11,6)</td><td>否</td><td>审批定价，授信通过 时非必填</td><td>年化利率,如0.24; 如返回，也不进行使</td></tr><tr><td>guaranteeFeeRate</td><td>decimal(11,6)</td><td>否</td><td>融担费率</td><td>用 默认返回0</td></tr><tr><td>termPriceList</td><td>String</td><td>否</td><td>期次定价列表</td><td>end</td></tr><tr><td>refuseCode</td><td>String</td><td>授信拒绝2必填</td><td>拒绝原因码</td><td></td></tr><tr><td>refuseMessage</td><td>String</td><td>授信拒绝2必填</td><td>拒绝原因描述</td><td></td></tr></table></body></html>  

返回报文格式：  


<html><body><table><tr><td colspan="5">响应参数</td></tr><tr><td colspan="5">Head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>返回码</td><td>returnCode</td><td>String</td><td>是</td><td></td></tr><tr><td>返回码描述</td><td>returnDes</td><td>String</td><td>是</td><td></td></tr></table></body></html>  

# 报文样例  

# 请求报文  

{   
"head": { "cooprCode": "ORG", "tradeTime": "11:02:17", "sysFlag": "11", "serno": "142022ef392d4dd295681b09008f6070", "tradeCode": "03", "tradeDate": "2022-09-22", "tradeType": "01"   
}, "body": { "accountStatus": "1", "guaranteeApplSeq": "********", "applSeq": "********", "remainLimit": "9000.00", "applyResult": "1", "termPriceList": [ { "term": 6, "approvePrice": "0.24", "guaranteeFeeRate": "0" }, { "term": 9, "approvePrice": "0.24", "guaranteeFeeRate": "0" }, { "term": 12, "approvePrice": "0.24", "guaranteeFeeRate": "0" } ], "creditLimit": "9000.00", "creditTime": "2022-09-22 11:02:17", "limitType": "CIRCLE"   
}   
}   
响应报文   
{ "head": { "retMsg": "success", "retFlag": "00000" }, "body": { "returnCode": "00000", "returnDes": "success"   
}   
}  

# 3.1.3 授信结果查询接口  

接口说明：由融担方实现，海尔调用，用于十分钟后没有返回授信结果时的查询接口  

接口地址：三方定义  

请求样例：  

请求报文：  


<html><body><table><tr><td colspan="5">请求参数</td></tr><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>说明</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>否</td><td></td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>是</td><td>本接口传01</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>7.10中的接口号</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>是</td><td>融担机构在海尔的定义</td></tr><tr><td colspan="5"></td></tr><tr><td>body 参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>说明</td></tr><tr><td>海尔额度申请流 水号</td><td>applSeq</td><td>String</td><td>是</td><td></td></tr><tr><td>融担方额度申请guaranteeApplS 流水号</td><td>eq</td><td>String</td><td>否</td><td></td></tr></table></body></html>  

响应报文：  


<html><body><table><tr><td colspan="5">响应参数</td></tr><tr><td colspan="5">Head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr></table></body></html>  

<html><body><table><tr><td colspan="5">Body</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>returnCode</td><td>String</td><td>是</td><td>返回码</td><td></td></tr><tr><td>returnDes</td><td>String</td><td>是</td><td>返回码描述</td><td></td></tr><tr><td>applyResult</td><td>String</td><td>是</td><td>授信申请结果</td><td>-1:无记录 0:授信中 1:授信通过</td></tr><tr><td>applSeq</td><td>String</td><td>是</td><td>海尔额度申请流水号</td><td>2:授信拒绝</td></tr><tr><td>creditTime</td><td>String</td><td>是</td><td>授信时间</td><td>格式 yyyyMMddHHmms S</td></tr><tr><td>accountStatus</td><td>String</td><td>否</td><td>账户状态 授信通过时必填</td><td>"1":活动 "2":注销 "3":锁定 "4":冻结 "5":已关闭 除了活动状态外，其</td></tr><tr><td>creditLimit</td><td>decimal(11,2)</td><td>否</td><td>授信额度 授信通过必填</td><td>他状态无法借款 5000，单位：元</td></tr><tr><td>limitType</td><td>String</td><td>否</td><td>额度类型 授信通过必填</td><td>CIRCLE:循环额度 UNCIRCLE:非循环额 度 SINGLE_CIRCLE:单 次循环额度</td></tr><tr><td>termPriceList</td><td>String</td><td>香</td><td>期次定价列表</td><td>默认为CIRCLE start</td></tr><tr><td>term</td><td>int</td><td>否</td><td>期限，授信通过时必</td><td>期次，如</td></tr><tr><td>guaranteeFeeRate</td><td>decimal(11,6)</td><td></td><td>填 融担费率</td><td>3,6,9,12 默认返回@</td></tr><tr><td>approvePrice</td><td>decimal(11,6)</td><td>香</td><td>审批定价，授信通过</td><td>年化利率，如0.24</td></tr><tr><td>termPriceList</td><td>String</td><td>否</td><td>时必填 期次定价列表</td><td>end</td></tr><tr><td>refuseCode</td><td>String</td><td>授信拒绝2必填</td><td>拒绝原因码</td><td></td></tr><tr><td>refuseMessage</td><td>String</td><td>授信拒绝2必填</td><td>拒绝原因描述</td><td></td></tr></table></body></html>  

# 报文样例  

# 请求报文  

{   
"head": { "cooprCode": "ORG", "tradeTime": "10:42:01",  

"channelNo": "42", "sysFlag": "11", "serno": "39fa3d6270e84aad9f991b80f6e62c7a", "tradeCode": "02", "tradeDate": "2022-09-23", "tradeType": "01" }, "body": { "guaranteeApplSeq": "77000008", "applSeq": "77000008" } } 响应报文 { "head": { "retMsg": "处理成功", "retFlag": "00000" }, "body": { "returnCode": "00000", "returnDes": "处理成功", "applSeq": "77000008", "remainLimit": "0.00", "applyResult": "0", "creditLimit": "0.00", "creditTime": "2022-09-23 10:42:02" } }  

# 3.2 用信流程(加解密一)  

# 3.2.1 支用审核申请接口  

接口说明：海尔调用该接口向融担方提供支用审批。接口由融担方实现，海尔调用。  

接口地址：三方定义  

接口请求样例：  

请求报文：  

<html><body><table><tr><td colspan="5">请求参数</td></tr><tr><td colspan="5"></td></tr><tr><td>Head 参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>说明</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>否</td><td></td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>是</td><td>本接口传02</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>7.10中的接口号</td></tr></table></body></html>  

4.   


<html><body><table><tr><td colspan="5">Body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>参数类型</td><td>是否必须</td><td>备注</td></tr><tr><td>用户ID</td><td>userld</td><td>String</td><td>否</td><td>海尔用户Id</td></tr><tr><td>用户姓名</td><td>custName</td><td>String</td><td>是</td><td>申请人姓名</td></tr><tr><td>用户证件号</td><td>custNo</td><td>String</td><td>是</td><td>申请人证件号</td></tr><tr><td>用户手机号</td><td>custPhone</td><td>String</td><td>是</td><td>申请人手机号</td></tr><tr><td>海尔支用申请流水号</td><td>creditUseApplSeq</td><td>String</td><td>是</td><td></td></tr><tr><td>海尔支用申请时间</td><td>creditUseTime</td><td>String</td><td>是</td><td>yyyyMMddHHmmss</td></tr><tr><td>借款用途</td><td>loanUse</td><td>String</td><td>是</td><td>COS:整形美容 DEC:房屋装修 EDU：教育培训 FLI:手机数码 FMY:日常消费 HEA:健康医疗 MAR：结婚庆典 PARK:车位分期 SALE:家用电器 STO:家具家居</td></tr><tr><td>还款方式</td><td>repayMethod</td><td>String</td><td>是</td><td>TRA:外出旅游 0：等本等息 1：等额本金 2：等额本息</td></tr><tr><td>银行卡信息</td><td>bankCardlnfo</td><td>String</td><td>是</td><td>3：一次性还本付息 参考bankCardInfo 对象</td></tr><tr><td>资金信息</td><td>financelnfo</td><td>String</td><td>是</td><td>参考financelnfo对象</td></tr><tr><td>现居住省</td><td>liveProvince</td><td>String</td><td>是</td><td></td></tr><tr><td>现居住市</td><td>liveCity</td><td>String</td><td>是</td><td></td></tr><tr><td>现居地址区</td><td>liveArea</td><td>String</td><td>是</td><td></td></tr></table></body></html>  

<html><body><table><tr><td colspan="5"></td></tr><tr><td>现居地址</td><td>liveAddr</td><td>String</td><td>是</td><td>详细地址</td></tr><tr><td>个人邮箱</td><td>apptEmail</td><td>String</td><td>香</td><td></td></tr><tr><td>学历</td><td>educationLevel</td><td>String</td><td>是</td><td></td></tr><tr><td>婚姻状况</td><td>maritalStatus</td><td>String</td><td>是</td><td></td></tr><tr><td>工作信息</td><td> joblnfo</td><td>Object</td><td>是</td><td>详情参考joblnfo描述</td></tr><tr><td>联系人信息</td><td>contactList</td><td>List</td><td>是</td><td>详情参考contactList描述</td></tr><tr><td>活体信息</td><td>livingInfo</td><td>Object</td><td>香</td><td>详情参考 livinglnfo 描述</td></tr><tr><td>身份证OCR信息</td><td>idlnfo</td><td>Object</td><td>香</td><td>详情参考idlnfo描述</td></tr><tr><td>风控附加信息</td><td>riskExtInfo</td><td>Map<Strin g,Object></td><td>否</td><td>用于双方沟通的风控附加信 息，详情参考riskExtInfo</td></tr><tr><td colspan="5">contactList描述</td></tr><tr><td>联系人姓名</td><td>relName</td><td>String</td><td>是</td><td></td></tr><tr><td>与申请人关系</td><td>relRelation</td><td>String</td><td>是</td><td>01 父母；02 子女及兄弟姐妹； 06夫妻；07本人；99其他</td></tr><tr><td>联系人电话</td><td>relMobile</td><td>String</td><td>是</td><td></td></tr><tr><td>所在单位</td><td>relEmpName</td><td>String</td><td>香</td><td></td></tr><tr><td>联系人居住地址</td><td>relAddr</td><td>String</td><td>否</td><td></td></tr><tr><td colspan="5">bankCardlnfo描述</td></tr><tr><td>银行卡类型</td><td>bankCardType</td><td>String</td><td>是</td><td>1:储蓄卡</td></tr><tr><td>银行名称</td><td>bankName</td><td>String</td><td>是</td><td></td></tr><tr><td>银行卡号</td><td>bankCardNo</td><td>String</td><td>是</td><td></td></tr><tr><td>银行卡姓名</td><td>bankCardName</td><td>String</td><td>是</td><td></td></tr><tr><td>银行卡预留手机号</td><td>bankCardBinding MobileNo</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5"></td></tr><tr><td>jobInfo描述 工作单位</td><td>workUnits</td><td>String</td><td>是</td><td></td></tr><tr><td>工作电话</td><td>workPhone</td><td>String</td><td>是</td><td></td></tr><tr><td>职业</td><td>apptProfession</td><td>String</td><td>是</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>月收入 行业性质</td><td>monthlylncome industryNature</td><td>String String</td><td>是 是</td><td></td></tr><tr><td colspan="5"></td></tr><tr><td>活体信息描述 图片类型</td><td>imageType</td><td>String</td><td>否</td><td></td></tr><tr><td>活体正面照</td><td>livinglmageOne</td><td>String</td><td>香</td><td></td></tr><tr><td>动作照1</td><td>livinglmageTwo</td><td>String</td><td>否</td><td></td></tr><tr><td>动作照2</td><td>livinglmageThree</td><td>String</td><td>香</td><td></td></tr><tr><td>动作照3</td><td>livinglmageFour</td><td>String</td><td>否</td><td></td></tr><tr><td>环境照</td><td>livinglmageFive</td><td>String</td><td>香</td><td></td></tr><tr><td>人脸分数数组</td><td>faceRecolnfoList</td><td>List<faceR</td><td>否</td><td>详情参考faceRecolnfo描</td></tr><tr><td>faceRecolnfo描述</td><td></td><td>ecolnfo></td><td></td><td>述</td></tr><tr><td colspan="5"></td></tr><tr><td>活体识别渠道</td><td>faceCheckChanne</td><td>String</td><td>否</td><td>01：爰金03、06：旷视</td></tr></table></body></html>  

<html><body><table><tr><td></td><td>一</td><td></td><td></td><td>02:国政通</td></tr><tr><td>活体识别类型</td><td>faceCheckType</td><td>String</td><td>否</td><td>01：有源；02：无源</td></tr><tr><td>活体识别分数</td><td>faceCheckScore</td><td>String</td><td>否</td><td>0-100的小数，精确到小 数点后两位</td></tr><tr><td>活体置信度阈值</td><td>faceCheckThresh olds</td><td>String</td><td>否</td><td>03\06传此字段，传的数据格 式为 { "le-3":62.168713, "le-5":74.39926, "le-4":69.31534, "1e-6":78.038055 } 01、02不传此字段</td></tr><tr><td colspan="7">Idlnfo描述</td></tr><tr><td>姓名</td><td>name</td><td>String</td><td>是</td><td></td></tr><tr><td>性别</td><td>sex</td><td>String</td><td>是</td><td></td></tr><tr><td>民族</td><td>nation</td><td>String</td><td>是</td><td></td></tr><tr><td>身份证号</td><td>idNo</td><td>String</td><td>是</td><td></td></tr><tr><td>户口所在地</td><td>address</td><td>String</td><td>是</td><td></td></tr><tr><td>签发机关</td><td>issueAgency</td><td>String</td><td>是</td><td></td></tr><tr><td>签发日</td><td>issueDate</td><td>String</td><td>是</td><td></td></tr><tr><td>到期日</td><td>expireDate</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">riskExtInfo描述</td></tr><tr><td>模型分1</td><td>modelScorel</td><td>String</td><td>否</td><td></td></tr><tr><td>模型分2</td><td>modelScore2</td><td>String</td><td>否</td><td></td></tr><tr><td>模型分3</td><td>modelScore3</td><td>String</td><td>否</td><td></td></tr><tr><td>预留字段1</td><td>reservedField1</td><td>String</td><td>否</td><td></td></tr><tr><td>预留字段2</td><td>reservedField2</td><td>String</td><td>否 否</td><td></td></tr><tr><td colspan="5">预留字段3 reservedField3 String</td></tr><tr><td colspan="5">financelnfo</td></tr><tr><td>installTotalAmt</td><td>decimal(11,2)</td><td>是</td><td>分期总金额</td><td>单位：元</td></tr><tr><td>installTotalCnt</td><td>int</td><td>是</td><td>分期总期数</td><td></td></tr><tr><td>installRate</td><td>decimal(11,6)</td><td>是</td><td>分期利率</td><td>如0.24</td></tr></table></body></html>  

5.  

6. 返回报文：  


<html><body><table><tr><td>响应参数</td><td colspan="4"></td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td colspan="5">Head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000;失败：00001</td></tr></table></body></html>  

<html><body><table><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">Body</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>returnCode</td><td>String</td><td>是</td><td>返回码</td><td></td></tr><tr><td>returnDes</td><td>String</td><td>是</td><td>返回码描述</td><td></td></tr><tr><td>creditUseApply No</td><td>String</td><td>是</td><td>支用申请流水号</td><td>海尔流水号</td></tr><tr><td>approveTime</td><td>String</td><td>是</td><td>响应时间</td><td></td></tr><tr><td>applyResult</td><td>String</td><td>是</td><td>审批结果</td><td>0:审批中</td></tr></table></body></html>  

# 7. 报文样例  

# 8. 请求报文  

9. {   
10. "head": {   
11. "tradeTime": "14:02:05",   
12. "channelNo": "A1",   
13. "sysFlag": "11",   
14. "serno": "9d373849e3c844078ac734642cfaff15",   
15. "tradeCode": "04",   
16. "tradeDate": "2022-09-22",   
17. "tradeType": "02"   
18. },   
19. "body": {   
20. "custNo": "34022110000009821X",   
21. "loanUse": "FMY",   
22. "applSeq": "********",   
23. "repayMethod": "2",   
24. "creditUseTime": "**************",   
25. "custName": "张三",   
26. "custPhone": "***********",   
27. "userId": "C202209000000000X20510",   
28. "financeInfo": {   
29. "installTotalAmt": 500,   
30. "installTotalCnt": "12",   
31. "installRate": 0.24   
32. },   
33. "contactList": [   
34. {   
35. "relName": "爱好",   
36. "relMobile": "***********",   
37. "relRelation": "06"   
38. },   
39. {   
40. "relName": "火锅",   
41. "relMobile": "***********",   
42. "relRelation": "99"   
43. }   
44. ],   
45. "bankCardInfo": {   
46. "bankCardNo": "****************",   
47. "bankCardType": "1",   
48. "bankCardBindingMobileNo": "***********",   
49. "bankName": "上海浦东发展银行",   
50. "bankCardName": "张三"   
51. },   
52. "creditUseApplSeq": "********",   
53. "idInfo": {   
54. "address": "安徽省芜湖市芜湖县六郎镇",   
55. "issueAgency": "芜湖县公安局",   
56. "nation": "汉",   
57. "sex": "男",   
58. "name": "张三",   
59. "expireDate": "********",   
60. "issueDate": "********",   
61. "idNo": "34022000000000821X"   
62. }   
63. }   
64. }   
65. 响应报文   
66. {   
67. "head": {   
68. "retMsg": "处理成功",   
69. "retFlag": "00000"   
70. },   
71. "body": {   
72. "returnCode": "00000",   
73. "returnDes": "响应成功",   
74. "creditUseApplyNo": "********",   
75. "approveTime": "2022-09-22 14:02:17"   
76. }   
77. }  

# 3.2.2 支用结果通知接口  

接口说明：接口由海尔实现，融担方调用。调用该接口将支用审批结果通知给海尔。  

接口地址：待定  

接口请求样例：  

接口说明：  

请求参数：  

78. 返回参数：  


<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>7.10中的接口号</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>是</td><td>本接口传01</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>是</td><td>融担机构在海尔的定义</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>支用申请流水号</td><td>creditUseApplyNo</td><td>String</td><td>是</td><td>海尔流水号</td></tr><tr><td>审批结果</td><td>applyResult</td><td>String</td><td>是</td><td>1:支用通过</td></tr><tr><td>审批时间</td><td>approveTime</td><td>String</td><td>是</td><td>2:支用拒绝</td></tr><tr><td>拒绝原因码</td><td>failCode</td><td>String</td><td>否</td><td>拒绝时必传</td></tr><tr><td>拒绝原因</td><td>failReason</td><td>String</td><td>否</td><td>拒绝时必传</td></tr></table></body></html>  

<html><body><table><tr><td colspan="5">响应参数</td></tr><tr><td colspan="5">Head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td>body</td><td colspan="4"></td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>返回码</td><td>returnCode</td><td>String</td><td>是</td><td></td></tr><tr><td>返回码描述</td><td>returnDes</td><td>String</td><td>是</td><td></td></tr></table></body></html>

79. 80.  

# 3.2.3 支用结果查询接口  

接口说明：接口由融担方实现，海尔调用。调用该接口查询融担方的支用审批结果  

# 接口地址：三方定义  

接口请求样例：  

接口说明：  

请求参数  


<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>7.10中的接口号</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>是</td><td>本接口传01</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>是</td><td>融担机构在海尔的定义</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>支用申请流水号</td><td>creditUseApplyNo</td><td>String</td><td>是</td><td>海尔流水号</td></tr></table></body></html>  

# 81. 响应参数  

<html><body><table><tr><td colspan="5">响应参数</td></tr><tr><td colspan="5">Head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td>body</td><td colspan="4"></td></tr><tr><td>参数名 类型</td><td></td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>返回码</td><td>returnCode</td><td>String</td><td>是</td><td></td></tr><tr><td>返回码描述</td><td>returnDes</td><td>String</td><td>是</td><td></td></tr><tr><td>支用申请流水号</td><td>creditUseApplyNo</td><td>String</td><td>是</td><td>海尔流水号</td></tr><tr><td></td><td>applyResult</td><td>String</td><td>是</td><td>0:审批中 1:支用通过</td></tr><tr><td>审批结果 审批时间</td><td>approveTime</td><td>String</td><td>是</td><td>2:支用拒绝</td></tr><tr><td>拒绝原因码</td><td>faiCode</td><td>String</td><td>否</td><td></td></tr><tr><td>拒绝原因</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>failReason</td><td>String</td><td>否</td><td></td></tr></table></body></html>  

# 3.3 代扣流程(加解密二)  

# 3.3.1 代扣申请  

接口说明：该接口由海尔实现，合作方调用，合作方发起的还款指令，调用海尔发起代扣还款  

<html><body><table><tr><td>method</td><td colspan="4">/api/{standard}/pay/apply</td></tr><tr><td colspan="5">请求参数</td></tr><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>userld</td><td>String</td><td>是</td><td>合作方平台的用户唯 一标识</td><td></td></tr><tr><td>payApplyNo</td><td>String</td><td>是</td><td>代扣请求流水号</td><td></td></tr><tr><td>channelCode</td><td>String</td><td>否</td><td>流量渠道编码</td><td>流量渠道编号 自营-D04 京东-D05</td></tr><tr><td>loanNo</td><td>String</td><td>是</td><td>资金方借据号</td><td>京东借钱-D06</td></tr><tr><td>payAmt</td><td>decimal(1 1,2)</td><td>是</td><td>扣款金额</td><td></td></tr><tr><td>bankCardNo</td><td>String</td><td>是</td><td>扣款银行卡号</td><td></td></tr><tr><td>bankCardName</td><td>String</td><td>是</td><td>银行卡户名</td><td></td></tr><tr><td>bankCardBindM obile</td><td>String</td><td>是</td><td>银行卡绑定手机号</td><td></td></tr><tr><td>bankCardType</td><td>String</td><td>是</td><td>银行卡类型</td><td>1:储蓄卡,2:信用卡</td></tr><tr><td>bankCardCode</td><td>String</td><td>是</td><td>银行编码</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>bankName</td><td>String</td><td>是</td><td>银行名称</td><td></td></tr><tr><td>certNo</td><td>String</td><td>否</td><td>身份证号</td><td></td></tr><tr><td>bankCardinfo</td><td>list</td><td></td><td>绑卡信息</td><td> start</td></tr><tr><td>bindChannelld</td><td>String</td><td>是</td><td>支付渠道id</td><td>01中金/02通联/03快钱/04宝 付/05连连/06京东/07百付宝</td></tr><tr><td>payContractld</td><td>String</td><td>是</td><td>协议支付id</td><td></td></tr><tr><td>bankCardinfo</td><td>list</td><td></td><td>绑卡信息</td><td>end</td></tr><tr><td colspan="5">响应参数</td></tr><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>是</td><td>返回码</td><td>审批原因码</td></tr><tr><td>message</td><td>String</td><td>是</td><td>返回码描述</td><td></td></tr><tr><td>hxPayApplyNo</td><td>String</td><td>是</td><td>海尔扣款申请案件号</td><td></td></tr></table></body></html>  

# 3.3.2 代扣申请结果查询  

接口说明：该接口由海尔实现，合作方调用，合作方海尔发起代扣还款申请后，通过此接口查询代扣结果  

<html><body><table><tr><td> method</td><td colspan="5">/api/{standard}/pay/payResultQuery</td></tr><tr><td colspan="7">请求参数</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td><td></td></tr><tr><td>payApplyNo</td><td> String</td><td>选填</td><td>代扣请求流水号</td><td>两者必传一个</td><td></td></tr><tr><td>hxPayApplyNo</td><td>String</td><td>选填</td><td>海尔扣款申请案件号</td><td>两者必传一个</td><td></td></tr></table></body></html>  

<html><body><table><tr><td colspan="6">响应参数</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td><td></td></tr><tr><td>code</td><td>String</td><td>是</td><td>返回码</td><td>审批原因码</td><td></td></tr><tr><td>message</td><td>String</td><td>是</td><td>返回码描述</td><td></td><td></td></tr><tr><td>status</td><td>String</td><td>是</td><td>扣处理状态</td><td>0 处理中/1成功/2失败</td><td></td></tr><tr><td>payAmt</td><td>decim al(11, 2)</td><td>是</td><td>扣款成功金额</td><td></td><td></td></tr><tr><td>payTime</td><td>String</td><td>否</td><td>扣款成功时间</td><td>yyyy-MM-dd HH:mm:ss 扣款成功必填</td><td></td></tr><tr><td>failCode</td><td>String</td><td>否</td><td>失败原因码</td><td></td><td></td></tr><tr><td>failMsg</td><td>String</td><td>否</td><td>失败原因描述</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  

# 3.3.3 代扣结果回调  

接口说明：该接口由合作方实现，海尔调用，海尔通过此接口回调合作方代扣结果  

<html><body><table><tr><td> method</td><td colspan="4">合作方定义</td></tr><tr><td colspan="5">请求参数</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>payApplyNo</td><td>String</td><td>是</td><td>代扣申请流水 号</td><td></td></tr><tr><td>status</td><td>String</td><td>是</td><td>扣处理状态</td><td>1成功/2失败</td></tr><tr><td>payAmt</td><td>decimal(11,2)</td><td>否</td><td>扣款成金额</td><td>扣款成功必填</td></tr></table></body></html>  

<html><body><table><tr><td>payTime</td><td>String</td><td>否</td><td>扣款成功时间</td><td>yyyy-MM-dd HH:mm:ss 扣款成功必填</td></tr><tr><td>failCode</td><td> String</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>failMsg</td><td>String</td><td>否</td><td>失败原因描述</td><td></td></tr><tr><td colspan="5">响应参数</td></tr><tr><td colspan="4"> head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>code</td><td> String</td><td>是</td><td>返回码</td><td></td></tr><tr><td>message</td><td>String</td><td>是</td><td>返回码描述</td><td></td></tr></table></body></html>  

# 3.4 还款流程(加解密三)  

# 【LP30006】还款计划查询  

接口地址：https://xxx/hrlp/repay/LP30006  

请求数据样例:  

{"applyNo":"","tradeCode":"","channelNo":"","data":"Json 加密内容"}  

当期应还总额：本金+正常利息+逾期利息+复利+费用金额+违约金+滞纳金 +提前还款手续费+账号管理费  

当期已还总额：已还本金+已还利息+已还逾期利息+已还费用金额+已还违约  

金+已还滞纳金+已还提前还款手续费+已还账号管理费  

剩余应还：当期应还总额-当期已还总额  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>固定值：LP30006</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证每日唯一</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>否</td><td>空</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>是</td><td>海尔消金项目经理提供</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>否</td><td>空</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>借据号</td><td>loanNo</td><td>String</td><td></td><td>借据号和申请流水号二者必填一</td></tr><tr><td>申请流水号</td><td>applSeq</td><td>String</td><td>选填</td><td>个， (未放款则不生成还款计 划）若都输入则以借据号查询</td></tr></table></body></html>  

# 5.3.1.1. 请求报文：  

# 5.3.1.2. 返回报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否必输</td><td>注释</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">返回多条信息(resultList):</td></tr><tr><td>申请编号</td><td>applSeq</td><td>BigDecimal(10,0)</td><td>否</td><td></td></tr><tr><td>申请日期</td><td>applyDt</td><td>String</td><td>否</td><td></td></tr><tr><td>贷款金额</td><td>apprvAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>总期数</td><td>apprvTnr</td><td>String</td><td>否</td><td></td></tr><tr><td>期限类型</td><td>applyTnrTyp</td><td>String</td><td>否</td><td></td></tr><tr><td>门店编号</td><td>cooprCde</td><td>String</td><td>香</td><td></td></tr><tr><td>门店名称</td><td>cooprName</td><td>String</td><td>否</td><td></td></tr><tr><td>渠道编号</td><td>channelNo</td><td>String</td><td>否</td><td></td></tr><tr><td>商户编号</td><td>superCoopr</td><td>String</td><td>香</td><td></td></tr><tr><td>合同编号</td><td>contNo</td><td>String</td><td>香</td><td></td></tr><tr><td>借据编号</td><td>loanNo</td><td>String</td><td>否</td><td></td></tr><tr><td>身份证号</td><td>idNo</td><td>String</td><td>香</td><td></td></tr><tr><td>是否联合贷款</td><td>loanMode</td><td>String</td><td>否</td><td></td></tr><tr><td>还款方式</td><td>mtdCde</td><td>String</td><td>否</td><td></td></tr><tr><td>还款方式描述</td><td>mtdDesc</td><td>String</td><td>否</td><td></td></tr><tr><td>贷款类型</td><td>typGrp</td><td>String</td><td>否</td><td></td></tr><tr><td>贷款产品</td><td>loanTyp</td><td>String</td><td>香</td><td></td></tr><tr><td>下一次还款日</td><td>nextDueDt</td><td>String</td><td>香</td><td>已结清业务此项必为空</td></tr><tr><td>最后还款日</td><td>lastDueDt</td><td>String</td><td>香</td><td>已结清业务此项必为空</td></tr><tr><td>放款日期</td><td>loanActvDt</td><td>String</td><td>否</td><td>YYYY-MM-DD</td></tr><tr><td>贷款状态</td><td>loanOdind</td><td>String</td><td>否</td><td>Y逾期N不逾期 还款日当天返回N过了还款日显 示Y 已结清业务此项必为空</td></tr><tr><td>返回多条信息 (lmPmShdList)</td><td colspan="4"></td></tr><tr><td>期号</td><td>psPerdNo</td><td>String</td><td>否</td><td></td></tr><tr><td>到期日</td><td>psDueDt</td><td>String</td><td>否</td><td></td></tr><tr><td>本金</td><td>psPrcpAmt</td><td>BigDecimal(16,2)</td><td>香</td><td></td></tr><tr><td>正常利息</td><td>psNormInt</td><td>BigDecimal(16,2)</td><td>香</td><td></td></tr><tr><td>逾期利息</td><td>psOdIntAmt</td><td>BigDecimal(16,2)</td><td>香</td><td></td></tr><tr><td>复利</td><td>psCommOdInt</td><td>BigDecimal(16,2)</td><td>香</td><td></td></tr><tr><td>费用金额</td><td>psFeeAmt</td><td></td><td>香</td><td>应还手续费</td></tr><tr><td>已还本金</td><td></td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td></td><td>setlPrcp</td><td>BigDecimal(16,2)</td><td></td><td></td></tr><tr><td>已还利息</td><td>setlNormInt</td><td>BigDecimal(16,2)</td><td>否</td><td>正常利息+提前还款利息</td></tr><tr><td>已还逾期利息</td><td>setlOdlntAmt</td><td>BigDecimal(16,2)</td><td>否</td><td>罚息</td></tr><tr><td>已还复利</td><td>setlCommOdlnt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>已还费用金额 结清标志</td><td>setlFeeAmt setlind</td><td>BigDecimal(16,2) String</td><td>香 否</td><td>已还手续费 Y已结清N未结清</td></tr><tr><td>逾期标志</td><td>psOdInd</td><td>String</td><td>否</td><td>当期本金、利息、罚息、复利、 费用如果有未结清的，则该标志 为'Y' 已过还款日但还处在宽限期内该</td></tr><tr><td>实际还款日</td><td></td><td></td><td></td><td>字段显示'N'</td></tr><tr><td>利率</td><td>lastSetlDt psIntRate</td><td>String BigDecimal(16,9)</td><td>否 否</td><td></td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>罚息利率</td><td>psOdlntRate</td><td>BigDecimal(16,9)</td><td>否</td><td></td></tr><tr><td>剩余本金</td><td>psRemPrcp</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>账号管理费</td><td>acctFeeAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>已还账号管理 费</td><td>setlAcctFeeAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>违约金</td><td>penalFeeAmt setlPenalFeeAm</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>已还违约金</td><td>t</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>滞纳金</td><td>lateFeeAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>已还滞纳金</td><td>setlLateFeeAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>期供金额 提前还款手续</td><td>pslnstmAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>费 已还提前还款</td><td>advanceFeeAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>手续费</td><td>setlAdvanceFee Amt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>减免正常利息 减免逾期利息</td><td>psWvNmInt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>减免复利</td><td>psWvOdInt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td></td><td>psWvCommInt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>减免信息列表</td><td>wvlist</td><td>List</td><td>否</td><td></td></tr><tr><td colspan="5">返回多条信息wvlist</td></tr><tr><td>减免类型</td><td>wVlype</td><td>String</td><td>香</td><td>01-已入账减免; 02-未入账减免; 03-内部差额减免; 04-买断未入账减免;</td></tr><tr><td>减免利息</td><td>wvNormintAmt</td><td>BigDecimal(16,2)</td><td></td><td>05-买断未入账内部减免;</td></tr><tr><td>减免罚息</td><td>wvOdintAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>减免滞纳金</td><td>wvlateAmt</td><td>BigDecimal(16,2)</td><td>否 香</td><td></td></tr><tr><td>减免违约金</td><td>wvPenalAmt</td><td>BigDecimal(16,2)</td><td>香</td><td></td></tr><tr><td>减免手续费</td><td>wvfeeAmt</td><td>BigDecimal(16,2)</td><td>否</td><td></td></tr><tr><td>减免还款手续</td><td>wvAdvanceAm</td><td></td><td></td><td></td></tr><tr><td>费</td><td>t</td><td>BigDecimal(16,2)</td><td>香</td><td></td></tr></table></body></html>  

# 5.3.1.3. 样例报文  

请求报文样例：   
{ "body": { "applSeq": "********" },  

"head": { "channelNo": "", "cooprCode": "", "serno": "baafc27776e74f16b595b80948156d8e", "sysFlag": "11", "tradeCode": "100354", "tradeDate": "2019-06-14", "tradeTime": "18:17:49", "tradeType": "" } 返回报文样例： "head": { "serno": "190618141703000000", "retFlag": "11", “retMsg”:”” }, “body”:{ "serno": "baafc27776e74f16b595b80948156d8e", "resultList": [{ "loanOdInd": "Y", "nextDueDt": "2022-11-20", "superCoopr": "512019002743", "applSeq": ********, "applyTnrTyp": "6", "apprvAmt": 748.50, "channelNo": "", "loanNo": "HCF-BTFQ01202209303772171111", "applyDt": "2019-04-23", "cooprName": "", "lastDueDt": "2023-04-20", "lmPmShdList": [{ "psWvOdInt": 0.0, "setlPenalFeeAmt": 0.0, "setlLateFeeAmt": 0.0, "psCommOdInt": 0.0, "psDueDt": "2022-10-01", "advanceFeeAmt": 0.0, "lateFeeAmt": 0.0, "psInstmAmt": 0.0, "psIntRate": 0.0, "psOdIntAmt": 0.0, "psOdIntRate": 0.0,  

![](images/85992dd58692c22476be93e900877fe423e0abe3cbdfa56d42976d05bd959d6f.jpg)  

penalFeeAmt": 3.51, "psWvCommInt": 0.0, "setlOdIntAmt": 0.0, "setlAdvanceFeeAmt": 0.0, "setlPrcp": 0.0, "psWvNmInt": 0.0, "psFeeAmt": 9.73, "setlNormInt": 0.0, "setlCommOdInt": 0.0, "psPrcpAmt": 124.75, "setlInd": "N", "psNormInt": 0.0, "psRemPrcp": 623.75, "acctFeeAmt": 0.0, "psOdInd": "Y", "lastSetlDt": "", "setlAcctFeeAmt": 0.0 }, { "psWvOdInt": 0.0, "setlPenalFeeAmt": 0.0, "setlLateFeeAmt": 0.0, "psCommOdInt": 0.0, "psDueDt": "2022-12-20", "advanceFeeAmt": 0.0, "lateFeeAmt": 30.0, "psInstmAmt": 172.85, "psIntRate": 0.0, "psOdIntAmt": 0.0, "psOdIntRate": 0.0, "psPerdNo": 2, "setlFeeAmt": 0.0, "penalFeeAmt": 8.37, "psWvCommInt": 0.0, "setlOdIntAmt": 0.0, "setlAdvanceFeeAmt": 0.0, "setlPrcp": 0.0, "psWvNmInt": 0.0, "psFeeAmt": 9.73, "setlNormInt": 0.0, "setlCommOdInt": 0.0, "psPrcpAmt": 124.75, "setlInd": "N", "psNormInt": 0.0, "psRemPrcp": 499.0,  

"acctFeeAmt": 0.0, "psOdInd": "Y", "lastSetlDt": "", "setlAcctFeeAmt": 0.0   
}, { "psWvOdInt": 0.0, "setlPenalFeeAmt": 0.0, "setlLateFeeAmt": 0.0, "psCommOdInt": 0.0, "psDueDt": "2023-01-20", "advanceFeeAmt": 0.0, "lateFeeAmt": 30.0, "psInstmAmt": 176.88, "psIntRate": 0.0, "psOdIntAmt": 0.0, "psOdIntRate": 0.0, "psPerdNo": 3, "setlFeeAmt": 0.0, "penalFeeAmt": 12.4, "psWvCommInt": 0.0, "setlOdIntAmt": 0.0, "setlAdvanceFeeAmt": 0.0, "setlPrcp": 0.0, "psWvNmInt": 0.0, "psFeeAmt": 9.73, "setlNormInt": 0.0, "setlCommOdInt": 0.0, "psPrcpAmt": 124.75, "setlInd": "N", "psNormInt": 0.0, "psRemPrcp": 374.25, "acctFeeAmt": 0.0, "psOdInd": "Y", "lastSetlDt": "", "setlAcctFeeAmt": 0.0   
}, { "psWvOdInt": 0.0, "setlPenalFeeAmt": 0.0, "setlLateFeeAmt": 0.0, "psCommOdInt": 0.0, "psDueDt": "2023-02-20", "advanceFeeAmt": 0.0, "lateFeeAmt": 30.0, "psInstmAmt": 179.6,  

"psOdIntAmt": 0.0, "psOdIntRate": 0.0, "psPerdNo": 4, "setlFeeAmt": 0.0, "penalFeeAmt": 15.12, "psWvCommInt": 0.0, "setlOdIntAmt": 0.0, "setlAdvanceFeeAmt": 0.0, "setlPrcp": 0.0, "psWvNmInt": 0.0, "psFeeAmt": 9.73, "setlNormInt": 0.0, "setlCommOdInt": 0.0, "psPrcpAmt": 124.75, "setlInd": "N", "psNormInt": 0.0, "psRemPrcp": 249.5, "acctFeeAmt": 0.0, "psOdInd": "Y", "lastSetlDt": "", "setlAcctFeeAmt": 0.0 }, { "psWvOdInt": 0.0, "setlPenalFeeAmt": 0.0, "setlLateFeeAmt": 0.0, "psCommOdInt": 0.0, "psDueDt": "2023-03-20", "advanceFeeAmt": 0.0, "lateFeeAmt": 33.62, "psInstmAmt": 188.87, "psIntRate": 0.0, "psOdIntAmt": 0.0, "psOdIntRate": 0.0, "psPerdNo": 5, "setlFeeAmt": 0.0, "penalFeeAmt": 20.77, "psWvCommInt": 0.0, "setlOdIntAmt": 0.0, "setlAdvanceFeeAmt": 0.0, "setlPrcp": 0.0, "psWvNmInt": 0.0, "psFeeAmt": 9.73, "setlNormInt": 0.0,  

"setlCommOdInt": 0.0, "psPrcpAmt": 124.75, "setlInd": "N", "psNormInt": 0.0, "psRemPrcp": 124.75, "acctFeeAmt": 0.0, "psOdInd": "Y", "lastSetlDt": "", "setlAcctFeeAmt": 0.0 }, { "psWvOdInt": 0.0, "setlPenalFeeAmt": 0.0, "setlLateFeeAmt": 0.0, "psCommOdInt": 0.0, "psDueDt": "2023-04-20", "advanceFeeAmt": 0.0, "lateFeeAmt": 40.34, "psInstmAmt": 184.54, "psIntRate": 0.0, "psOdIntAmt": 0.0, "psOdIntRate": 0.0, "psPerdNo": 6, "setlFeeAmt": 0.0, "penalFeeAmt": 9.72, "psWvCommInt": 0.0, "setlOdIntAmt": 0.0, "setlAdvanceFeeAmt": 0.0, "setlPrcp": 0.0, "psWvNmInt": 0.0, "psFeeAmt": 9.73, "setlNormInt": 0.0, "setlCommOdInt": 0.0, "psPrcpAmt": 124.75, "setlInd": "N", "psNormInt": 0.0, "psRemPrcp": 0.0, "acctFeeAmt": 0.0, "psOdInd": "Y", "lastSetlDt": "", "setlAcctFeeAmt": 0.0 } contNo": "HCF-BTFQ012022093037  

"cooprCde": "402019100000000000", "loanTyp": "20190010", "mtdDesc": "零利率还款", "typGrp": "02", "mtdCde": "LT001", "loanMode": "N", "apprvTnr": "6" } ] }  

# 5.3.1.4. 返回码值  

<html><body><table><tr><td>错误码</td><td>注释</td></tr><tr><td>HX99999</td><td></td></tr></table></body></html>  

# 【LP30041】 主动还款 （晨风）  

接口说明：  

接口地址：https://xxx/hrlp/repay/LP30041  

请求数据样例:   
{"applyNo":"","tradeCode":"LP3004101","channelNo":"","data":"Json   
加密内容"}  

# 5.3.1.5. 请求报文  

<html><body><table><tr><td colspan="5">Head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否 必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>Varchar(30)</td><td>是</td><td>LP30041</td></tr></table></body></html>  

<html><body><table><tr><td>报文流水号</td><td>serno</td><td>varchar(30)</td><td>是</td><td>保证唯一，不关于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>Varchar(30)</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>Varchar(30)</td><td>否</td><td>空</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>Varchar(30)</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td> tradeTime</td><td>Varchar(30)</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>Varchar(30)</td><td>否</td><td>业务渠道号</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>Varchar(30)</td><td>否</td><td>空</td></tr><tr><td colspan="5">Body</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否 必传</td><td>注释</td></tr><tr><td>三方还款请求 号</td><td>serNb</td><td>VARCHAR(3 0)</td><td>是</td><td>不能重复，可以使用渠道号+序列 的方式，保证不重复</td></tr><tr><td>合同号</td><td>contNo</td><td>VARCHAR(3 0)</td><td>是</td><td>还款业务的合同号</td></tr><tr><td>还款类型</td><td>setlMode</td><td>VARCHAR(2)</td><td>是</td><td>主动还款： NF (按期还款，按还款计划还款至 指定期数) NM（ 逾期后按金额还款，未欠款不可用</td></tr><tr><td>主动还款金额</td><td>actvPayAmt</td><td>Number(16, 2)</td><td>是</td><td>NM模式还款) 此字段必填，且输入金额必须 >0； 试算应还总金额；试算总额</td></tr><tr><td>主动还款本金</td><td>actvPrcp</td><td>Number(16, 2)</td><td>否</td><td>totalAmt字段值，无需计算 当 setIMode="ER"&& actvPaylnd="P"时,此字段必填, 且输入金额必须>0;</td></tr><tr><td>指定还款期号</td><td>psPerdNo</td><td>NUMBER(10 ）</td><td>是</td><td>其他模式还款此字段输入为空; NF 模式专属字段；其他模式该字 段无意义；NF和 FS时，该字段必</td></tr><tr><td>是否已到账</td><td>paymlnd</td><td>VARCHAR(2)</td><td>是</td><td>输，FS时，传未结清的最小期 N:未到账 Y:已到账</td></tr><tr><td>还款卡号</td><td>acctNo</td><td>VARCHAR(3 0)</td><td>选填</td><td>用来传还款账户卡号,必须和 acctName一起传值。默认扣还款 卡，共享其他卡时，需要传其他卡</td></tr><tr><td>卡号户名</td><td>acctName</td><td>VARCHAR(3</td><td>选填</td><td>号 用来传还款账号名称，必须和 acctNo一起传值。</td></tr><tr><td>还款途径</td><td>expRecCde</td><td>0） VARCHAR(1</td><td>是</td><td>主动还款：对接时提供</td></tr><tr><td></td><td></td><td>0）</td><td></td><td>线下还款：对接时提供</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>微信（如有）：对接时提供 支付宝（如有）：对接时提供</td></tr><tr><td>主动还款融担 金额</td><td>rdPayAmt</td><td>Number(16, 2)</td><td>否</td><td>融担金额</td></tr></table></body></html>  

# 5.3.1.6. 返回报文  

<html><body><table><tr><td colspan="4">head</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>注释</td></tr><tr><td>报文流水号</td><td>serno</td><td>VARCHAR(30)</td><td>请求报文流水号</td></tr><tr><td>返回标志</td><td>retFlag</td><td>VARCHAR(10)</td><td>成功：00000 失败：</td></tr><tr><td>返回消息</td><td>retMsg</td><td>VARCHAR(100)</td><td>成功：交易成功 失败：</td></tr></table></body></html>  

<html><body><table><tr><td colspan="5">body</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否必传</td><td>注释</td></tr><tr><td>还款状态</td><td>repaySts</td><td>VARCHAR2(10)</td><td>是</td><td>01：还款处理中 02：还款成功 03：还款失败</td></tr></table></body></html>  

# 5.3.1.7.返回码值  

<html><body><table><tr><td>错误码</td><td>错误原因</td></tr><tr><td>HX99999</td><td>网络通讯异常</td></tr><tr><td>HX30001</td><td>回购时，还款金额输入有误</td></tr><tr><td>HX30002</td><td>逾期天数【天数】，不满足回购条件</td></tr><tr><td>HX30003</td><td>该笔业务不允许回购</td></tr><tr><td>HX99999</td><td>暂不支持该业务模式【模式】</td></tr></table></body></html>  

# 【LP30042】 主动还款查询 （晨风）  

接口说明：  

接口地址：https://xxx/hrlp/repay/LP30042  

请求数据样例:  

{"applyNo":"","tradeCode":"LP3004201","channelNo":"","data":"Json 加密内容"}  

# 5.3.1.8. 请求报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>固定值：LP30042</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证每日唯一</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>香</td><td>空</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>是</td><td>海尔消金项目经理提供</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>否</td><td>空</td></tr><tr><td colspan="5">body</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否必传</td><td>注释</td></tr><tr><td>三方还款流水 号</td><td>serNb</td><td>Varchar(30)</td><td>是</td><td>还款请求时流水</td></tr></table></body></html>  

# 5.3.1.9. 返回报文  

<html><body><table><tr><td colspan="4">head</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>注释</td></tr><tr><td>报文流水号</td><td>serno</td><td>VARCHAR(30)</td><td>请求报文流水号</td></tr><tr><td>返回标志</td><td>retFlag</td><td>VARCHAR(10)</td><td>成功：00000 失败：</td></tr><tr><td>返回消息</td><td>retMsg</td><td>VARCHAR(100)</td><td>成功：交易成功 失败：</td></tr></table></body></html>  

<html><body><table><tr><td>body</td></tr></table></body></html>  

<html><body><table><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否必传</td><td>注释</td></tr><tr><td>还款状态</td><td>repaySts</td><td>Varchar(20)</td><td>是</td><td>01：还款处理中 02：还款成功</td></tr><tr><td>失败原因</td><td>failDesC</td><td>Varchar(256)</td><td></td><td>03：还款失败</td></tr><tr><td>贷款合同号</td><td>contNo</td><td>Varchar(30)</td><td></td><td></td></tr><tr><td>还款模式</td><td>setiMode</td><td>Varchar(20)</td><td></td><td></td></tr><tr><td>NF还款期数</td><td>psPerdNo</td><td>Varchar(20)</td><td></td><td>其他模式可能没有 值</td></tr><tr><td>NF应还日期</td><td>setlSpeDt</td><td>Varchar(20)</td><td></td><td></td></tr><tr><td>还款成功时间</td><td>repayRealTime</td><td>Varchar(10)</td><td></td><td>格式：yyyy-MM-</td></tr><tr><td>放款金额</td><td>dnAmt</td><td>decimal(16,2)</td><td></td><td>dd</td></tr><tr><td>还款总金额</td><td>totalAmt</td><td>decimal(16,2)</td><td></td><td></td></tr><tr><td>逾期本金</td><td>prcpAmt</td><td>Number(16,2)</td><td></td><td>欠款本金</td></tr><tr><td>提前还款本金</td><td>actvPrcp</td><td>Number(16,2)</td><td></td><td>提前还款未到期的 本金</td></tr><tr><td>正常利息</td><td>normlnt</td><td>Number(16,2)</td><td></td><td>到期应还本金产生</td></tr><tr><td>还逾期利息</td><td>odlnt</td><td>Number(16,2)</td><td></td><td>的利息 罚息</td></tr><tr><td>复利</td><td>commInt</td><td>Number(16,2)</td><td></td><td>复利（目前未收 取）</td></tr><tr><td>主动还款利息</td><td>actvNormInt</td><td>Number(16,2)</td><td></td><td>提前还款利息（提 前还款的已计提利</td></tr><tr><td>总费用</td><td>TotalFee</td><td>Number(16,2)</td><td></td><td>息） 手续费 repayAmt + 违约金 penaltyAmt +滞 纳金lateAmt +提 前还款手续费 earlyRepayAmt （FS或ER或NF模 式再减去减免金额 feeWaive（减免的 是提前还款手续 费)</td></tr></table></body></html>  

# 5.3.1.10. 返回码值  

<html><body><table><tr><td>错误码</td><td>错误原因</td></tr><tr><td>HX99999</td><td>网络通讯异常</td></tr></table></body></html>  

# 【LP30043】主动还款试算 （晨风）  

接口说明：  

接口地址：https://xxx/hrlp/repay/LP30043  

请求数据样例:  

{"applyNo":"","tradeCode":"*********","channelNo":"","data":"Json 加密内容"}  

# 5.3.1.11. 请求报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否必输</td><td>注释</td></tr><tr><td>报文流水号</td><td>serno</td><td>Varchar(32)</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>交易码</td><td>tradeCode</td><td>Varchar(10)</td><td>是</td><td>固定值：*********</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>Varchar(10)</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>Varchar(30)</td><td></td><td></td></tr><tr><td>交易日期</td><td>tradeDate</td><td>Varchar(30)</td><td>是</td><td>yyyy-mm-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>Varchar(30)</td><td>是</td><td>HH:mm;ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>Varchar(10)</td><td>是</td><td>海尔消金项目经理提供</td></tr><tr><td colspan="5">body</td></tr><tr><td>合同号</td><td>contNo</td><td>VARCHAR(30 ）</td><td>是</td><td>还款业务的合同号</td></tr><tr><td>借据号</td><td>loanNo</td><td>VARCHAR(30 ）</td><td>否</td><td>当单笔还款时，借据号必输</td></tr><tr><td>还款类型</td><td>setlMode</td><td>VARCHAR(2)</td><td>是</td><td>主动还款：放款日当天不支持提前 全部结清 FS（结清还款，任何情况下都可结 清还款)</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>NF（指定期数还款，按还款计划还 款至指定期数) NM（归还欠款，未欠款不可用NM 模式还款)</td></tr><tr><td>指定还款期号</td><td>psPerdNo</td><td>NUMBER(10)</td><td>是</td><td>FS 时，传未结清的最小期，NF 时，传当期</td></tr></table></body></html>  

# 5.3.1.12. 返回报文  

总金额  (  已减优惠金额  )= 应归还逾期本金(prcpAmt)+提前还款本金(actvPrcp)+应归还正常利息(normInt)+ 应 归 还 逾 期 利 息 (odInt)+ 应 归 还 复 利 (commInt)+ 主 动 还 款 利 息(actvNormInt)+应还手续费(repayAmt)+违约金费用(penaltyAmt)+滞纳金费用(lateAmt)  

总的还款本金  = 应归还逾期本金(prcpAmt)+提前还款本金(actvPrcp)应还总费用  ( feeAmt)=提前还款手续费+应还手续费+违约金费用+滞纳金费用  

<html><body><table><tr><td colspan="4">head</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>注释</td></tr><tr><td>返回标志</td><td>retFlag</td><td></td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td></td><td></td></tr><tr><td>流水号</td><td>serno</td><td></td><td>该字段为请求流水号</td></tr></table></body></html>  

<html><body><table><tr><td colspan="4">body</td></tr><tr><td>合同号</td><td>contNo</td><td>VARCHAR(30)</td><td>业务合同号</td></tr><tr><td>应归还逾期本金</td><td>prcpAmt</td><td>Number(16,2)</td><td>欠款本金</td></tr><tr><td>应归还正常利息</td><td>normlnt</td><td>Number(16,2)</td><td>到期应还本金产生的利息</td></tr><tr><td>应归还逾期利息</td><td>odlnt</td><td>Number(16,2)</td><td>罚息</td></tr><tr><td>应归还复利</td><td>commlnt</td><td>Number(16,2)</td><td>复利 (目前未收取)</td></tr><tr><td>主动还款利息</td><td>actvNormInt</td><td>Number(16,2)</td><td>提前还款利息 (提前还款的已计提利 息）</td></tr><tr><td>提前还款本金</td><td>actvPrcp</td><td>Number(16,2)</td><td>提前还款未到期的本金</td></tr><tr><td>违约金</td><td>penaltyAmt</td><td>Number(16,2)</td><td>客户逾期后，按产品配置费率每天收 取违约金，违约金的应还总金额</td></tr><tr><td>滞纳金</td><td>lateAmt</td><td>Number(16,2)</td><td>客户逾期后，按产品配置费率每期收 取滞纳金，滞纳金的应还总金额</td></tr><tr><td>应还手续费</td><td>repayAmt</td><td>Number(16,2)</td><td>应还手续费总额 (客户还款对应的应 还手续费、不包含提前还款手续费)</td></tr><tr><td>应还总费用</td><td>feeAmt</td><td>Number(16,2)</td><td>提前还款手续费+应还手续费+违约 金费用+滞纳金费用</td></tr><tr><td></td><td>loanOsPrcp</td><td>Number(16,2)</td><td></td></tr><tr><td>本次总计还款金额</td><td>totalAmt</td><td>Number(16,2)</td><td>本次试算还款的总金额;</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td>（如果做过CR模式还款，则包含 CR模式还款的金额) (优惠券使用后，提前还款手续费减 免后的总金额)</td></tr><tr><td>超限金额</td><td>diffAmt</td><td>Number(16,2)</td><td>超年化36 部分的金额，备注：只有 结清试算是才有金额，非结清试算时 为0</td></tr><tr><td>客户默认还款卡号</td><td>AcctNo</td><td>VARCHAR(50)</td><td>客户的默认还款卡号</td></tr><tr><td>客户默认还款卡户名</td><td>acctName</td><td>VARCHAR(50)</td><td>客户默认还款卡户名</td></tr><tr><td>提前还款手续费</td><td>earlyRepayAmt</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td>优惠券减免金额</td><td>coupUseAmt</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td>提前结清当期补记红线 手续费</td><td>curSupplyAmt</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td>补记红线金额</td><td> supplyAmt</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td>应还款总金额</td><td>allAmt</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td>Xxx</td><td>lastDteDt</td><td>VARCHAR(50)</td><td>不需要使用</td></tr><tr><td>贷款本金余额</td><td>loanOsPrcp</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td>已还款未入账的金额</td><td> setITotalAmtCr</td><td>Number(16,2)</td><td>不需要使用</td></tr><tr><td colspan="4">还款计划展示列表ImPmShdNFlist 注：此列表为NF模式专用，当入参中NF模式指定归还日期为空是才返回如下信息列表，返回信息为可归还的</td></tr><tr><td>还款计划信息 (只显示未结清的还款计划) 期号</td><td>psPerdNo</td><td>VARCHAR(2)</td><td></td></tr><tr><td>到期日</td><td>psDueDt</td><td>VARCHAR(10)</td><td></td></tr><tr><td>期供金额</td><td>psInstmAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>本金</td><td>psPrcpAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>剩余本金</td><td>psRemPrcp</td><td>Number(16,2)</td><td></td></tr><tr><td>利息</td><td>psNormInt</td><td>Number(16,2)</td><td></td></tr><tr><td>罚息</td><td>psOdlntAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>手续费</td><td> psFeeAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>账号管理费</td><td>acctFeeAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>违约金</td><td>penalFeeAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>滞纳金</td><td>lateFeeAmt</td><td>Number(16,2)</td><td></td></tr><tr><td>逾期标志</td><td>psOdInd</td><td>VARCHAR(2)</td><td></td></tr><tr><td colspan="4">冲账调账最大可减免金额列表reverseList</td></tr><tr><td></td><td></td><td></td><td>F:费用（冲账）； I:利息（冲账）；</td></tr><tr><td>类型</td><td>reverseKind</td><td>VARCHAR(2)</td><td>IP：罚息（冲账）； IM：差额（调账）；</td></tr></table></body></html>



<html><body><table><tr><td>类型</td><td>reverseKind</td><td>VARCHAR(2)</td><td>F:费用（冲账）； I:利息（冲账）； IP:罚息（冲账）； IM：差额（调账）； LD：违约金（调账）； CM：佣金（调账）；</td></tr><tr><td>最大可调减金额</td><td>reverseAmt</td><td>Number(16,2)</td><td>如果调账，必传 最大可减免金额，即最大可亏损金额</td></tr></table></body></html>  

# 还款通知 【消金to 三方】  

接口说明：此接口用于客户还款后，全量通知三方以便同步还款计划使用。该数据推送采用RSA 非对称加密，传输三方时，采用加签+加密的方式，报文体如下所示。  

{"applyNo":"","tradeCode":"repayNotify","channelNo":"","data":"Json 加密内容"}  

接口地址：合作机构提供接收地址  

# ********. 请求报文  

<html><body><table><tr><td colspan="5"> head</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>是否必传</td><td>注释</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>LP40025</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证每日唯一</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>否</td><td>空</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>否</td><td></td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>否</td><td>空</td></tr></table></body></html>  

<html><body><table><tr><td colspan="5">body</td></tr><tr><td>名称</td><td>代码</td><td>数据类型</td><td>必传</td><td>注释</td></tr><tr><td>合作方 还款流水</td><td>partnerRepayNo</td><td>Varchar</td><td>否</td><td>合作方发起的还款 单号，仅合作方发</td></tr><tr><td>海尔还款流水号</td><td>repayNo</td><td>Varchar</td><td>是</td><td>起的单号有值 海尔侧还款</td></tr><tr><td>海尔扣款流水号</td><td>payNo</td><td>Varchar</td><td>否</td><td>仅未到账还款时有 值</td></tr><tr><td>还款状态</td><td>repayStatus</td><td>Varchar</td><td>是</td><td>02：还款成功</td></tr><tr><td>状态描述</td><td>message</td><td>Varchar</td><td>是</td><td>03：还款失败</td></tr><tr><td>合同号</td><td>contNo</td><td>Varchar</td><td>是</td><td></td></tr><tr><td>还款时间</td><td>repayTime</td><td>Varchar</td><td>否</td><td>还款成功必有值</td></tr><tr><td>还款总金额</td><td>repayAmt</td><td>Number(16,2)</td><td>否</td><td>还款成功必有值 实际扣款金额</td></tr><tr><td>还款模式</td><td>repayType</td><td>Varchar</td><td>是</td><td>01:主动还款 02:到期批扣 03:客服还款 04:催收 05：代偿</td></tr><tr><td>借据状态</td><td>loanStatus</td><td>Varchar</td><td>是</td><td>06:买断 逾期：overdue 正常：normal</td></tr><tr><td>还款期数</td><td>repayPerdNo</td><td>Varchar</td><td>否</td><td>还款成功有值 [期数逗号分隔]</td></tr><tr><td>提前还款手续费</td><td>preRepayFee</td><td>Number(16,2)</td><td>是</td><td>1,2,3,4,5,6</td></tr><tr><td>超限金额</td><td>diffAmt</td><td>Number(16,2)</td><td></td><td>超36</td></tr><tr><td>repayDetailList</td><td></td><td></td><td></td><td></td></tr><tr><td>还款期数</td><td>repayPerdNo</td><td>Varchar</td><td>是</td><td></td></tr><tr><td>结清标志</td><td>settlelnd</td><td>Varchar</td><td>是</td><td>Y已结清 N未结清</td></tr><tr><td>实还本金</td><td>repayPrincipal</td><td>Number(16,2)</td><td>是</td><td></td></tr><tr><td>实还利息</td><td>repaylnterest</td><td>Number(16,2)</td><td>是</td><td></td></tr><tr><td>实还罚息</td><td>repayOverdueFee</td><td>Number(16,2)</td><td>是</td><td></td></tr><tr><td>实还费用</td><td>repayFee</td><td>Number(16,2)</td><td>是</td><td>无为0</td></tr><tr><td>减免利息</td><td>wvNormintAmt</td><td>Number(16,2)</td><td>否</td><td></td></tr><tr><td>减免罚息</td><td>wvOdintAmt</td><td>Number(16,2)</td><td>否</td><td></td></tr></table></body></html>  

# ********. 返回报文  

<html><body><table><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>00000：成功 非00000:不成功</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td>失败，必返</td></tr></table></body></html>

{ “retFlag”：”00000“， “retMsg” ：”“ }  

# 3.5 通用接口(加解密三)  

# 【LP10010】 额度申请进度查询  

接口地址：https://xxx/hrlp/credit/LP10010  

请求数据样例:   
{"applyNo":"*********0","tradeCode":"LP1001001","channelNo":""   
,"data":"Json 加密内容"}  

# 5.3.1.15. 请求报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定传：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>否</td><td>传空</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>是</td><td>海尔消金项目经理提供</td></tr></table></body></html>  

<html><body><table><tr><td>交易码</td><td>tradeCode</td><td> String</td><td>是</td><td>固定值：LP1001001</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>额度申请流水号</td><td>applSeq</td><td>String</td><td>是</td><td>额度申请成功时返回的applSeq</td></tr></table></body></html>  

# 5.3.1.16. 返回报文  

<html><body><table><tr><td colspan="5"></td></tr><tr><td>head 参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>额度申请流水号</td><td>applSeq</td><td>String</td><td>是</td><td></td></tr><tr><td>状态</td><td>outSts</td><td>String</td><td>是</td><td>参照牧举值：额度审批状态</td></tr><tr><td>额度审批金额</td><td>apprvCrdAmt</td><td>Decimal</td><td>否</td><td>审批通过后有值</td></tr><tr><td>额度有效期</td><td>apprvTnr</td><td>String</td><td>否</td><td>天数。审批通过后有值</td></tr><tr><td>审批轨迹集合</td><td>resultList</td><td>List</td><td>否</td><td></td></tr><tr><td>客户编号</td><td>custNo</td><td>String</td><td>是</td><td>客户编号</td></tr><tr><td colspan="5">resultList（多条）-start注:（resultList）里面的 info 是每个状态的审批 轨迹</td></tr><tr><td colspan="5"></td></tr><tr><td>info 节点名称</td><td>wfiNodeName</td><td>String</td><td>是</td><td></td></tr><tr><td>节点ID</td><td>wfiNodeId</td><td>String</td><td>是</td><td></td></tr><tr><td>流程状态</td><td>wfiStatus</td><td>String</td><td>是</td><td></td></tr><tr><td>错误编码</td><td>errorCode</td><td>String</td><td>否</td><td>暂时不用</td></tr><tr><td>办理人</td><td>appUserName</td><td>String</td><td></td><td></td></tr><tr><td>办理时间</td><td>operateTime</td><td>String</td><td></td><td></td></tr><tr><td>审批结论</td><td>appConclusion</td><td>String</td><td></td><td>10同意 20拒绝 40退回 92取消</td></tr></table></body></html>  

<html><body><table><tr><td colspan="4"></td><td>99 转人工审批</td></tr><tr><td>内部意见</td><td>applnAdvice</td><td>String</td><td></td><td></td></tr><tr><td>外部意见</td><td>appOutAdvice</td><td>String</td><td></td><td></td></tr><tr><td>办理机构</td><td>appOrgName</td><td>String</td><td></td><td></td></tr><tr><td colspan="5">resultList（多条) -end</td></tr><tr><td>产品信息</td><td>productInfo</td><td>List</td><td>否</td><td></td></tr><tr><td colspan="5">productInfo(多条)其中包含intRatList (其中包含 resultList)</td></tr><tr><td>贷款品种</td><td>typCde</td><td>String</td><td>是</td><td></td></tr><tr><td>标识贷款品种顺 序号</td><td>typSeq</td><td>String</td><td></td><td></td></tr><tr><td>单笔最小贷款金 额</td><td>minAmt</td><td>String</td><td></td><td></td></tr><tr><td>单笔最大贷款金 额</td><td>maxAmt</td><td>String</td><td></td><td></td></tr><tr><td>贷款期限</td><td>tnrOpt</td><td>String</td><td></td><td></td></tr><tr><td>最大天数</td><td>tnrMaxDays</td><td>String</td><td></td><td></td></tr><tr><td>还款方式代码</td><td>mtdCde</td><td>String</td><td></td><td></td></tr><tr><td>还款方式描述</td><td>mtdDesc</td><td>String</td><td></td><td></td></tr><tr><td>进件通路</td><td>docChannel</td><td>String</td><td></td><td></td></tr><tr><td>每期还款日</td><td>dueDayOpt</td><td>String</td><td></td><td></td></tr><tr><td>还款日</td><td>dueDay</td><td>String</td><td></td><td></td></tr><tr><td>还款间隔</td><td>TypFreq</td><td>String</td><td></td><td></td></tr><tr><td>贷款类型</td><td>typGrp</td><td>String</td><td></td><td></td></tr><tr><td>还款方式代码</td><td>mtdTypeCde</td><td>String</td><td></td><td></td></tr><tr><td>利息,利率相关信 息</td><td>intRatList</td><td>List</td><td></td><td></td></tr></table></body></html>  

intRatList：以下字段均取自信贷数据库！！描述来自信贷字段注释！  


<html><body><table><tr><td>收取方式</td><td>comDesc</td><td>String</td><td></td><td></td></tr><tr><td>费用描述</td><td>feeDesc</td><td>String</td><td></td><td></td></tr><tr><td>费用按期限收取 类型\r\n通用类 型：</td><td>feeTnrTyp</td><td>String</td><td></td><td></td></tr><tr><td>客户利率</td><td>intRat</td><td>String</td><td></td><td></td></tr><tr><td>贷款品种代码</td><td>typCde</td><td>String</td><td></td><td></td></tr><tr><td>费用比例</td><td>feePct</td><td>String</td><td></td><td></td></tr><tr><td>版本号</td><td>typVer</td><td>String</td><td></td><td></td></tr><tr><td>贷款品种状态</td><td>typSts</td><td>String</td><td></td><td></td></tr><tr><td>贷款品种描述</td><td>typDesc</td><td>String</td><td></td><td></td></tr><tr><td>标识贷款品种顺</td><td>typSeq</td><td>String</td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td>序号</td><td></td><td></td><td></td><td></td></tr><tr><td>期限设定</td><td>tnrOpt</td><td>String</td><td></td><td></td></tr><tr><td>增值服务列表</td><td>resultList</td><td>List</td><td>否</td><td></td></tr></table></body></html>  

返回多条信息(resultList):  

<html><body><table><tr><td>增值服务集编号</td><td>valueaddCde</td><td>String</td><td></td><td></td></tr><tr><td>基础产品编号</td><td>typCde</td><td>String</td><td></td><td></td></tr><tr><td>基础产品名称</td><td>typDesc</td><td>String</td><td></td><td></td></tr><tr><td>增值服务产品编 号</td><td>loanTypCde</td><td>String</td><td></td><td></td></tr><tr><td>增值服务产品名 称</td><td>loanName</td><td>String</td><td></td><td></td></tr><tr><td>增值服务名称</td><td>serviceName</td><td>String</td><td></td><td></td></tr><tr><td>增值服务描述</td><td>serviceDesc</td><td>String</td><td></td><td></td></tr><tr><td>价格描述</td><td>priceDesc</td><td>String</td><td></td><td></td></tr><tr><td>是否默认</td><td>valueaddInd</td><td>String</td><td></td><td></td></tr></table></body></html>  

# 5.3.1.17. 样例报文  

# 请求报文样例：  

{ "body": { "applSeq":"********" }, "head": { "channelNo": "", "serno": "5112AP190408173455000000", "sysFlag": "11", "tradeCode": "ACQ-4002", "tradeDate": "2019-04-08", "tradeTime": "17:34:55" }   
}   
返回报文样例：   
{ "head": { "retFlag": "00000", "retMsg": "处理成功" }, "body": { "applSeq": "********",   
"outSts": "00",   
"custNo":"C2022081432118561745",   
"resultList": [{ "appOrgName": "", "wfiStatus": "", "operateTime": "2019-05-23 09:54:46", "appUserName": "xxx", "appOutAdvice": "", "wfiNodeName": "待提交", "appConclusion": "", "wfiNodeId": "37_a3", "appInAdvice": "" }   
] ,   
"productInfo": [   
{ "tnrMinDays": "3", "typLvlDesc": "海尔冷柜分期购", "typLvlCde": "010003", "maxAmt": "200000.00", "minAmt": "600.00", "dueDay": "", "tnrOpt": "3", "intRatList": "", "mtdTypeCde": "LT002", "tnrMaxDays": "", "typGrp": "01", "mtdDesc": "到期一次性还本", "typCde": "15090a", "mtdCde": "11", "dueDayOpt": "1", "typFreq": "1M", "typSeq": "700364", "resultList": [], "docChannel": "DOClist019"   
},   
{ "tnrMinDays": "3", "typLvlDesc": "海尔冷柜分期购", "typLvlCde": "010003", "maxAmt": "200000.00", "minAmt": "600.00", "dueDay": 12, "tnrOpt": "6,9",  

"intRatList": "", "mtdTypeCde": "LT001", "tnrMaxDays": "", "typGrp": "01", "mtdDesc": "按期等额还本", "typCde": "15091a", "mtdCde": "02", "dueDayOpt": "2", "typFreq": "1M", "typSeq": "700367", "resultList": [], "docChannel": "DOClist019" } ] } }  

# 5.3.1.18. 返回码值  

<html><body><table><tr><td>错误码</td><td>注释</td></tr><tr><td>HX00045</td><td>必输项【xxx】不可为空</td></tr><tr><td>HX00044</td><td>参数异常</td></tr><tr><td>HX00047</td><td>获取流水号失败!/字典项校验参数异常！/未知的操作标识!/日期格式 (yyyy-MM-dd)校验失败!key=xxx;value=xxx</td></tr><tr><td>HX00056</td><td>信贷系统返回为空/您有在途的额度申请，请稍后！</td></tr><tr><td>HX00053</td><td>联系人【××x】的手机号【××x】不能与配偶的手机号相同!/×××不符合 身份证的校验规则/x××的值长度超过设置的最大值(×x×)/【x×x】字段不 符合数字的校验规则/xXX:XX×为无效的省市区编码，请更新省市区编码!/ 您有在途的额度申请，请稍后！</td></tr><tr><td>HX00051</td><td>ACQ:申请人的手机号【xxx】不能与配偶手机号相同!/【xxx】不符合手</td></tr></table></body></html>  

<html><body><table><tr><td></td><td>机号校验规则</td></tr><tr><td>HX00046</td><td>检测到10s内重复修改订单/额度申请信息未找到,修改失败!/额度申请信 息未找到,提交失败!/额度申请信息未找到，提交失败!/额度申请未找到/ 信贷系统异常</td></tr><tr><td>HX00048</td><td>新增额度申请，流水号获取失败！</td></tr><tr><td>HX00055</td><td>门店编码【×××】不在有效的配置范围内/等级人员编码【××x】不在有效 的配置范围内！</td></tr><tr><td>HX00049</td><td>当前额度申请状态不允许被修改/当前额度申请状态不允许被提交</td></tr><tr><td>HX00057</td><td>当前额度申请状态不允许被提交</td></tr><tr><td>HX00050</td><td>额度类型不在字典配置范围内！</td></tr><tr><td>HX00052</td><td>参数【x×x】的字典项校验失败!</td></tr><tr><td>HX00054</td><td>【xxx】不符合固定电话校验规则</td></tr><tr><td>HX99999</td><td></td></tr></table></body></html>  

# 【LP20005】 贷款信息/审批状态查询  

接口地址：https://xxx/hrlp/loan/LP20005  

请求数据样例:   
{"applyNo":"*********0","tradeCode":"LP2000501","channelNo":""   
,"data":"Json 加密内容"}  

# 5.3.1.19. 请求报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>固定值：LP2000501</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td> String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>否</td><td>不用填</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td> String</td><td>是</td><td>海尔消金项目经理提供</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>否</td><td>不用填</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>申请号</td><td>applSeq</td><td>String</td><td>否</td><td>对应支付进件返回字段applSeq</td></tr><tr><td>外部订单号</td><td>extSeq</td><td>String</td><td>否</td><td>渠道的外部订单号</td></tr></table></body></html>  

# 5.3.1.20. 返回报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr><tr><td>申请流水号</td><td>applSeq</td><td>String</td><td>是</td><td></td></tr><tr><td>合同号</td><td>contNo</td><td>String</td><td>否</td><td>合同签订后返</td></tr><tr><td>借据号</td><td>loanNo</td><td>String</td><td>否</td><td>记账完成后返</td></tr><tr><td>申请金额</td><td>applyAmt</td><td>String</td><td>是</td><td></td></tr><tr><td>核准金额</td><td>apprvAmt</td><td>String</td><td>是</td><td></td></tr><tr><td>还款账号户名</td><td>repayApplAcNa m</td><td>String</td><td>是</td><td></td></tr><tr><td>还款卡号</td><td>repayApplCard No</td><td>String</td><td>是</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>还款开户银行 代码</td><td>repayAccBankC de</td><td>String</td><td>是</td><td></td></tr><tr><td>还款账户所在 省</td><td>repayAcProvinc e</td><td>String</td><td>是</td><td></td></tr><tr><td>还款账户所在 市</td><td>repayAcCity</td><td> String</td><td>是</td><td></td></tr><tr><td>申请日期</td><td>applyDt</td><td>String</td><td>是</td><td></td></tr><tr><td>记账日期</td><td>loanActvDt</td><td>String</td><td>否</td><td>记账成功之后返</td></tr><tr><td>贷款期数</td><td>apprvTnr</td><td>String</td><td>是</td><td></td></tr><tr><td>还款方式</td><td>mtdCde</td><td>String</td><td>是</td><td></td></tr><tr><td>还款日</td><td>dueDay</td><td>String</td><td>是</td><td></td></tr><tr><td>到期日</td><td>lastDueDay</td><td>String</td><td>否</td><td></td></tr><tr><td>审批状态</td><td>outSts</td><td>String</td><td>是</td><td>枚举参考：附录-字典-支用审批 状态</td></tr><tr><td>拒绝原因</td><td>demo</td><td>String</td><td>否</td><td></td></tr><tr><td>是否联合放款</td><td>loanMode</td><td>VARCHAR(10)</td><td>否</td><td>Y:联合贷款 N:自有贷款 空:放款结果未返回</td></tr><tr><td>合作机构主键| D</td><td>agencyld</td><td>varchar (50)</td><td>否</td><td>联合放款为Y时有值</td></tr><tr><td>合作机构名称</td><td>agencyldName</td><td>varchar (100)</td><td>否</td><td>联合放款为Y时有值</td></tr><tr><td colspan="6">多条： (list)</td></tr><tr><td colspan="6"> goods</td></tr><tr><td>商品名称</td><td>goodsName</td><td>String</td><td>否</td><td></td></tr><tr><td>商品价格</td><td>goodsPrice</td><td>String</td><td>否</td><td></td></tr><tr><td>商品数量</td><td>goodsNum</td><td>String</td><td>否</td><td></td></tr></table></body></html>  

# 5.3.1.21. 样例报文  

# 请求报文样例：  

"body": { "applSeq": "********"   
},   
"head": { "channelNo": "", "serno": "hrlp201906150502293c39e53c37", "sysFlag": "11", "tradeCode": "100021",  

"tradeDate": "2019-06-15", "tradeTime": "05:02:29" } 返回报文样例： “head”:{ “serno” : “hrlp201906241326363cfa805253”, “retFlag” : “00000”, “retMsg” : “处理成功” }, “body”:{ "repayAcCity": "510100", "applSeq": "********", "apprvAmt": "1000", "loanNo": "", "applyDt": "2019-06-01", "repayAcProvince": "510000", "contNo": "HCF-WKX27202303223771111", "dueDay": "", "list": { "goods": [{ "goodsPrice ": "1000", "goodsName ": " 海尔", "goodsNum ": "1" }] }, "demo": "", "applyAmt": "1000", "repayApplAcNam": "xxx, "rejectReason": "", "lastDueDay": "", "outSts": "24", "mtdCde": "LT001", "loanMode": "null", "apprvTnr": "3", "repayApplCardNo": "1111111111111111111", "repayAccBankCde": "308", "loanActvDt" : "" }  

# 5.3.1.22. 返回码值  

<html><body><table><tr><td>错误码</td><td>注释</td></tr><tr><td>HX00017</td><td>必填项【xxx】不能为空/修改状态,流水号和申请编号不可都为空</td></tr><tr><td>HX00016</td><td>格式错误/参数异常/参数校验失败!流水号与申请号校验失败！</td></tr><tr><td>HX00020 HX00039</td><td>商品列表解析一场/字典项校验参数异常/获取流水号失败/未知的操作标 识!/申请流水号【x××】对应的贷款信息未找到!/请求报文格式转换错误！ (list==>info)xxx/日期格式(yyyy-MM-dd)校验失败! KEY=xxx;value=xxx</td></tr><tr><td>HX00021</td><td>您有在途的额度申请加支用 XXX:XX×为无效的省市区编码请更新省市区编码!/不符合身份证的校验规 则/xxX的值长度超过设置的最大值(xxx)/xx×不符合数字的校验规则/</td></tr><tr><td>HX00023</td><td>【xxx】不符合手机号校验规则/ACQ:申请人的手机号【x×x】不能与配 偶手机号相同</td></tr><tr><td>HX00038</td><td>核心系统返回为空</td></tr><tr><td>HX00018</td><td>检测到10s内重复修改订单/要提交的贷款申请不存在/贷款申请不存在</td></tr><tr><td>HX00027</td><td>贷款申请信息未找到,修改失败!/数据不存在流水号:×××/风险信息类型未 有效配置:dataTyp:xxx/所查询的贷款信息不存在/所查询的行卡信息不 存在!/所查询的商品信息不存在</td></tr><tr><td>HX00042</td><td>数据已被更新，请重新提交取消申请！/数据已被更新，请重新提交取消 申请！</td></tr><tr><td>HX00019</td><td>参数异常</td></tr></table></body></html>  

<html><body><table><tr><td>HX00034</td><td>PGW系统查询银行卡签约接口调用异常</td></tr><tr><td>HX00026</td><td>新增贷款申请，流水号获取失败</td></tr><tr><td>HX00025</td><td>门店编码【×xx】不在有效的配置范围内/等级人员编码【×××】不在有效 的配置范围内</td></tr><tr><td>HX00028</td><td>当前贷款状态不允许被修改/审批状态不符合,不是00 或03状态,不允许 删除/不允许修改</td></tr><tr><td>HX00031</td><td>改贷款品种【x×x】查询的业务信息已失效</td></tr><tr><td>HX00041</td><td>支用类型不在字典配置范围内</td></tr><tr><td>HX00032</td><td>身份证校验不通过/门店编号为空/录单用户ID为空/可访问渠道为空</td></tr><tr><td>HX00033</td><td>访问渠道(xxx)受限!</td></tr><tr><td>HX00022</td><td>参数【xxx】的字典项校验失败</td></tr><tr><td>HX00024</td><td>【xxx】不符合固定电话校验规则</td></tr><tr><td>HX00040</td><td>系统繁忙，提交失败，请稍后重试</td></tr><tr><td>HX00029</td><td>申请流水号和formld不可同时为空</td></tr><tr><td>HX00030</td><td>业务类型、key均不得为空</td></tr><tr><td>HX00035</td><td>您的默认还款卡未签约，为保证您的资金安全，请关注嗨付微信公众 号，点击【签约通道】进行签约，谢谢</td></tr><tr><td>HX00036</td><td>查询银行签约失败，不允许提交</td></tr><tr><td>HX00037</td><td>未查到银行还款卡信息，不允许提交</td></tr><tr><td>HX00043</td><td>贷款取消失败:xxX</td></tr><tr><td>HX99998</td><td>订单超时</td></tr></table></body></html>  

【LP40008】额度/贷款审批状态推送【消金 to 三方】  

接口说明：该接口为额度/贷款审批结果推送接口，合作方也可自行通过接口【LP20005】查询。  

数据不涉及加密  

接口地址：合作机构提供接收地址  

请求数据样例:   
{"applyNo":"*********0","tradeCode":"LP4000801","channelNo":""   
,"data":"Json 加密内容"}  

# 5.3.1.23. 请求报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>LP4000801</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>否</td><td></td></tr><tr><td>交易日期</td><td>tradeDate</td><td> String</td><td>是</td><td>yyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>是</td><td>海尔消金项目经理提供</td></tr><tr><td colspan="5">bodyA-额度审批通过后的字段，B-支用审批通过后的字段</td></tr></table></body></html>  

<html><body><table><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>申请编号</td><td>applSeq</td><td>String</td><td>是</td><td>A+B 额度申请返回的applSeq</td></tr><tr><td>渠道编号</td><td>channelNo</td><td colspan="2">String 是</td><td>A+B 渠道号</td></tr><tr><td>审批状态</td><td>outSts</td><td colspan="2">是 String</td><td>A+B 见额度审批状态枚举</td></tr><tr><td>审批描述</td><td>message</td><td colspan="2">否 String</td><td>A+B 若失败，有失败原因，若 成功为空。</td></tr><tr><td>推送类型</td><td>msgTyp</td><td colspan="2">是 String</td><td>A+B 推送类型 01:贷款审批消息推送</td></tr><tr><td>身份证号</td><td>idNo</td><td colspan="2">String 是</td><td>02:额度审批消息推送 A+B</td></tr><tr><td></td><td>custNo</td><td colspan="2">String</td><td>身份证号</td></tr><tr><td>客户编号 产品信息</td><td>productInfo List</td><td></td><td>是 否</td><td>A A</td></tr><tr><td colspan="5"></td></tr><tr><td>productlnfo(多条)其中包含 intRatList(其中包含 resultList) 贷款品种</td><td>typCde</td><td>String</td><td>是</td><td></td></tr><tr><td>标识贷款品种顺</td><td>typSeq</td><td>String</td><td></td><td></td></tr><tr><td>序号 单笔最小贷款金</td><td>minAmt</td><td>String</td><td></td><td></td></tr><tr><td>额 单笔最大贷款金</td><td>maxAmt</td><td>String</td><td></td><td></td></tr><tr><td>额 贷款期限</td><td>tnrOpt</td><td>String</td><td></td><td></td></tr><tr><td>最大天数</td><td>tnrMaxDays</td><td>String</td><td></td><td></td></tr><tr><td>还款方式代码</td><td>mtdCde</td><td>String</td><td></td><td></td></tr><tr><td>还款方式描述</td><td>mtdDesc</td><td>String</td><td></td><td></td></tr><tr><td>进件通路</td><td>docChannel</td><td>String</td><td></td><td></td></tr><tr><td>每期还款日</td><td>dueDayOpt</td><td>String</td><td></td><td></td></tr><tr><td>还款日</td><td>dueDay</td><td>String</td><td></td><td></td></tr><tr><td>还款间隔</td><td>TypFreq</td><td>String</td><td></td><td></td></tr><tr><td>贷款类型</td><td>typGrp</td><td>String</td><td></td><td></td></tr><tr><td>还款方式代码</td><td>mtdTypeCde</td><td>String</td><td></td><td></td></tr><tr><td>利息,利率相关</td><td>intRatList</td><td>List</td><td></td><td></td></tr><tr><td>信息</td><td></td><td></td><td></td><td></td></tr></table></body></html>  

intRatList：以下字段均取自信贷数据库！！描述来自信贷字段注释！  


<html><body><table><tr><td>收取方式</td><td>comDesc</td><td>String</td><td></td><td>1</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td>费用描述</td><td>feeDesc</td><td>String</td><td></td><td></td></tr><tr><td>费用按期限收取 类型\r\n通用类 型：</td><td>feeTnrTyp</td><td>String</td><td></td><td></td></tr><tr><td>客户利率</td><td>intRat</td><td>String</td><td></td><td></td></tr><tr><td>贷款品种代码</td><td>typCde</td><td>String</td><td></td><td></td></tr><tr><td>费用比例</td><td>feePct</td><td>String</td><td></td><td></td></tr><tr><td>版本号</td><td>typVer</td><td>String</td><td></td><td></td></tr><tr><td>贷款品种状态</td><td>typSts</td><td>String</td><td></td><td></td></tr><tr><td>贷款品种描述</td><td>typDesc</td><td>String</td><td></td><td></td></tr><tr><td>标识贷款品种顺 序号</td><td>typSeq</td><td>String</td><td></td><td></td></tr><tr><td>期限设定</td><td>tnrOpt</td><td>String</td><td></td><td></td></tr><tr><td>增值服务列表</td><td>resultList</td><td>List</td><td>否</td><td></td></tr><tr><td colspan="5">返回多条信息(resultList):</td></tr><tr><td>增值服务集编号</td><td>valueaddCde</td><td>String</td><td></td><td></td></tr><tr><td>基础产品编号</td><td>typCde</td><td>String</td><td></td><td></td></tr><tr><td>基础产品名称</td><td>typDesc</td><td>String</td><td></td><td></td></tr><tr><td>增值服务产品编 号</td><td>loanTypCde</td><td>String</td><td></td><td></td></tr><tr><td>增值服务产品名 称</td><td>loanName</td><td>String</td><td></td><td></td></tr><tr><td>增值服务名称</td><td>serviceName</td><td>String</td><td></td><td></td></tr><tr><td>增值服务描述</td><td>serviceDesc</td><td>String</td><td></td><td></td></tr><tr><td>价格描述</td><td>priceDesc</td><td>String</td><td></td><td></td></tr><tr><td>是不默认</td><td>aluoaddlnd</td><td>Strine</td><td></td><td></td></tr></table></body></html>  

# 5.3.1.24. 返回报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是滞必返</td><td>备注</td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td></td><td>成功：00000,其他均为失败</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td></td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是滞必返</td><td>备注</td></tr><tr><td>申请流水号</td><td>applSeq</td><td>String</td><td></td><td></td></tr></table></body></html>  

# 5.3.1.25. 样例报文  

# 请求报文样例：  

head": {   
"tradeTime": "09:11:51",   
"sysFlag": "",   
"channelNo": "E8", "serno": "606051390850072576",   
"tradeCode": "100022", "tradeDate": "2019-07-31",   
"tradeType": ""   
body": {   
"custNo": "C201907160603032142240",   
"applSeq": 13455915,   
"outSts": "27",   
"channelNo": "E8",   
"message": "核心同步额度申请状态推送",   
"idNo": "321202198306030321",   
"msgTyp": "02",   
"productInfo": [ { "tnrMinDays": "3", "typLvlDesc": "创客贷（海尔员工）", "typLvlCde": "090001", "maxAmt": "200000.00", "minAmt": "3000.00", "dueDay": 12, "tnrOpt": "6", "intRatList": "", "mtdTypeCde": "LT003", "tnrMaxDays": "", "typGrp": "02", "mtdDesc": "按期付息，到期还本，随借随还", "typCde": "15070a", "mtdCde": "15", "dueDayOpt": "2", "typFreq": "1M", "typSeq": "1987133", "resultList": [], "docChannel": "DOCLIST004"   
"tnrMinDays": "3",   
"typLvlDesc": "3C 数码\u201c 轻松购\u201d 分期",   
"typLvlCde": "020001",   
"maxAmt": "10000.00",   
"minAmt": "600.00",   
"dueDay": 12,   
"tnrOpt": "6,9,12",   
"intRatList": [ { "comDesc": "null", "feeDesc": "3C 数码分期 6-12 期分期手续费-按贷款周期-个人", "feeTnrTyp": "03", "intRatMonth": "0.00000", "intRat": "0.00000", "typCde": "16075", "feePct": "", "typVer": "2", "typSts": "A", "typDesc": "3C 数码分期", "typSeq": "289661740", "tnrOpt": "9" }, { "comDesc": "null", "feeDesc": "3C 数码分期 6-12 期分期手续费-按贷款周期-个人", "feeTnrTyp": "03", "intRatMonth": "0.00000", "intRat": "0.00000", "typCde": "16075", "feePct": "", "typVer": "2", "typSts": "A", "typDesc": "3C 数码分期", "typSeq": "289661740", "tnrOpt": "12" }   
],   
"mtdTypeCde": "LT001",   
"tnrMaxDays": "",   
"typGrp": "01",   
"mtdDesc": "按期还本付费",   
"typCde": "16075",  

"mtdCde": "04", "dueDayOpt": "2", "typFreq": "1M", "typSeq": "289661740", "resultList": [],  

023,DOC035,DOC036,DOCLIST001,DOCLI   
LIST006,DOCLIST008,DOClist023"   
}, { "tnrMinDays": "3", "typLvlDesc": "够花（嗨付线上渠道）", "typLvlCde": "110001", "maxAmt": "200000.00", "minAmt": "1000.00", "dueDay": "", "tnrOpt": "D", "intRatList": "", "mtdTypeCde": "SYS001", "tnrMaxDays": "60", "typGrp": "02", "mtdDesc": "按日计息，随借随还", "typCde": "16096a", "mtdCde": "09", "dueDayOpt": "1", "typFreq": "1M", "typSeq": "7480275", "resultList": [], "docChannel": "DOClist013"   
}, { "tnrMinDays": "3", "typLvlDesc": "有用分期", "typLvlCde": "020006", "maxAmt": "17000.00", "minAmt": "600.00", "dueDay": "", "tnrOpt": "12,15,18,6,9", "intRatList": [ { "comDesc": "null", "feeDesc": "3C 数码分期 6-12 期分期手续费-按贷款周期-个人", "feeTnrTyp": "03", "intRatMonth": "0.00610",  

"intRat": "0.00021", "typCde": "16106a", "feePct": "", "typVer": "1", "typSts": "A", "typDesc": "3C 数码分期 - 有用分期（外转）", "typSeq": "700535", "tnrOpt": "9" }, { "comDesc": "null", "feeDesc": "3C 数码分期 6-12 期分期手续费-按贷款周期-个人", "feeTnrTyp": "03", "intRatMonth": "0.00710", "intRat": "0.00024", "typCde": "16106a", "feePct": "", "typVer": "1", "typSts": "A", "typDesc": "3C 数码分期 - 有用分期（外转）", "typSeq": "700535", "tnrOpt": "12" } ], "mtdTypeCde": "M0002", "tnrMaxDays": "", "typGrp": "01", "mtdDesc": "等额本息", "typCde": "16106a", "mtdCde": "01", "dueDayOpt": "1", "typFreq": "1M", "typSeq": "700535", "resultList": [], "docChannel": "DOCLIST001" "tnrMinDays": "3", "typLvlDesc": "电信手机分期购", "typLvlCde": "020002", "maxAmt": "200000.00", "minAmt": "600.00", "dueDay": 20, "tnrOpt": "26",  

"intRatList": "", "mtdTypeCde": "LT013", "tnrMaxDays": "", "typGrp": "01", "mtdDesc": "按期还本付费", "typCde": "17038a", "mtdCde": "04", "dueDayOpt": "2", "typFreq": "1M", "typSeq": "20166973", "resultList": [], "docChannel": "DOCLIST001"   
},   
{ "tnrMinDays": "3", "typLvlDesc": "线上商城-消费分期", "typLvlCde": "020007", "maxAmt": "200000.00", "minAmt": "600.00", "dueDay": "", "tnrOpt": "10,12,3,6", "intRatList": "", "mtdTypeCde": "LT001", "tnrMaxDays": "", "typGrp": "01", "mtdDesc": "按期等额还本", "typCde": "17134a", "mtdCde": "02", "dueDayOpt": "4", "typFreq": "1M", "typSeq": "43719168", "resultList": [], "docChannel": "DOCLIST001"   
},   
{ "tnrMinDays": "3", "typLvlDesc": "海尔家电贷（嗨付）", "typLvlCde": "140003", "maxAmt": "200000.00", "minAmt": "100.00", "dueDay": "", "tnrOpt": "6,12", "intRatList": "", "mtdTypeCde": "M0002", "tnrMaxDays": "", "typGrp": "02", "mtdDesc": "等额本息", "typCde": "20180035", "mtdCde": "01", "dueDayOpt": "1", "typFreq": "1M", "typSeq": "288546644", "resultList": [], "docChannel": "DOClist030" }, { "tnrMinDays": "3", "typLvlDesc": "海尔家电贷（嗨付）", "typLvlCde": "140003", "maxAmt": "200000.00", "minAmt": "1000.00", "dueDay": "", "tnrOpt": "6,12", "intRatList": "", "mtdTypeCde": "M0002", "tnrMaxDays": "", "typGrp": "02", "mtdDesc": "等额本息", "typCde": "20180036", "mtdCde": "01", "dueDayOpt": "1", "typFreq": "1M", "typSeq": "280170692", "resultList": [], "docChannel": "DOClist030" } ] }   
}   
返回报文样例：   
{ "head": { "retFlag": "00000", "retMsg": "处理成功" }, "body": { "applSeq":”********” }  

# 【LP40029】客户撞库接口 【海尔to 三方】  

接口说明：客户撞库接口  

接口地址：三方提供接口  

请求数据样例:  
{“applyNo”:”*********0”,”tradeCode”:”LP40029”,”channelNo”:””,  
”data”:”Json 加密内容”}  

# 5.3.1.26. 请求报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>交易码</td><td>tradeCode</td><td>String</td><td>是</td><td>固定值：LP1000301</td></tr><tr><td>报文流水号</td><td>serno</td><td>String</td><td>是</td><td>保证唯一，不大于32位</td></tr><tr><td>系统标识</td><td>sysFlag</td><td>String</td><td>是</td><td>固定值：11</td></tr><tr><td>交易类型</td><td>tradeType</td><td>String</td><td>否</td><td>传空</td></tr><tr><td>交易日期</td><td>tradeDate</td><td>String</td><td>是</td><td>yyyy-MM-dd</td></tr><tr><td>交易时间</td><td>tradeTime</td><td>String</td><td>是</td><td>HH:mm:ss</td></tr><tr><td>渠道编码</td><td>channelNo</td><td>String</td><td>是</td><td>海尔消金项目经理提供</td></tr><tr><td>合作方编码</td><td>cooprCode</td><td>String</td><td>否</td><td>传空</td></tr><tr><td colspan="5">body</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必输</td><td>备注</td></tr><tr><td>手机号</td><td>mobileNo</td><td>String</td><td>是</td><td>Md5加密后的密文</td></tr><tr><td>身份证号</td><td>idNo</td><td>String</td><td>是</td><td>Md5加密后的密文</td></tr></table></body></html>  

# 5.3.1.27. 返回报文  

<html><body><table><tr><td colspan="5">head</td></tr><tr><td>参数名称</td><td>参数代码</td><td>数据类型</td><td>是否必返</td><td>备注</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>返回标志</td><td>retFlag</td><td>String</td><td>是</td><td>成功：00000 失败：</td></tr><tr><td>返回消息</td><td>retMsg</td><td>String</td><td>是</td><td></td></tr><tr><td colspan="5">body</td></tr><tr><td>撞库结果</td><td> status</td><td>String</td><td>否</td><td>1-成功 -1-失败</td></tr><tr><td>撞库失败原因</td><td>message</td><td>String</td><td>否</td><td>撞库失败必反</td></tr></table></body></html>  

# 5.3.1.28. 样例报文  

# 5.3.1.29. 返回码值  

<html><body><table><tr><td>错误码</td><td>注释</td></tr><tr><td>00000</td><td>成功</td></tr><tr><td>非00000</td><td>异常</td></tr></table></body></html>  

# 5.3.1.30. 返回码值  

# 4 文件  

# 放款对账  

还款计划  

还款记录  

买断文件  

结算明细文件  

5 返回码描述  

# 5.1 授信&支用流程  

系统异常(head 返回)  


<html><body><table><tr><td>retFlag</td><td>retMsg</td></tr><tr><td>00000</td><td>成功</td></tr><tr><td>00010</td><td>系统异常</td></tr></table></body></html>  

业务异常(body 返回)  

授信：  


<html><body><table><tr><td>returnCode</td><td>returnDes</td></tr><tr><td>SUCCESS</td><td>成功</td></tr><tr><td>ERROR_IDNO</td><td>身份证号格式错误</td></tr><tr><td>NOTFOUND_FLAG</td><td>标识缺失</td></tr></table></body></html>  

<html><body><table><tr><td>NOTFOUND_APPLYNO</td><td>申请流水号缺失</td></tr><tr><td>NOTFOUND_NAME</td><td>申请人姓名缺失</td></tr><tr><td>NOTFOUND_IDNO</td><td>身份证信息缺失</td></tr><tr><td>NOTFOUND_MOBILE</td><td>手机号或userld 缺失</td></tr><tr><td>NOTFOUND_ADDRESS_INFO</td><td>地址信息缺失</td></tr><tr><td>NOTFOUND_EDU_LEVEL</td><td>学历缺失</td></tr><tr><td>NOTFOUND_MARITAL_STATU S</td><td>婚姻状况缺失</td></tr><tr><td>NOTFOUND_JOB_INFO</td><td>工作信息缺失</td></tr><tr><td>NOTFOUND_LIVING_INFO</td><td>活体信息缺失</td></tr><tr><td>NOTFOUND_FACE_RECO_INF 0</td><td>人脸识别信息缺失</td></tr><tr><td>NOTFOUND_BANKCARD_INFO</td><td>银行卡信息缺失</td></tr><tr><td>NOTFOUND_CONCAT_INFO</td><td>联系人信息缺失</td></tr><tr><td>NOTFOUND_LIVING_INFO</td><td>活体信息缺失</td></tr><tr><td>NOTFOUND_ID_INFO</td><td>身份证信息缺失</td></tr><tr><td>NOTFOUND_RISK_EXT_INFO</td><td>风险附加信息缺失</td></tr><tr><td>NOTFOUND_ID_IMG</td><td>身份证正反面照片缺失</td></tr><tr><td>NOTFOUND_FACE_IMG</td><td>人脸照片缺失</td></tr><tr><td>NOTFOUND_AGREEMENT</td><td>协议文件缺失</td></tr><tr><td>OTHER</td><td>其他</td></tr></table></body></html>  

# 支用：  

<html><body><table><tr><td>returnCode</td><td>returnDes</td></tr><tr><td>SUCCESS</td><td>成功</td></tr><tr><td>ERROR_IDNO</td><td>身份证号格式错误</td></tr><tr><td>NOTFOUND_FLAG</td><td>标识缺失</td></tr><tr><td>NOTFOUND_APPLYNO</td><td>申请流水号缺失</td></tr><tr><td>NOTFOUND_NAME</td><td>申请人姓名缺失</td></tr><tr><td>NOTFOUND_IDNO</td><td>身份证信息缺失</td></tr><tr><td>NOTFOUND_MOBILE</td><td>手机号或userld缺失</td></tr><tr><td>NOTFOUND_LOAN_USE</td><td>借款用途缺失</td></tr><tr><td>NOTFOUND_REPAY_METHOD</td><td>还款方式缺失</td></tr><tr><td>NOTFOUND_ADDRESS_INFO</td><td>地址信息缺失</td></tr><tr><td>NOTFOUND_EDU_LEVEL</td><td>学历缺失</td></tr><tr><td>NOTFOUND_MARITAL_STATU S</td><td>婚姻状况缺失</td></tr><tr><td>NOTFOUND_JOB_INFO</td><td>工作信息缺失</td></tr><tr><td>NOTFOUND_LIVING_INFO</td><td>活体信息缺失</td></tr><tr><td>NOTFOUND_FACE_RECO_INF 0</td><td>人脸识别信息缺失</td></tr><tr><td>NOTFOUND_BANKCARD_INFO</td><td>银行卡信息缺失</td></tr><tr><td>NOTFOUND_FINANCE_INFO</td><td>资金信息缺失</td></tr><tr><td>NOTFOUND_CONCAT_INFO</td><td>联系人信息缺失</td></tr><tr><td>NOTFOUND_LIVING_INFO</td><td>活体信息缺失</td></tr><tr><td>NOTFOUND_ID_INFO</td><td>身份证信息缺失</td></tr></table></body></html>  

<html><body><table><tr><td>NOTFOUND_RISK_EXT_INFO</td><td>风险附加信息缺失</td></tr><tr><td>NOTFOUND_ID_IMG</td><td>身份证正反面照片缺失</td></tr><tr><td>NOTFOUND_FACE_IMG</td><td>人脸照片缺失</td></tr><tr><td>NOTFOUND_AGREEMENT</td><td>协议文件缺失</td></tr><tr><td>BIZ_NOTFOUND_CREDIT_REC ORD</td><td>未查询到对应授信记录</td></tr><tr><td>OTHER</td><td>其他</td></tr></table></body></html>  
'''


toutiao_document = '''
# 通信协议  

# 报⽂结构  

# 1. 请求公共参数  

1.1. 平台向合作机构发起1.2. 合作机构向平台发起  
2. 响应公共数据  
3. 业务数据  
4. 签名  
5. 示例5.1 明⽂数据5.2 加密业务数据5.3 签名  

# 加密规范  

1. 加密  

# 2. 解密  

# 资⽅需要提供的接⼝  

资质预审1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂  
银⾏卡签约短信发送1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂  
银⾏卡签约短信验证1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂  
银⾏卡切换通知1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂  

# 授信申请  

1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂授信申请结果查询1. 使⽤场景  

# 2. 接⼝说明  

# 3. 参数说明  

# 3.1 请求参数  

# 3.2 响应报⽂  

# 额度查询  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 客户信息更新  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 借款试算  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 借款申请  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 借款结果查询  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 借款详情查询  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 提前结清试算  

1. 使⽤场景  
2. 接⼝说明  
3. 参数说明3.1 请求参数3.2 响应报⽂  

# 还款提交  

1. 使⽤场景  
2. 接⼝说明  

3. 参数说明3.1 请求参数3.2 响应报⽂还款提交结果查询1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂协议列表1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂  
平台提供的接⼝调额调价通知1. 使⽤场景2. 接⼝说明3. 参数说明3.1 请求参数3.2 响应报⽂  
资⽅需要提供的离线⽂件当⽇借款使⽤场景⽂件字段说明当⽇还款使⽤场景⽂件字段说明近期应还⽂件字段说明逾期信息⽂件字段说明  
附录错误码  

# 通信协议  

# 报⽂结构  

报⽂共分三部分组成：公共数据(双向请求、响应)、业务数据、签名。双⽅报⽂均采⽤HTTP POST⽅式提交，报⽂数据以JSON格式写⼊请求Body中。报⽂头信息中Content-Type指定为"text/plain"类型。  

# 1. 请求公共参数  

# 1.1. 平台向合作机构发起  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>merchantld</td><td>string</td><td>是</td><td>合作机构为平台分配的ID</td><td></td></tr><tr><td>transSeqNo</td><td>string</td><td>是</td><td>送入方交易流水号</td><td></td></tr><tr><td>transReqTime</td><td>string</td><td>是</td><td>交易易请求时间</td><td>时间格式 "20060102150405"</td></tr><tr><td>version</td><td>string</td><td>是</td><td>版本号</td><td></td></tr></table></body></html>  

# 1.2. 合作机构向平台发起  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>companyld</td><td>string</td><td>是</td><td>平台为合作机构分配的ID</td><td></td></tr><tr><td>transSeqNo</td><td>string</td><td>是</td><td>送入方交易流水号</td><td></td></tr><tr><td>transReqTime</td><td>string</td><td>是</td><td>交易请求时间</td><td>时间格式 "20060102150405"</td></tr><tr><td>version</td><td>string</td><td>是</td><td>版本号</td><td></td></tr></table></body></html>  

# 2. 响应公共数据  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>code</td><td>string</td><td>是</td><td>返回码</td><td>0:成功1:缺少必要参数2:参数值错误3:加密错误 4:系统内部错误</td></tr><tr><td>msg</td><td>string</td><td>是</td><td>消息</td><td>错误的写明错误原因，成功为success</td></tr><tr><td>transSeqNo</td><td>string</td><td>是</td><td>送入方交易 流水号</td><td></td></tr><tr><td>transReqTime</td><td>string</td><td>是</td><td>交易响应时 间</td><td>时间格式"20060102150405"</td></tr></table></body></html>  

# 3. 业务数据  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必 须</td><td>说明</td><td>备注</td></tr><tr><td>bizContent</td><td>string</td><td>是</td><td>加密后的业务 数据</td><td>对业务数据进行RSA加密，base64(RSA(业务明文 json内容))</td></tr></table></body></html>  

RSA加密过程简要描述：A和B互为接收⽅和送⼊⽅，A⽅⽣成⼀对密钥：私钥（KA）和公钥（KPA），B⽅也⽣成⼀对密钥：（KB）和（KPB），其中（KPA）和（KPB）是公开的。送⼊⽅⽤算法：E=ENC（M，接收⽅公钥）进⾏加密，接收⽅⽤算法：M=DEC（E，接收⽅私钥）进⾏解密，即可得到原⽂。  

其中约定如下：1.RSA密钥采⽤⽤2048位，可以⽤openssl⽣成。2.padding模式采⽤PKCS #1 v1.5 padding  

# 4. 签名  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是 否 必 须</td><td>说备 明</td><td>注</td></tr><tr><td>sign</td><td>string</td><td>是</td><td>签 名</td><td></td></tr><tr><td>将返回值键值对（除去sign）按照字段名的ASCI码从小到大排序（字典序, 其中bizContentd是加密后的数据）组装成一个json格式的字符串，记为 sortedjson字符串，然后MD5(salt+sortedJson+salt)即为结果，结果为16进 制的32位字符，其中salt双方约定.比如sortedJson为(字符串，为了可读性加 入了换行和缩进，实际不需要)</td><td></td><td></td><td></td><td></td></tr></table></body></html>  

"bizContent": "MIIFpAYJKoZC...4sZ5SgCF/nFzx1T2NEI0="   
"merchantId": "N09130100000000",   
"transReqTime": "**************"   
"transSeqNo": "*************"   
"version": "1.0"  

# 5. 示例  

# 5.1 明⽂数据  

"merchantId": "N09130100000000", "transReqTime": "**************", "transSeqNo": "*************", "version": "1.0", "bizContent": "{"name":"测试","idcardno":"530113198710211919","idcardaddr":"上海海市 漕河泾经济开发 区","idcardduration":"20","phone":"***********","bankaccount":"***************","compan yaddr":"上海海市漕河泾经济开发区宜⼭⼭路路988号1901室","contactname":"廉系 仁","contactphone":"***********","creditchecking":1}" }  

# 5.2 加密业务数据  

"merchantId": "N09130100000000", "transReqTime": "**************", "transSeqNo": "*************" "version": "1.0", "bizContent": "MIIFpAYJKoZIhvcNAQcDoIIFlTCC...4sZ5SgCwG057F/nFzx1T2NEI0=" }  

# 5.3 签名  

{ "merchantId": "N09130100000000", "transReqTime": "**************", "transSeqNo": "*************", "version": "1.0", "bizContent": "MIIFpAYJKoZIhvcNAQcDoIIFlTCC...4sZ5SgCwG057F/nFzx1T2NEI0=" "sign:" 098f6bcd4621d373cade4e832627b4f6"   
}  

# 加密规范  

# 1. 加密  

1. 业务数据先json化，并形成json字符串串；  
2. 发送⽅使⽤私钥和接收⽅公钥对json业务数据加密（算法“RSA”，详⻅“业务数据”部分）；  
3. 将密⽂字节转化为base64字符串赋值给bizContent字段；  
4. 把内容进⾏签名，详⻅“签名”部分。  

# 2. 解密  

1. 接收⽅使⽤签名算法进⾏数据校验，详⻅“签名”部分；  
2. 接收⽅使⽤私钥和发送⽅公钥对报⽂bizContent字段（base64解码后的字节码）进⾏解密；  
3. 解密通过后取出明⽂⽂Json字符串串；  
4. 对json字符串json化，并转化为业务对象。  

# 资⽅需要提供的接⼝  

# 资质预审  

# 1. 使⽤场景  

平台向合作机构查询是否允许进件  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>identity_md5</td><td>string</td><td>是</td><td>md5后的身份证号码</td><td></td></tr><tr><td>register_phone_md5</td><td>string</td><td>是</td><td>md5后的注册手机号</td><td></td></tr></table></body></html>  

示例  

{ "identity_md5": "0e123sdsadsadasdasdadxxxxx. "register_phone_md5": "0e123sdsadsadasdasdadxxxxx..   
}  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>预审结果码</td><td>1:允许进件-1:不允许进件</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>拒绝进件原因码</td><td>-1情况下必填；见附录-错误码-预审</td></tr></table></body></html>  

示例  

{ "status": -1, "error_code": "pre_1001"   
}  

# 银⾏卡签约短信发送  

# 1. 使⽤场景  

平台向合作机构提交⽤户四要素，合作机构发送银⾏代扣签约验证码到⽤户⼿机  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户ID</td><td></td></tr><tr><td>identity</td><td>string</td><td>是</td><td>身份证</td><td></td></tr><tr><td>name</td><td>string</td><td>是</td><td>姓名</td><td></td></tr><tr><td>bank_account</td><td> string</td><td>是</td><td>银行卡号</td><td></td></tr><tr><td>bank_name</td><td>string</td><td>是</td><td>银行名称</td><td></td></tr><tr><td>phone</td><td> string</td><td>是</td><td>预留手机号</td><td></td></tr></table></body></html>  

示例  

"account_id": 101,   
"identity":"2102041993909091212",   
"name": "张⼩娴",   
"bank_account": "****************"   
"bank_name":"中国银⾏",   
"phone": "***********"  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>结果码</td><td>1:短信验证码发送成功2:已签约成功无需验证 码-1:短信验证码发送失败</td></tr><tr><td>sms_token</td><td>string</td><td>否</td><td>短信验证码对应 的token</td><td>status=1时必填</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td>status=-1时必填；见附录-错误码-银行卡-发送短验</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

<html><body><table><tr><td colspan="2">{</td></tr><tr><td colspan="2">"status": 1,</td></tr><tr><td colspan="2">"sms_token": "123456",</td></tr><tr><td colspan="2">"error_code": "pre_1001",</td></tr><tr><td colspan="2">"error_msg": ""</td></tr><tr><td colspan="2"></td></tr><tr><td colspan="2">}</td></tr></table></body></html>  

# 银⾏卡签约短信验证  

# 1. 使⽤场景  

平台向合作机构校验银⾏代扣签约验证码，并绑定⽤户银⾏卡  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户唯一ID</td><td></td></tr><tr><td>sms_token</td><td>string</td><td>是</td><td>短信验证码对应的token，即上一接口调用返回的值</td><td></td></tr><tr><td>check_code</td><td>string</td><td>是</td><td>短信验证码</td><td></td></tr></table></body></html>  

示例  

{ "account_id": 101, "sms_token": "123456" "check_code": "1234"   
}  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>结果码</td><td>1:验证成功-1:验证失败</td></tr><tr><td>error_code</td><td>string</td><td>是</td><td>失败原因码</td><td>见附录-错误码-银行卡-验证短验</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

{ "status": 1, "error_code": "pre_1001", "error_msg": "xxxx"   
}  

# 银⾏卡切换通知  

# 1. 使⽤场景  

平台向合作机构通知⽤户默认代扣卡切换  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户ID</td><td></td></tr><tr><td>bank_account</td><td>string</td><td>是</td><td>银行卡号</td><td></td></tr><tr><td>identity</td><td>string</td><td>是</td><td>身份证</td><td></td></tr><tr><td>name</td><td>string</td><td>是</td><td>姓名</td><td></td></tr><tr><td>phone</td><td>string</td><td>是</td><td>预留手机号</td><td></td></tr></table></body></html>  

： 示例  

{ "account_id": 101, "name": "张⼩娴", "identity": 110193199501031400, "phone": "***********", "bank_account": "****************"   
}  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>结果码</td><td>1:切换成功-1:切换失败</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

{ "status": 1, "error_code": "pre_1001", "error_msg":   
}  

# 授信申请  

# 1. 使⽤场景  

平台向合作机构提交⽤户授信申请  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是 否 必 须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户ID</td><td></td></tr><tr><td>credit_trans_no</td><td> string</td><td>是</td><td>额度申 请流水 号</td><td>14位生成时间+4位随机数+2位客户分群+3位预留扩展位。唯一 标识一次授信请求。申请失败或超时，不会更新 credit_trans_no，以credit_trans_no幂等。授信已经通过，在 有效期内，更换credit_trans_no重复发起授信，这种异常case合 作方应返回授信申请失败。授信通过，但已经过有效期，更换</td></tr><tr><td>name</td><td>string</td><td>是</td><td>姓名</td><td>credit_trans_no重新发起授信，合作方应重新授信。</td></tr><tr><td>identity</td><td>string</td><td>是</td><td>身份证 号码</td><td></td></tr><tr><td>phone</td><td>string</td><td>是</td><td>手机号 码</td><td></td></tr><tr><td>idcard_addr</td><td>string</td><td>是</td><td>身份证 地址 (ocr)</td><td></td></tr><tr><td>idcard_start_date</td><td>string</td><td>是</td><td>身份证 有效期 起始时 间 (ocr)</td><td></td></tr><tr><td>idcard_end_date</td><td>string</td><td>是</td><td>身份证 有效期 结束时 间 (ocr)</td><td></td></tr><tr><td>front_img</td><td>string</td><td>是</td><td>身份证 正面</td><td>base64编码</td></tr><tr><td>back_img</td><td> string</td><td>是</td><td>身份证 反面</td><td>base64编码</td></tr><tr><td>live_img</td><td>string</td><td>是</td><td>活体影 像</td><td>base64编码</td></tr></table></body></html>

示例  

"account_id": 1234556,   
"credit_trans_no": "20210611171359123401000"   
"name": "张⼩娴",   
"identity": 110193199501031400,   
"phone": "***********"   
"idcard_addr": "北京市海淀区中关村东路8号"   
"idcard_start_date": "2013-03-11",   
"idcard_end_date":"2023-03-11",   
"front_img":"xxxx"   
"back_img":"xxxx"   
"live_img":"xxxx"  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>结果码</td><td>1:申请成功-1:申请失败</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td>status=-1时必传；见附录-错误码-授信提交</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

{ "status": 1, "error_code": "pre_1001", "error_msg": "xxx"   
}  

# 授信申请结果查询  

# 1. 使⽤场景  

平台向合作机构查询⽤户授信申请结果  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>credit_trans_no</td><td>string</td><td>是</td><td>额度申请流水号</td><td></td></tr></table></body></html>  

示例  

{ "account_id": 1234556, "credit_trans_no": "20210611171359123401000"   
}  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必 须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>结果码</td><td>0:无授信记录1:授信审核中2:授信通过3:授信拒 绝</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因 码</td><td>3情况下必填；见附录-错误码-授信结果</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败原因</td><td></td></tr></table></body></html>  

示例  

{ "status": 1, "error_code": "pre_1001", "error_msg": "pre_1001"   
}  

# 额度查询  

# 1. 使⽤场景  

平台向合作机构查询⽤户额度信息  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr></table></body></html>  

示例示例  

<html><body><table><tr><td></td></tr><tr><td>"account_id": 1234556</td></tr><tr><td></td></tr></table></body></html>  

3.2 响应报⽂   


<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>当前额度 状态</td><td>2:额度正常4:额度过期7:额度冻结8:额度注销</td></tr><tr><td>quota</td><td>int64 是</td><td>是</td><td>额度</td><td></td></tr><tr><td>quota_indate</td><td>string</td><td>是</td><td>额度有效 期</td><td>yyyy-MM-dd hh:mm:ss；如果长期有效，则为 2999-01-0100:00:00</td></tr><tr><td>approval_time</td><td>string</td><td>是</td><td>审批通过 时间</td><td>yyyy-MM-dd hh:mm:ss</td></tr><tr><td>available_quota</td><td>int64</td><td>是</td><td>可用额度</td><td></td></tr><tr><td>daily_interest</td><td> string</td><td>是</td><td>日利率</td><td>以%为单位 (如日利率为0.05%则返回"0.05")</td></tr><tr><td>annual_rate</td><td>string</td><td>是</td><td>年化率</td><td>以%为单位 (如年化率为18%，则返回"18")</td></tr><tr><td>error_code</td><td> string</td><td>否</td><td>失败原因 码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败原因</td><td></td></tr></table></body></html>  

"status": 1,   
"approval_time": "2017-09-12 18:32:04",   
"quota": 10000,   
"quota_indate": "2018-09-12 18:32:04",   
"available_quota": 8000,   
"daily_interest":"0.05",   
"annual_rate":"15.5",   
"error_code":""  

# 客户信息更新  

# 1. 使⽤场景  

平台向合作机构提交客户信息变更  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>3s</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>identity</td><td> string</td><td>是</td><td>身份证号码</td><td></td></tr><tr><td>idcard_addr</td><td>string</td><td>是</td><td>身份证地址 (ocr)</td><td></td></tr><tr><td>idcard_start_date</td><td>string</td><td>是</td><td>身份证有效期起始时间 (ocr)</td><td></td></tr><tr><td>idcard_end_date</td><td>string</td><td>是</td><td>身份证有效期结束时间 (ocr)</td><td></td></tr><tr><td>ocr_authority</td><td>string</td><td>是</td><td>签发机构</td><td></td></tr><tr><td>ocr_gender</td><td>string</td><td>是</td><td>性别</td><td></td></tr><tr><td>ocr_ethnicity</td><td>string</td><td>是</td><td>民族</td><td></td></tr><tr><td>front_img</td><td>string</td><td>否</td><td>身份证正面</td><td>base64编码</td></tr><tr><td>back_img</td><td>string</td><td>否</td><td>身份证反面</td><td>base64编码</td></tr><tr><td>contact_list</td><td>array</td><td>否</td><td>联系人信息列表</td><td>详下</td></tr></table></body></html>

contact_list 数组元素字段组成：  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必 须</td><td>说明</td><td>备注</td></tr><tr><td>contact_name</td><td>string</td><td>是</td><td>姓名</td><td></td></tr><tr><td>contact_phone</td><td> string</td><td>是</td><td>电话</td><td></td></tr><tr><td>contact_relation</td><td>int</td><td>是</td><td>与该联系人的关 系</td><td>1:父母2:配偶3:亲戚4:子女6:朋友7: 同事</td></tr></table></body></html>  

示例  

"account_id": 1234556,   
"identity": 110193199501031400,   
"idcard_addr": "北京市海淀区中关村东路8号",   
"idcard_start_date": "2013-03-11"   
"idcard_end_date": "2023-03-11",   
"ocr_authority": "唐县公安局",   
"ocr_gender": "男",   
"ocr_ethnicity": "汉",   
"front_img": "xxxx",   
"back_img": "xxxx",   
"contact_list" :[   
{ "contact_name": "芮恩特", "contact_phone": "***********", "contact_relation": 1 },{ "contact_name": "张三", "contact_phone": "***********", "contact_relation": 2 }   
]  

# 3.2 响应报⽂  

示例  


<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int64</td><td>是</td><td>结果码</td><td>1:成功-1:失败</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败原因</td><td></td></tr></table></body></html>  

{ "status": 1, "error_code": "pre_1001", "error_msg": "xxx"   
}  

# 借款试算  

# 1. 使⽤场景  

平台向合作机构查询借款试算  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>amount</td><td>int64</td><td>是</td><td>借款金额</td><td></td></tr><tr><td>period</td><td>int</td><td>是</td><td>期数</td><td></td></tr></table></body></html>

示例  

{ "account_id": 1001, "amount": 20000, "period":12   
}  

# 3.2 响应报⽂  

repay_plan格式  


<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>interest</td><td>int64</td><td>是</td><td>总利息</td><td></td></tr><tr><td>initial_repay</td><td>string</td><td>是</td><td>首期还款日</td><td>yyyy-MM-dd hh:mm:ss</td></tr><tr><td>repay_date</td><td>int</td><td>是</td><td>应还日</td><td></td></tr><tr><td>daily_interest</td><td>string</td><td>是</td><td>执行日利率</td><td>以%为单位 (如0.05%，则返回"0.05")</td></tr><tr><td>annual_rate</td><td>string</td><td>是</td><td>执行年化率</td><td>以%为单位 (如年化率为18%，则返回"18")</td></tr><tr><td>repay_plan</td><td> array</td><td>是</td><td>还款计划</td><td>格式见下表</td></tr><tr><td>repay_type</td><td>int</td><td>是</td><td>计息方式</td><td>1等额本息2等额本金3等本等息</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>period</td><td>int</td><td>是</td><td>当前期数</td><td></td></tr><tr><td>amount</td><td>int64</td><td>是</td><td>应还本金</td><td></td></tr><tr><td>interest</td><td>int64</td><td>是</td><td>应还利息</td><td></td></tr><tr><td>repay_date</td><td>string</td><td>是</td><td>当期应还日</td><td>yyyy-MM-dd hh:mm:ss</td></tr></table></body></html>  

示例  

"interest": 1029,   
"initial_repay": "2017-09-13 00:00:00",   
"repay_date": 27,   
"daily_interest": "0.05",   
"repay_plan": [ {  

"period": 1, "amount": 200, "interest": 2.9, "repay_date": "2017-09-13 00:00:00" }, { "period": 2, "amount": 200, "interest": 2.9, "repay_date": "2017-10-13 00:00:00" } ], "repay_type":1, "error_code": "pre_1001", "error_msg": "xxx"  

# 借款申请  

# 1. 使⽤场景  

平台向合作机构提交借款申请  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>借据流水号</td><td></td></tr><tr><td>amount</td><td>int64</td><td>是</td><td>借款金额</td><td></td></tr><tr><td>period</td><td>int</td><td>是</td><td>期数</td><td></td></tr><tr><td>usage</td><td>int</td><td>是</td><td>借款用途</td><td>1房屋装修，3旅游，4教育，9个人日常消费</td></tr><tr><td>bank_account</td><td> string</td><td>是</td><td>银行卡号</td><td></td></tr></table></body></html>  

示例  

"account_id": 1001, "loan_id":"19191", "amount": 2000, "period": 3, "usage": 1, "bank_account": "****************" }  

# 3.2 响应报⽂  

示例  


<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必 须</td><td>说明</td><td>备注</td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借据流水号</td><td></td></tr><tr><td>partner_loan_id</td><td>string</td><td>是</td><td>合作机构借据流 水号</td><td></td></tr><tr><td>status</td><td>int</td><td>是</td><td>申请状态</td><td>1:申请成功-1:申请失败 -3:借据流水号 已存在</td></tr><tr><td>error_code</td><td> string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

"loan_id": "123", "partner_loan_id": "456" "status": 1, "error_code":"fxj3001" "error_msg":"fxj3001"  

# 借款结果查询  

# 1. 使⽤场景  

平台向合作机构查询借款结果  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借据流水号</td><td></td></tr></table></body></html>  

示例  

{ "account_id": 1001, "loan_id":"19191"   
}  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借据流水 号</td><td></td></tr><tr><td>partner_loan_id</td><td>string</td><td>是</td><td>合作机构借据 流水号</td><td></td></tr><tr><td>status</td><td>int</td><td>是</td><td>申请状态</td><td>1:处理中3:放款成功 -1:放款失败 -3:借 据流水号不存在</td></tr><tr><td>req_time</td><td> string</td><td>是</td><td>申请接收时间</td><td></td></tr><tr><td>deal_time</td><td>string</td><td>是</td><td>处理完成时间</td><td></td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td>status=-1必填；见附录-错误码-用信结果</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

{  

"loan_id": "123",   
"partner_loan_id": "456",   
"status": 4,   
"req_time": "2017-09-12 18:32:04",   
"deal_time": "2017-09-12 18:32:05"   
"error_code":"fxj3001",   
"error_msg":"fxj3001"  

# 借款详情查询  

# 1. 使⽤场景  

平台向合作机构查询借款详情  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借款流水号</td><td></td></tr></table></body></html>  

： 示例  

<html><body><table><tr><td colspan="2">{</td></tr><tr><td>"account_id": 1001,</td><td></td></tr><tr><td>"loan_id":"19191"</td><td></td></tr><tr><td>}</td><td></td></tr></table></body></html>  

# 3.2 响应报⽂  

details格式  


<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借款交易流水号</td><td></td></tr><tr><td>partner_loan_id</td><td>string</td><td>是</td><td>合作机构借款交易流水号</td><td></td></tr><tr><td>is_first</td><td>int</td><td>是</td><td>0:首贷1:复贷</td><td></td></tr><tr><td>funder_name</td><td>string</td><td>是</td><td>放款方名称</td><td></td></tr><tr><td>details</td><td>array</td><td>是</td><td>记录详情</td><td>见下表</td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败原因</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>period</td><td>int</td><td>是</td><td>当前期数</td><td></td></tr><tr><td>plan_principal</td><td>int64</td><td>是</td><td>应还本金</td><td></td></tr><tr><td>plan_interest</td><td>int64</td><td>是</td><td>应还利息</td><td></td></tr><tr><td>plan_overdue</td><td>int64</td><td>是</td><td>应还罚息</td><td></td></tr><tr><td>repaid_principal</td><td>int64</td><td>是</td><td>已还本金</td><td></td></tr><tr><td>repaid_interest</td><td>int64</td><td>是</td><td>已还利息</td><td></td></tr><tr><td>repaid_overdue</td><td>int64</td><td>是</td><td>已还罚息</td><td></td></tr><tr><td>remaining_principal</td><td>int64</td><td>是</td><td>本金余额</td><td></td></tr><tr><td>remaining_interest</td><td>int64</td><td>是</td><td>利息余额</td><td></td></tr><tr><td>remaining_overdue</td><td>int64</td><td>是</td><td>罚息余额</td><td></td></tr><tr><td>plan_repay_date</td><td>string</td><td>是</td><td>计划应还日</td><td>格式为yyyy-MM-dd HH:mm:ss</td></tr><tr><td>settled_date</td><td>string</td><td>是</td><td>实际结清/终 止时间</td><td>格式为yyyy-MM-dd HH:mm:ss</td></tr><tr><td>repay_status</td><td>string</td><td>是</td><td>当前状态</td><td>unsettled:未结清 settled:已结清 cancel:停止/取消</td></tr></table></body></html>  

. 示例  

{ "is_first":0,  

"funder_name":"xxx",   
"details":[ { "period":1, "plan_principal":1000, "plan_interest":10, "plan_overdue":0, "repaid_principal":1000, "repaid_interest":10, "repaid_overdue":0, "remaining_principal":0, "remaining_interest":0, "remaining_overdue":0, "plan_repay_date":"2017-09-13 00:00:00", "settled_date":"0000-00-00 00:00:00", "repay_status":"settled" }  

# 提前结清试算  

# 1. 使⽤场景  

平台向合作机构提交提前还款试算  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借款流水号</td><td></td></tr></table></body></html>  

示例  

<html><body><table><tr><td>{</td><td></td></tr><tr><td>"account_id": 1001,</td><td></td></tr><tr><td>"loan_id":"19191"</td><td></td></tr><tr><td>广</td><td></td></tr></table></body></html>  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>amount</td><td>int64</td><td>是</td><td>提前结清本金</td><td></td></tr><tr><td>interest</td><td>int64</td><td>是</td><td>提前结清利息</td><td></td></tr><tr><td>overdue</td><td>int64</td><td>是</td><td>提前结清罚息</td><td></td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

"amount": 2000,   
"interest": 50,   
"service": 50,   
"overdue": 0,   
"error_code":"fxj3001",   
"error_msg":"fxj3001"  

# 还款提交  

# 1. 使⽤场景  

平台向合作机构提交还款  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

示例  


<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借款流水号</td><td></td></tr><tr><td>repay_id</td><td>string</td><td>是</td><td>平台还款流水号</td><td></td></tr><tr><td>type</td><td>int</td><td>是</td><td>还款类型</td><td>1:按期还款 2:提前结清</td></tr><tr><td>period</td><td>int</td><td>是</td><td>还款期次</td><td>提前结清或多期时传0</td></tr><tr><td>bank_account</td><td>string</td><td>是</td><td>扣款银行卡号</td><td></td></tr><tr><td>actual_deduction</td><td>int64</td><td>是</td><td>客户扣款金额</td><td></td></tr><tr><td>repay_account_time</td><td>string</td><td>是</td><td>还款提交时间</td><td>格式为yyyy-MM-dd hh:mm:ss</td></tr></table></body></html>  

"account_id": 1001,   
"loan_id": 3894,   
"repay_id": 1234,   
"type": 1,   
"period": 1,   
"bank_account": *****************,   
"actual_deduction": 100,   
"repay_account_time": "2021-01-01 23:53:39"  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必 须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int</td><td>是</td><td>提交状态</td><td>1:提交成功 2:还款流水号已存在 -1: 提交失败</td></tr><tr><td>partner_repay_id</td><td>string</td><td>是</td><td>合作机构还款流 水号</td><td></td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td></td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

{ "status": 1, "partner_repay_id": "123", "error_code":"fxj3001", "error_msg":"fxj3001"   
}  

# 还款提交结果查询  

# 1. 使⽤场景  

平台向合作机构查询还款进度  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>是</td><td>平台借款流水号</td><td></td></tr><tr><td>repay_id</td><td>string</td><td>是</td><td>平台还款流水号</td><td></td></tr></table></body></html>  

示例  

<html><body><table><tr><td colspan="2">{</td></tr><tr><td colspan="2">"account_id": 1001,</td></tr><tr><td colspan="2">"loan_id": "3894",</td></tr><tr><td colspan="2">"repay_id": "1234"</td></tr><tr><td colspan="2"></td></tr></table></body></html>  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备注</td></tr><tr><td>status</td><td>int</td><td>是</td><td>还款结果</td><td>1:还款成功 2:还款处理中 -1:还款失败 -2:还款请求不存在</td></tr><tr><td>partner_repay_id</td><td>string</td><td>是</td><td>合作机构还款 流水号</td><td></td></tr><tr><td>error_code</td><td>string</td><td>否</td><td>失败原因码</td><td>见附录-错误码-还款结果</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败详细原因</td><td></td></tr></table></body></html>  

示例  

<html><body><table><tr><td colspan="2"></td></tr><tr><td colspan="2">"status": 1,</td></tr><tr><td colspan="2">"partner_repay_id": "123",</td></tr><tr><td colspan="2">"error_code":"fxj3001",</td></tr><tr><td colspan="2"></td></tr><tr><td colspan="2">"error_msg":"fxj3001"</td></tr><tr><td colspan="2">了</td></tr></table></body></html>  

# 协议列表  

# 1. 使⽤场景  

平台向合作机构查询⽤户签署协议  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

content格式  


<html><body><table><tr><td>参数名</td><td>类型</td><td>是 否 必 须</td><td>说明</td><td>备注</td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户id</td><td></td></tr><tr><td>loan_id</td><td>string</td><td>否</td><td>平台借 款流水 号</td><td>如果传入loan_id,则获取的为该借据相关的协议。借款试 算无loan_id</td></tr><tr><td>content</td><td>content</td><td>是</td><td>合同内 容</td><td>在借款试算、授信等场景用于内容填充</td></tr><tr><td>type</td><td>string</td><td>否</td><td>协议类 型</td><td>pre:借款试算loan:借款详情 credit:授信 bind:绑 卡如果为空则查询全部类型协议</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必 须</td><td>说明</td><td>备注</td></tr><tr><td>name</td><td>string</td><td>否</td><td>姓名</td><td>授信时必填</td></tr><tr><td>identity</td><td>string</td><td>否</td><td>身份证号 码</td><td>授信时必填</td></tr><tr><td>phone</td><td>string</td><td>否</td><td>手机号码</td><td>授信时必填</td></tr><tr><td>amount</td><td>int64</td><td>否</td><td>借款金额</td><td>借款试算时必填</td></tr><tr><td>period</td><td>int</td><td>否</td><td>期数</td><td>借款试算时必填</td></tr><tr><td>usage</td><td>int</td><td>否</td><td>借款用途</td><td>1房屋装修，3旅游，4教育，9个人日常消费；借款试 算时必填</td></tr><tr><td>bank_account</td><td>string</td><td>否</td><td>银行卡号</td><td>借款试算时必填</td></tr></table></body></html>  

示例  

"account_id": 1001,   
"loan_id": "3894",   
"content": { "name": "张⼩娴", "identity": "110193199501031400",  

"phone": "***********", "bank_account": "*****************", "amount": 2000, "period": 3, "usage": 1 }, "type": "loan" }  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>contracts</td><td>array</td><td>是</td><td>合同列表</td><td></td></tr><tr><td>contracts[].url</td><td>string</td><td>是</td><td>合同地址</td><td></td></tr><tr><td>contracts[].name</td><td>string</td><td>是</td><td>合同名称</td><td></td></tr><tr><td>contracts[].type</td><td>string</td><td>是</td><td>合同类型</td><td></td></tr></table></body></html>  

示例  

{ "contracts":[{ "url":"http://xxxxxxxx" "name":"借款协议", "type":"loan" },{ "url":"http://xxxxxxxx" "name":"绑卡协议", "type":"bind" }]   
}  

# 平台提供的接⼝  

# 调额调价通知  

# 1. 使⽤场景  

机构合作⽅通知平台关于⽤户额度申请结果的回调，更新⽤户信息  

# 2. 接⼝说明  

<html><body><table><tr><td></td><td></td></tr><tr><td>请求地址</td><td></td></tr><tr><td>请求方式</td><td>POST</td></tr><tr><td>数据类型</td><td>JSON</td></tr><tr><td>性能要求</td><td>100ms</td></tr></table></body></html>  

# 3. 参数说明  

# 3.1 请求参数  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否 必须</td><td>说明</td><td>备 注</td></tr><tr><td>status</td><td>int</td><td>是</td><td>2:额度正常4:额度过期7:额度冻结8:额度注销</td><td></td></tr><tr><td>account_id</td><td>int64</td><td>是</td><td>账户唯一ID</td><td></td></tr><tr><td>approval_time</td><td>string</td><td>是</td><td>审批时间</td><td></td></tr><tr><td>quota</td><td>int64</td><td>是</td><td>审批额度</td><td></td></tr><tr><td>available_quota</td><td>int64</td><td>是</td><td>可用额度</td><td></td></tr><tr><td>quota_indate</td><td>string</td><td>是</td><td>额度有效期，格式为yyyy-MM-ddhh:mm:ss；如果长期有 效，则为2999-01-0100:00:00</td><td></td></tr><tr><td>daily_interest</td><td>string</td><td>是</td><td>日利率，以%为单位 (如日利率为0.05%则返回"0.05")</td><td></td></tr><tr><td>annual_rate</td><td>string</td><td>是</td><td>年化率，以%为单位 (如年化率为18%，则返回"18")</td><td></td></tr></table></body></html>

示例  

"status":1,   
"account_id": 1001,   
"approval_time": "2017-09-12 18:32:04",   
"quota": 10000,   
"available_quota": 10000,   
"quota_indate": "2018-09-12 18:32:04"   
"daily_interest":"0.05",   
"annual_rate":"18"  

# 3.2 响应报⽂  

<html><body><table><tr><td>参数名</td><td>类型</td><td>是否必须</td><td>说明</td><td>备注</td></tr><tr><td>error_code</td><td>string</td><td>是</td><td>失败原因码</td><td>见附录-错误码-授信</td></tr><tr><td>error_msg</td><td>string</td><td>否</td><td>失败原因</td><td></td></tr></table></body></html>  

示例  

{ "error_code":"fxj3001", "error_msg":"fxj3001"   
}  

# 资⽅需要提供的离线⽂件  

# 当⽇借款  

# 使⽤场景  

合作⽅D+1向平台提供D⽇借款数据，⽂件名为payorderyyyyMMdd.txt如:ttpayorder20180220.txt  

# ⽂件字段说明  

<html><body><table><tr><td>文件头记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>0</td></tr><tr><td>文件生成时间</td><td>格式为YYYYMMDDHHMMSS</td></tr><tr><td>总笔数</td><td>记录明细数据行数</td></tr><tr><td>总金额</td><td>记录明细数据金额总和 (以分为单位)</td></tr></table></body></html>  

<html><body><table><tr><td>记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>1</td></tr><tr><td>账户ID</td><td>平台账户ID</td></tr><tr><td>借据ID</td><td>平台借据流水号loan_id</td></tr><tr><td>合作方借据ID</td><td>合作机构借据流水号partner_loan_id</td></tr><tr><td>借款金额</td><td>以分为单位</td></tr><tr><td>借款时间</td><td>格式为YYYYMMDDHHMMSS,例如：20180220091900</td></tr><tr><td>是否首贷</td><td>0:首贷 复贷：1</td></tr><tr><td>放款方名称</td><td></td></tr></table></body></html>  

示例：  

0\t20180221061000\t2\t200000   
1\t119\t1091\t201802201193\t100000\t20180220091900   
1\t112\t1092\t201802201210\t100000\t20180220102100  

示例中'\t'均以⽂本显示，仅为便于理解。  

# 当⽇还款  

# 使⽤场景  

合作⽅D+1向平台提供D⽇还款数据，⽂件名为ttrepayorderyyyyMMdd.txt，如repayorder20180220.txt  

⽂件字段说明  


<html><body><table><tr><td>文件头记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>0</td></tr><tr><td>文件生成时间</td><td>格式为YYYYMMDDHHMMSS</td></tr><tr><td>总笔数</td><td>记录明细数据行数</td></tr><tr><td>总金额</td><td>记录明细数据金额总和 (以分为单位)</td></tr></table></body></html>  

<html><body><table><tr><td>记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>1</td></tr><tr><td>账户ID</td><td>平台用户ID</td></tr><tr><td>借据ID</td><td>平台借款流水号loan_id</td></tr><tr><td>还款ID</td><td>平台还款流水号repay_id，非平台主动还款则该字段为空</td></tr><tr><td>合作方借据ID</td><td>合作机构借款流水号partner_loan_id</td></tr><tr><td>合作方还款ID</td><td>合作机构还款流水号partner_repay_id</td></tr><tr><td>还款总金额</td><td>以分为单位</td></tr><tr><td>期次</td><td>扣款期数</td></tr><tr><td>期次还款金额</td><td>以分为单位</td></tr><tr><td>期次本金</td><td>以分为单位</td></tr><tr><td>期次利息</td><td>以分为单位</td></tr><tr><td>期次罚息</td><td>以分为单位</td></tr><tr><td>还款类型</td><td>1、平台主动还款2、非平台主动还款</td></tr><tr><td>还款时间</td><td>格式为YYYYMMDDHHMMSS,例如：20180220091900</td></tr></table></body></html>  

示例：  

0\t20180221061000\t1\t100150   
1\t119\t1999\t1091\t201802201193\t100150\t100000\t150\t0\t0\t1\t20180220091900  

示例中'\t'均以⽂本显示，仅为便于理解。  

# 近期应还  

合作⽅向平台提供近4天⽤户应还信息，便于平台向⽤户发送通知提醒还款。⽂件类型名：repayremindyyyyMMdd.txt（例如:repayremind20180220.txt）  

⽂件字段说明  


<html><body><table><tr><td>文件头记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>0</td></tr><tr><td>文件生成时间</td><td>格式为YYYYMMDDHHMMSS</td></tr><tr><td>总应还人数</td><td>记录明细数据行数</td></tr><tr><td>总应还金额</td><td>记录明细数据金额总和 (以分为单位)</td></tr></table></body></html>  

<html><body><table><tr><td>记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>1</td></tr><tr><td>账户ID</td><td>平台账户ID</td></tr><tr><td>借据ID</td><td>平台借据流水号loan_id</td></tr><tr><td>合作机构借据ID</td><td>合作机构借据流水号partner_loan_id</td></tr><tr><td>应还金额</td><td>以分为单位</td></tr><tr><td>应还本金</td><td>以分为单位</td></tr><tr><td>应还利息</td><td>以分为单位</td></tr><tr><td>应还罚息</td><td>以分为单位</td></tr><tr><td>应还日期</td><td>格式为YYYYMMDD</td></tr></table></body></html>  

示例：  

0\t20180221061000\t1\t101500   
1\t119\t1091\t201802201193\t101500\t100000\t1500\t0\t0\t20180220  

示例中'\t'均以⽂本显示，仅为便于理解  

# 逾期信息  

合作机构向平台提供当前所有逾期的⽤户数据。若同⽤户存在多笔借据逾期，则分多⾏展示。⽂件类型名：overdueyyyyMMdd.txt（例如:ttoverdue20180220.txt）  

⽂件字段说明  


<html><body><table><tr><td>文件头记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>0</td></tr><tr><td>文件生成时间</td><td>格式为YYYYMMDDHHMMSS</td></tr><tr><td>总逾期人数</td><td>记录明细数据行数</td></tr><tr><td>总逾期金额</td><td>记录明细数据金额总和 (以分为单位)</td></tr><tr><td>总在贷人数</td><td></td></tr><tr><td>总在贷金额</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>记录字段名</td><td>说明</td></tr><tr><td>记录类型</td><td>1</td></tr><tr><td>账户ID</td><td>平台账户ID</td></tr><tr><td>逾期借据ID</td><td>平台借据流水号loan_id</td></tr><tr><td>逾期合作机构借据ID</td><td>合作机构借据流水号partner_loan_id</td></tr><tr><td>总金额</td><td>以分为单位</td></tr><tr><td>本金</td><td>以分为单位</td></tr><tr><td>利息</td><td>以分为单位</td></tr><tr><td>罚息</td><td>以分为单位</td></tr><tr><td>逾期时间</td><td>当期逾期的开始日期 (格式为YYYYMMDD)</td></tr><tr><td>已逾期天数</td><td>当前逾期天数</td></tr></table></body></html>  

示例：  

0\t20180221061000\t1\t103000   
1\t119\t1091\t201802201193\t103000\t100000\t1500\t1500\t0\t20180220\t3  

# 附录  

错误码  


<html><body><table><tr><td>业务</td><td>错误码</td><td>错误原因</td><td>备注</td></tr><tr><td>预审</td><td>pre_1001</td><td>黑名单</td><td></td></tr><tr><td>预审</td><td>pre_1002</td><td>已授信经营客户</td><td></td></tr><tr><td>预审</td><td>pre_1003</td><td>授信拒绝管控客户</td><td></td></tr><tr><td>授信提交</td><td>apply_1001</td><td>重复申请</td><td></td></tr><tr><td>授信提交</td><td>apply_1999</td><td>其他</td><td></td></tr><tr><td>授信结果</td><td>apply_2001</td><td>黑名单</td><td></td></tr><tr><td>授信结果</td><td>apply_2002</td><td>多头</td><td></td></tr><tr><td>授信结果</td><td>apply_2003</td><td>欺诈</td><td></td></tr><tr><td>授信结果</td><td>apply_2004</td><td>征信</td><td></td></tr><tr><td>授信结果</td><td>apply_2999</td><td>其他</td><td></td></tr><tr><td>用信结果</td><td>loan_2001</td><td>黑名单</td><td></td></tr><tr><td>用信结果</td><td>loan_2002</td><td>多头</td><td></td></tr><tr><td>用信结果</td><td>loan_2003</td><td>欺诈</td><td></td></tr><tr><td>用信结果</td><td>loan_2004</td><td>征信</td><td></td></tr><tr><td>用信结果</td><td>loan_2005</td><td>卡片状态异常</td><td></td></tr><tr><td>用信结果</td><td>loan_2006</td><td>预留手机号错误</td><td></td></tr><tr><td>用信结果</td><td>loan_2007</td><td>四要素认证不通过</td><td></td></tr><tr><td>用信结果</td><td>loan_2008</td><td>银行卡不支持</td><td></td></tr><tr><td>用信结果</td><td>loan_2999</td><td>其他</td><td></td></tr><tr><td>还款结果</td><td>repay_2001</td><td>余额不足</td><td></td></tr><tr><td>还款结果</td><td>repay_2002</td><td>卡片状态异常</td><td></td></tr><tr><td>还款结果</td><td>repay_2003</td><td>预留手机号错误</td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td>还款结果</td><td>repay_2004</td><td>四要素认证不通过</td><td></td></tr><tr><td>还款结果</td><td>repay_2005</td><td>银行卡不支持</td><td></td></tr><tr><td>还款结果</td><td>repay_2006</td><td>卡片已解约/签约关系异常</td><td></td></tr><tr><td>还款结果</td><td>repay_2999</td><td>其他</td><td></td></tr><tr><td>银行卡-发送短验</td><td>card_1001</td><td>卡片状态异常</td><td></td></tr><tr><td>银行卡-发送短验</td><td>card_1002</td><td>预留手机号错误</td><td></td></tr><tr><td>银行卡-发送短验</td><td>card_1003</td><td>四要素认证不通过</td><td></td></tr><tr><td>银行卡-发送短验</td><td>card_1004</td><td>银行卡不支持</td><td></td></tr><tr><td>银行卡-验证短验</td><td>card_2001</td><td>验证码错误</td><td></td></tr><tr><td>银行卡-验证短验</td><td>card_2002</td><td>验证码过期</td><td></td></tr></table></body></html>  
'''


huawei_document = '''
# 借贷接口规范 V2  

Huawei Technologies Co., Ltd. All rights reserved.  

# 修订记录  

<html><body><table><tr><td>日期</td><td>修 订 版 本</td><td>修改描述</td><td>作者</td></tr><tr><td>2021-05- 17</td><td>1.0</td><td>初稿完成</td><td>设计：侯现成/吴蛟 评审：袁非凡</td></tr><tr><td>2021-06- 08</td><td>1.0</td><td>创建订单0203和订单确认0204接口支持异步处理</td><td>侯现成</td></tr><tr><td>2021-06- 08</td><td>1.0</td><td>增加开具证明发送0108接口</td><td>侯现成</td></tr><tr><td>2021-06- 13</td><td>1.0</td><td>增加用户资料不补充单独接口</td><td>侯现成</td></tr><tr><td>2021-06- 13</td><td>1.0</td><td>修改用信流程接口</td><td>侯现成</td></tr><tr><td>2021-06- 16</td><td>1.0</td><td>增加销户接口</td><td>侯现成</td></tr><tr><td>2021-06- 17</td><td>1.0</td><td>增加营销信息回调发送接口 授信结果通知增加身份证号 用户信息增加收入参数</td><td>侯现成</td></tr><tr><td>2021-06- 18</td><td>1.0</td><td>异步查询接口0104、0206、0304 增加msgld，用于标识查询哪次请求</td><td>侯现成</td></tr><tr><td>2021-06- 19</td><td>1.0</td><td>增加身份证是否需要重传接口0115 (返回的补录资料列表包含身份 证影像，华为主动去查一次是否已经上传过，兼容百度平台) 增加借钱和还钱记录详情查询接口0209、0306</td><td>侯现成</td></tr><tr><td>2021-06- 22</td><td>1.0</td><td>Cardlnfo对象中cardNo参数改为选 CreditSignlnfo 对象中changelnfo参数修改为changelnfos 授信结果通知请求去掉身份证号</td><td>侯现成</td></tr><tr><td>2021-06- 23</td><td>1.0</td><td>签约关系增加总逾期金额overdueAmount有逾期时返回</td><td>侯现成</td></tr></table></body></html>  

<html><body><table><tr><td>日期</td><td>修 订 版 本</td><td>修改描述</td><td>作者</td></tr><tr><td>2021-06- 24</td><td>1.0</td><td>借款订单 BorrowOrderInfo字段增加必选和可选说明 查询借钱记录接口0207响应增加可选参数： firstFqRepayAmount 首期应还金额在查询未完成确认订单 时返回提示用户</td><td>侯现成</td></tr><tr><td>2021-06- 29</td><td>1.0</td><td>创建订单接口0203请求参数oriOrderld为可选 确认还款接口0303请求参数oriOrderld为可选</td><td>侯现成</td></tr><tr><td></td><td></td><td>查询用信申请结果0206请求参数msgld为可选</td><td>侯现成</td></tr><tr><td></td><td></td><td>查询还款结果0304请求参数msgld为可选 RepayOrderlnfo 对象 repayOrderDetails参数增加可选说明</td><td>侯现成</td></tr><tr><td></td><td></td><td>RepayFqlnfo 对象 reamark 修改为 remark</td><td>侯现成</td></tr><tr><td>2021-07- 02</td><td>1.0</td><td>Risklnfo对象 删除 locallp,deviceUA,phonebook deviceCode改为 deviceld debugStatus改为debugState 以下字段改为必传 deviceld,positionlnfo,imsi,ipAddress,isRoot,isVirtual,simSeria</td><td>陈志江</td></tr><tr><td>2021-07- 05</td><td>1.0 删除无用参数：</td><td>Num 创建订单接口0203请求参数删除oriOrderld 确认还款接口0303请求参数删除oriOrderld 查询用信申请结果接口0206请求参数删除msgld 查询还款结果接口0304请求参数删除msgld 通知还款结果接口0606请求参数删除oriRepayOrderld 通知借贷结果接口0501请求参数删除oriOrderld</td><td>侯现成</td></tr><tr><td></td><td></td><td>RepayPlanlnfo对象 还款计划状态 status增加3：自动还款失败</td><td>侯现成</td></tr><tr><td></td><td></td><td>通知借贷结果接口0501请求参数增加BorrowOrderlnfo</td><td>侯现成</td></tr><tr><td></td><td></td><td>通知还款结果接口0606请求参数增加RepayOrderlnfo</td><td>侯现成</td></tr></table></body></html>  

<html><body><table><tr><td>日期</td><td>修 订 版 本</td><td>修改描述</td><td>作者</td></tr><tr><td></td><td>回 fqNum 分期数 usage 资金用途</td><td>BorrowOrderInfo对象。以下对象需要改成申请成功或失败后都返 borrowDate 借款时间 principalAmt 借款本金 repayType还款类型</td><td>潘海龙</td></tr><tr><td></td><td></td><td>bindCardld 收款卡SP侧绑定ID 用信创建订单接口0203 请求参数：添加必传项bindCardld 响应参数：删除oriOrderld</td><td>陈志江</td></tr><tr><td>2021-07- 09</td><td></td><td>BorrowOrderlnfo对象interestAmt改为非必传 0501-通知借贷结果（order.notify）：通知类型notifyType 值改 为03：放款成功04：放款失败</td><td>陈志江</td></tr><tr><td>2021-07- 12</td><td></td><td>营销通知接口新增 templateld 字段 新增IM消息模板</td><td>卢义飞</td></tr><tr><td></td><td></td><td>查询借钱记录 (borrow.order.query) 请求data数据中 type为2时改为：返回未申请完成订单</td><td>陈志江</td></tr><tr><td></td><td></td><td>(BorrowOrderlnfo的 status为00、01、02的订单）。 CreditSignlnfo 的overdueAmount 长度改为20</td><td>蔡永清</td></tr><tr><td></td><td></td><td>account.cancel.check 销户检查接口请求去除参数 cancelReason，响应去除参数reasonCode</td><td>夏圣涵</td></tr><tr><td></td><td></td><td>提前还款试算接口0302请求增加参数type还款类型字段 确认还款接口0303请求增加参数type还款类型字段</td><td>屠春晓</td></tr><tr><td></td><td></td><td>用户自动还款设置接口0404响应增加参数autoRepay是否开启自 动还款，如果请求只携带accountNo，需返回autoRepay</td><td>侯现成</td></tr><tr><td>2021-07- 13</td><td></td><td>0602-通知还款结果（repay.notify）接口删除 notifyType（通知类|刘定奇 型）字段 0304-查询还款结果(repay.status.query）响应消息status 为 SUCCESS或FAILED时，需要带上repayOrderInfo</td><td></td></tr><tr><td>2021-07- 14</td><td></td><td>营销通知删除通用模板</td><td>卢义飞</td></tr></table></body></html>  

<html><body><table><tr><td>日期修</td><td>订 版 本</td><td>修改描述 作者</td></tr><tr><td>2021-07- 16</td><td>试算、试算接口响应中为非必传</td><td>Risklnfo 对象：simSerialNum、imsi字段改为非必传 陈志江 RepayPlanlnfo 对象：borrowOrderld、status，添加备注：默认</td></tr><tr><td>2021-07- 20</td><td></td><td>repay.order.query 接口queryDate新增按年查询 卢义飞 promotion.notify接口支持 SMS和IM类型</td></tr><tr><td></td><td>account.lifecycle接口返回结果中 isAllowedCancel，reason，reasonCode去除。</td><td>潘海龙</td></tr></table></body></html>  

<html><body><table><tr><td>2021-07- 26</td><td>type优惠券类型</td><td>RepayOrderlnfo新增以下字段 bankCardNo还款银行卡号 bankName还款银行卡银行名称 CouponInfo 新增以下字段 promotinoContent营销描述</td><td>卢义飞</td></tr><tr><td>2021-07- 28</td><td>还款</td><td>RepayOrderlnfo.repayType修改入参类型，新增还款日还款及提前</td><td>刘定奇</td></tr><tr><td>2021-07- 28</td><td></td><td>credit.confirm接口新增可选入参Facelmagelnfo (活体信息)</td><td>刘定奇</td></tr><tr><td>2021-07- 29</td><td></td><td>Borrow.confirm接口新增可选入参Userlnfo（用户信息）百度需要 3.1.3 章节添加错误码定义</td><td>潘海龙</td></tr><tr><td></td><td></td><td>0201-默认试算接口 响应：usage、bindCardld、transParty改非必传 0202-试算接口 请求：usage、bindCardld改非必传 响应:usage、bindCardld、transParty改非必传 0207-查询借钱记录接口 查询类型type为0时：改为“未还清(包括申请成功待放款的单子)"</td><td>陈志江</td></tr><tr><td>2021-07- 30</td><td></td><td>0104-授信结果查询接口，msgType 增加account.cancel类型，用 于0114销户超时异常场景时，查询销户结果</td><td>侯现成</td></tr><tr><td></td><td></td><td>CouponInfo优惠券，增加类型参数，支持会员类型的优惠券 type String 2C优惠券类型：1会员券，2免息折扣券 (不传默认为免息折扣券) subType String 20C 券子类型: huawei_music:华为音乐 huawei_cloud:华为云空间 huawei_video:华为视频 voucherCode String 32C会员券需返回兑换码</td><td>侯现成</td></tr><tr><td></td><td></td><td>新增7.8 章节-查询协议列表接口</td><td>屠春晓</td></tr><tr><td>2021-08- 04</td><td></td><td>RepayTypelnfo还款方式宣传信息 增加非必传字段：fqNums (支持的分期数)</td><td>陈志江</td></tr></table></body></html>  

<html><body><table><tr><td>2021-08- 04</td><td></td><td>添加错误码：E070103 错误场景：用户同一天用同一个邮箱(已结清的借据相同）开具结清证 明</td><td>陈志江</td></tr><tr><td>2021-08- 04</td><td></td><td>BorrowOrderlnfo中 repayStatus还款计划状态为 1：正常还款未还清 (包含刚放款成功) 4：借款请求受理中 (包含待放款状态)</td><td>陈志江</td></tr><tr><td></td><td></td><td>RepayPlanlnfo 添加字段：couponNum 优惠券号、</td><td>刘定奇</td></tr><tr><td>2021-08- 05</td><td></td><td>RepayOrderInfo 修改字段：repayDate精确到时分秒 RepayOrderDetail新增字段：punishlnterest罚息</td><td>卢义飞</td></tr><tr><td></td><td></td><td>查询月账单/所有借贷(repay.plan.query）接口：响应字段totalOrd erCnt、totalPrincipalAmt、curOrderCnt、curRepayAmt补充 描述并补充响应样例数据 RepayOrderDetail新增字段：punishlnterest 罚息 overdueDays 逾期天数 RepayPlanlnfo 新增字段：punishlnterest 罚息 提前还款试算（repay.trial）接口响应参数增加 repayReduceAmt 本期减免金额</td><td>刘定奇</td></tr><tr><td>2021-08- 07</td><td></td><td>公共错误码： 删除：活体验证失败错误场景 添加：E010104 准入时手机号已注册错误 E050116 身份证件和实名信息不匹配 E050117人脸和身份信息不匹配 E050105 有申请中的订单 BorrowOrderlnfo 添加couponNum 优惠券号（用信申请时使用了优</td><td>陈志江</td></tr><tr><td>2021-08- 09</td><td>销时间</td><td>惠券场景，之后的流程都须返回) Couponlnfo 优惠券新增字段：writeOffDate String 32C核</td><td>李振伟</td></tr><tr><td>2021-08- 12</td><td></td><td>增加0801客服链接 sessionld 获取接口 (一期不做)</td><td>侯现成</td></tr><tr><td></td><td></td><td>0203创建订单接口响应中BorrowOrderlnfo增加参数返回说明 0204订单确认接口响应中 BorrowOrderlnfo 增加参数返回说明 0205订单二次确认接口响应中BorrowOrderlnfo 增加参数返回说明 0206用信结果查询接口响应中BorrowOrderlnfo增加参数返回说明 0207查询借钱记录接口响应中BorrowOrderlnfo 增加参数返回说明</td><td>侯现成</td></tr><tr><td></td><td></td><td>0209查询借款详情接口响应中增加 BorrowOrderlnfo 参数并做返回 说明</td><td>侯现成</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td>RepayOrderlnfo 对象中bindCardld 设置为可选 RepayOrderDetail对象中增加可选参数 bindCardld、bankCardNo、bankName 并做返回说明 1:repay.plan.query 查询月账单接口里的curReplayPlans*本期账</td><td>侯现成</td></tr><tr><td>2021-08- 14</td><td>5：禁还</td><td>单的还款计划列表里status是否已还清0：已还清1：正常未还清2: 已逾期3：自动还款失败 需加一个类型4：禁还 2：repay.plan.query 查询月账单接口里的borrowOrderlnfos*借款 记录的repayStatus 字段，（申请成功时，还款计划状态1:正常还 款,未还清2:已还清3:已逾期4:借款请求受理中），需加一个类型 3：borrow.record.query 借钱记录查询接口里的 borrowOrderlnfos*借款记录的 repayStatus 字段，（申请成功时， 还款计划状态1：正常还款,未还清2:已还清3：已逾期4:借款请求受 理中），需加一个类型5：禁还</td><td>刘定奇</td></tr><tr><td></td><td>月账单响应中添加</td><td>准入接口creditScore 信用分字段长度改为512。内容为json 字符串 风控信息中的positionlnfo字段长度32改64 个人资料补充：家庭住址的省、市、区信息改非必传 添加错误码 E060020借款正在还款处理中，不可再次发起还款</td><td>陈志江</td></tr><tr><td></td><td>repayDate dd dd</td><td>本期还款日、本期可还日两个字段 String 16M 本期还款日，格式yyyy-mm- canRepayDate String 16C本期可还日,格式yyyy-mm- 月账单接口：响应的样例数据删除，注意的三点信息删除第一点</td><td>刘定奇</td></tr><tr><td>2021-08- 20</td><td></td><td>追加0106,0107证明接口说明： 0106 用户开具证明接口，当remainTimes没有剩余次数的时候，不 进行报错处理，正常返回，样例：remainTimes:5|0 0107发送开具证明接口，当remainTimes没有剩余次数的时候，需 要进行报错处理。错误信息：开具证明次数超上限</td><td>潘海龙</td></tr><tr><td>2021-09- 06</td><td></td><td>通知借贷结果（order.notify）需要支持用信成功，用信失败场景，进「潘海龙 行回调通知。追加notifyType 值如下: 01 (用信成功) 02 (用信失败)</td><td></td></tr><tr><td>2021-10- 12</td><td></td><td>Protocollnfo 中 protocolLink 长度限制改为4096。 3.1报文结构中的data 字段长度由4098改为4000000。 添加手机号修改回调：8.70607-手机号修改通知。 (phoneno.change.notify)</td><td>陈志江</td></tr></table></body></html>  

<html><body><table><tr><td>2021-10- 15</td><td></td><td>RepayPlanlnfo（还款计划信息）： 1、增加本期总待还金额leftRepayTotalAmt 2、本金、利息、优惠、罚息字段做了场景的补充说明。</td><td>陈志江</td></tr><tr><td></td><td></td><td>创建订单（borrow.apply）接口 增加「是否确认信息共享授权」字段:confirmlnformationShare</td><td>陈志江</td></tr><tr><td>2021-10- 20</td><td></td><td>新增销户回调通知 (account.cancel.notify)</td><td>潘海龙</td></tr><tr><td>2021-11- 03</td><td></td><td>百度 borrow.apply,credit.confirm,borrow.confirm,borrow.offer.confi rm接口追加 creditScore信用分入参</td><td>潘海龙</td></tr><tr><td>2021-11- 18</td><td></td><td>Protocollnfo 协议信息中去除protocolContent字段</td><td>陈志江</td></tr><tr><td>2021-11- 25</td><td></td><td>新增身份证上传错误场景错误码和提示语 您上传的图片中不包含身份证身份证影像，请重新上传 E011005 您上传的身份证影像模糊，请重新上传E011006 您上传的身份证影像关键字段不清晰，请重新上传 E011007 您上传的身份证影像亮度过低，请重新上传 E011008 您上传的身份证影像为复印件，请重新上传 E011009 您上传的身份证影像为临时身份证，请上传正式身份证影像E011010 您上传的身份证影像为翻拍，请重新上传E011011 需要您上传身份证影像的原始照片，请重新上传 E011012 您上传的照片不是身份证影像，请重新上传 E011013 您的身份证影像正反面颠倒，请重新上传E011014</td><td>鲍正杰</td></tr></table></body></html>  

<html><body><table><tr><td>2021-12- 25</td><td>添加字段 transPart 4、0302-还款试算 (repay.trial) punishlnterest curReplayPlans 5、0116 -短信发送 (otp.apply) otpType 更换枚举用信：borrowApplySP</td><td>2、0301、0302接口字段新增 3、0301-查询月账单/所有借贷 (repay.plan.query) y、balanceEnough、fistOverdueDate、overdueOrderlnfos 6、0102-授信检查</td></tr><tr><td>2022-01- 12</td><td></td><td>1、0104、0603接口入参msgld 修改为creditMsgld 2、0102、0103接口新增入参字段hwEmployeeTag 1、0301接口查询月账单</td></tr><tr><td>2022-01- 06</td><td>响应：repayDate本期还款日改为非必传 canRepayDate本期可还日改为非必传 2、0403-绑卡确认 (bindcard.confirm) 请求增加 resign 是否补签约 3、0405-绑卡查询 (bindcard.query 请求增加 repayTrialInfos还款订单信息 (用于查询是否需要补签约) 响应CardInfo(绑卡信息）增加needResign 是否需要补签约 4、0201默认试算、0202借贷试算、0203创建订单接口 请求：repayType 添加枚举3：等本等息 5、3.2.2公共数据对象 RepayTypelnfo（还款方式宣传信息）、BorrowOrderlnfo（借据信 息） repayType 添加枚举3：等本等息 Cardlnfo（绑卡信息）增加needResign 是否需要补签约 6、0403-绑卡确认 (bindcard.confirm) 请求增加cardNo 卡号 7、公共错误码新增 E050118有放款中的订单</td><td></td></tr></table></body></html>  

<html><body><table><tr><td rowspan="18">2022-01- 07 约) 1、0103-授信确认接口(credit.confirm) 请求增加cardNo卡号</td><td colspan="4">请求删除resign 是否补签约 2、0405-绑卡查询 (bindcard.query 请求：删除repayTriallnfos还款订单信息(用于查询是否需要补签</td><td colspan="3">响应：Cardlnfo（绑卡信息）删除 needResign 是否需要补签约</td></tr><tr><td>3、0302-提前还款试算 (repay.trial) 请求：增加 bindCardld</td><td>String</td><td>32</td><td></td><td></td><td></td><td></td></tr><tr><td>响应：增加</td><td></td><td></td><td></td><td>M</td><td></td><td>收款卡</td></tr><tr><td></td><td></td><td>String</td><td>64</td><td>C</td><td></td><td>试算流水号</td></tr><tr><td>repayTrialld needResign</td><td></td><td>String</td><td>1</td><td>C</td><td></td><td>是否要重新签约</td></tr><tr><td>4、新增 0116 -短信发送 (otp.apply)</td><td></td><td></td><td></td><td></td><td>1:需要 要</td><td>0、不传：不需</td></tr><tr><td>5、0303-确认还款 (repay.confirm) 请求：增加otplnfo 验证码 status添加枚举05：放款成功06：放款失败 新增字段 overdueAmount逾期金额，有逾期时返回 fistOverdueDate 逾期起始日，有逾期时返回 punishInterest总罚息，单位分</td><td colspan="6">6、3.2.2公共数据对象 BorrowOrderInfo (借据信息)</td></tr><tr><td>2022-01- 15</td><td colspan="6">添加hwEmployeeTag:华为员工标签 2、0114-生命周期操作(account.lifecycle) 请求添加：facelmageld 3、0603接口 msgld 改为 creditMsgld 4、公共错误码新增 E050118有放款中的订单 5、3.2.2 公共数据对象 BorrowOrderlnfo (借据信息) status 添加枚举05：放款成功06：放款失败 6、0403-绑卡确认 (bindcard.confirm)</td></tr><tr><td>2022-01- 24</td><td colspan="6">0101-用户准入接口(account.check) 请求添加 enterpriseEmail String 1280 企业邮箱</td></tr></table></body></html>  

<html><body><table><tr><td>2022-02- 10</td><td>约关系)</td><td>0105-授信关系查询接口(credit.account.querry) 请求体creditOrderld 申请书编号添加描述（不传时，返回最新的签</td><td>陈志江</td></tr><tr><td>2022-02- 15</td><td></td><td>1、如下公共对象的 repayType字段，新增枚举4（等额本金）： RepayTypelnfo、 RepayPlanInfo、 BorrowOrderlnfo 2、如下接口字段新增枚举4 (等额本金): 0201接口出参repayType字段、 0202接口入参repayType 字段、 0203接口入参repayType字段、 0204接口入参repayType字段 3、公共错误码新增 E060021还款金额小于最小自定义还款金额 4、公共错误码新增 E060022自动扣款中，请稍后再还</td><td>刘定奇</td></tr><tr><td>2022-02- 17</td><td></td><td>0113-销户检查(account.cancel.check) 去除响应字段 protocolInfos。</td><td>潘海龙</td></tr><tr><td>2022-02- 28</td><td></td><td>1、公共对象 BorrowOrderlnfo 新增字段 overduePrincipalAmount (逾期本金) 0101-用户准入接口 (account.check)</td><td>刘定奇 陈志江</td></tr><tr><td>2022-03- 28</td><td>请求增加活动标识</td><td>0102-授信检查接口 (credit.check) 0103-授信确认接口 (credit.confirm) 0201-默认试算接口 (borrow.trial.default) 0202-借贷试算 (borrow.trial) 0203-创建订单 (borrow.apply) 0204-订单确认 (borrow.confirm) 0302-还款试算 (repay.trial) 0303-还款确认 (repay.confirm) BorrowOrderlnfo (借据信息）</td><td>潘海龙</td></tr><tr><td>2022-04- 12</td><td></td><td>添加活动标识 身份证上传 E011015您上传的身份证已过有效期，请办理后重新上 传</td><td>陈志江</td></tr><tr><td>2022-04- 13</td><td></td><td>新增接口身份证过期回调通知 (eid.expired.notify)</td><td>陈志江</td></tr></table></body></html>  

<html><body><table><tr><td>2022-05- 20</td><td></td><td>准入接口增加分期购渠道字段</td></tr><tr><td>2022-06- 02</td><td>1、公共数据对象 CreditSignlnfo授信签约关系新增字段 态返回 2、0605-调额通知 (credit.change.notify)</td><td>陈志江 entrepreneurTag企业用户标识：1企业用户0普通用户，已签约状 新增字段entrepreneurTag 企业用户标识：1企业用户0 普通用户 陈志江</td></tr><tr><td>2022-06- 07</td><td>新增公共数据对象 化信息) 确认还款（repay.confirm）接口新增字段 amountChangelnfo AmountChangelnfo NAO (还款成功后，返回) rateChangelnfo RateChangelnfo 成功后，返回) 查询还款结果（repay.status.query）新增字段 amountChangelnfo AmountChangelnfo NAO (还款成功后，返回) rateChangelnfo RateChangelnfo 成功后，返回)</td><td>AmountChangelnfo（额度变化信息）、RateChangelnfo（利率变 额度调整 NAO 利率调整 (还款 额度调整 NAO 利率调整 (还款 陈志江</td></tr><tr><td></td><td>CreditSignlnfo授信签约关系一新增字段 参数名类型 长度 M/O 说明 tempDailyRate tempAnnualRate tempExpireDate 调额通知 (credit.change.notify) 请求新增字段 tempExpireDate</td><td>rateNotifyType新增枚举2003：临时提升利率2004:临时降低利率</td></tr></table></body></html>  

<html><body><table><tr><td></td><td>(borrow.trial) 请求新增字段 scheduleRepay 响应新增字段 scheduleDailyRate scheduleAnnualRate 0203-创建订单 (borrow.apply) 请求体新增字段 scheduleRepay 公共数据对象 RepayPlanlnfo (还款计划信息) 新增字段 scheduleRepay</td><td>0201-默认试算(borrow.trial.default）/0202-借贷试算 陈志江</td></tr><tr><td>2022-06- 14</td><td>CreditSignlnfo 授信签约关系 增加字段 dailyRepayAmtPromo 每日还款额宣传信息 (1千用1天) scheduleRepay 0201-默认试算(borrow.trial.default）、0202-试算 (borrow.trial) 请求增加字段scheduleRepay 响应去除 scheduleRepaylnfo ScheduleRepaylnfo NAO 响应增加 scheduleDailyRate String 200 按期还日利率，单位是 % scheduleAnnualRate String 200 按期还年利率，单位是 %</td></tr></table></body></html>  

<html><body><table><tr><td>2022-06- 22</td><td></td><td>CreditSignlnfo授信签约关系 潘海龙 修改：scheduleRepay 不返回空，1:按期还0：灵活还 RepayPlanlnfo还款计划信息 修改：scheduleRepay不返回空，1:按期还0：灵活还 新增：penaltyRate String 20 O提前结清违约金利率，单位是% BorrowOrderInfo (借据信息) 修改：scheduleRepay 不返回空，1:按期还0：灵活还 新增：penaltyRate String 20 O提前结清违约金利率，单位是%</td></tr><tr><td>2022-07- 01</td><td>新增公共错误码</td><td>新增：penaltyRate String 20 O提前结清违约金利率，单位是% 刘定奇 授信确认E010311用户身份证信息超30分钟失效</td></tr><tr><td>2022-0726</td><td>1、用户准入接口(account.check)、授信检查接口(credit.check)、 授信确认接口(credit.confirm) 入参新增字段 loanProductType 2、CreditSignlnfo 授信签约关系 增加返回字段 entrepreneurTag loanProductType businessLicenseNum营业执照编号 3、0206-查询用信申请结果 (borrow.status.query) 响应新增字段</td><td>陈志江</td></tr><tr><td>2022-0729</td><td>needPhoneVerify 1、去除 CreditSignlnfo 授信签约关系中loanProductType 字段 2、去除查询用信申请结果 (borrow.status.query) 响应字段 needPhoneVerify 3、BorrowOrderInfo (借据信息) Status 新增枚举值 status String 2M订单状态 10：待电话核实</td><td>陈志江</td></tr></table></body></html>  

<html><body><table><tr><td>2022-0729</td><td>响应字段 needPhoneVerify Status新增枚举值</td><td>1、去除CreditSignlnfo 授信签约关系中 loanProductType 字段 2、去除查询用信申请结果 (borrow.status.query) 3、BorrowOrderlnfo (借据信息) statusString2M订单状态</td><td>陈志江</td></tr><tr><td>2022-0804</td><td>10：待电话核实 1、0102-授信检查接口(credit.check) 17：须跳转外部补录 2、新增公共数据对象 ExternalOptlnfo (外部操作信息) 3、授信结果查询接口(credit.status.query) 响应 status 增加枚举 ABORTED：操作中断 果（borrow.status.query)</td><td>响应operations 增加枚举值 14：营业执照影像 (预留，本次开发无须返回，返回17即可) 15：税务信息 (预留，本次开发无须返回，返回17即可) 增加字段externalOptinfo 4、0203-创建订单（borrow.apply）、0206-查询用信申请结 响应的operations 增加枚举值 16：电话核实 5、新增接口</td><td>陈志江</td></tr><tr><td>2022-08- 18</td><td>请求新增字段 调接口，需要告知产品类型 请求添加字段 loanProductType (account.cancel.notify) 请求添加字段 loanProductType 请求增加</td><td>0803-外部操作信息地址查询 (sp.operation.addr.query) 1、0101-用户准入接口(account.check) enterpriseAddr String128 O企业所在省市区 2、0603-通知授信结果 (credit.status.notify) 因为用户可能同时存在“信用贷”和“小微企业贷”，所以授信结果回 3、0604-推送优惠券（coupon.send）、0605-调额通知 (credit.change.notify）、0608-销户回调通知 4、0701-查询优惠券 (coupon.query)</td><td>陈志江</td></tr></table></body></html>  

<html><body><table><tr><td>2022-08- 25</td><td>1、0101用户准入接口(account.check) 请求增加 enterpriseAddr String 256O企业所在省市区 2、0102 授信检查接口(credit.check) 响应增加 creditOrderld String36 C授信申请单号(广发场景返回) 3、0104-授信结果查询接口(credit.status.query)、0110-身份 证上传接口(eid.upload)、0111-联系人添加接口(contact.add) 、0112-个人资料补充接口(userinfo.supplement) 请求增加 creditOrderld String36 C授信申请单号(广发场景) 4、授信关系查询接口(credit.account.query) 请求增加 loanProductType 5、0102-授信检查接口(credit.check) operations 增加枚举18：行业投向 6、Userlnfo 增加 industry String</td></tr><tr><td>2022-09- 07</td><td>8Ｃ行业投向 0101-用户准入接口(account.check) 请求新增入参 enterpriseAreaCode String32 O企业所在区/县编码 0110-身份证上传接口(eid.upload) 请求增加字段 validDate String 32C身份证有效期:，如:********- ******** 0803-外部操作地址查询 (sp.operation.addr.query) 入参creditApplyld 修改为 creditOrderld creditOrderld String36 C签约单号(广发场景) 0501-通知借贷结果 (order.notify) 请求notifyType 增加枚举05:放款二类户成功、提现一类户失败</td></tr><tr><td>2022-09- 08</td><td>notifyType String 805：放款二类户成功、提现一类户失败 CreditSignlnfo授信签约关系新增字段 licenseNumType 编号类型1：营业执照、2：工商注册号</td></tr></table></body></html>  

<html><body><table><tr><td>2022-10- 28</td><td>资金用途新增枚举: 6:生产经营 7：日常周转 8：购买原材料 9：购买生产设备 10：经营场所装修 涉及接口： BorrowOrderlnfo的 usage borrow.trial.default 响应 usage borrow.trial响应 usage borrow.apply 响应usage</td><td>韦涛</td></tr><tr><td>2022-11- 10</td><td>borrow.confirm 响应usage 新增错误码E070105：资方不支持开具结清证明”</td><td>月账单单接口新增错误码E060030：二类户无交易 陈志江 0203-创建订单接口（borrow.apply）响应 陈志江</td></tr><tr><td>2022-11- 15</td><td>Operations 新增枚举“20：银行卡用信重签约 CreditSignlnfo授信签约关系 按期还、支持灵活还，0、不传：仅支持灵活还”</td><td>修改scheduleRepay字段定义，修改为“2:仅支持按期还，1:支持</td></tr><tr><td>2023-01- 03</td><td>新增错误码 E060026、E070106、E070107 修改错误码含义E070105 BorrowOrderlnfo（借据信息）、RepayPlanlnfo（还款计划信|陈志江</td><td>陈志江</td></tr><tr><td>2023-03- 24</td><td>息）新增字段capitalType、capitalName . 新增接口：0804-查询转账还款银行信息 (sp.repay.card.info.query) 0303-确认还款（repay.confirm）、0304-查询还款结果 (repay.status.query) 新增响应字段返回: failedReaso n、capitalType、overdueAmount、overdueOrderCnt . 0302-还款试算 (repay.trial) 请求入参新增字段curRepayAmt . RepayOrderDetail (还款订单详情) 新增字段 leftPrincipalAmt</td><td></td></tr><tr><td>2023-03- 31</td><td>. 查询支持的银行列表（bank.support.list）入参增加用户、借据|陈志江 列表信息 绑卡查询(bindcard.query）入参增加借据列表信息</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>2023-04- 04</td><td></td><td>准入接口响应新增 faceCheckUrl、faceCheckToken、validTime</td><td>陈志江</td></tr><tr><td>2023-04- 06</td><td></td><td>0303-确认还款（repay.confirm）、0304-查询还款结果 (repay.status.query）、通知还款结果(repay.notify）新增 还款失败原因错误码枚举</td><td>陈志江</td></tr><tr><td>2023-04- 22</td><td></td><td>sp.repay.card.info.query新增入参 curReplayPlans、curRepayAmt、capitalType、type account.check 新增入参 callbackUrl、optCode sp.operation.addr.query 新增入参 callbackUrl、operationld，新增返回值token credit.check新增入参 callbackUrl</td><td>陈志江</td></tr><tr><td>2023-0423</td><td></td><td>. . account.check 删除入参callbackUrl、optCode，删除响应 faceCheckUrl、faceCheckToken、validTime 授信确认接口(credit.confirm) loanChannel新增枚举 html: 端外h5</td><td>陈志江</td></tr><tr><td>2023-05- 05</td><td></td><td>添加准入错误码</td><td>陈志江</td></tr><tr><td>2023-07- 10</td><td></td><td>RepayOrderInfo(还款记录）新增属性repayChannel</td><td>陈志江</td></tr><tr><td>2023-08- 28</td><td></td><td>新增接口借款意图信息提交 (borrow.intention.submit)</td><td>陈志江</td></tr><tr><td></td><td></td><td>新增错误码E050119 确认的订单不存在</td><td>陈志江</td></tr></table></body></html>  

<html><body><table><tr><td>2023-10- 09</td><td></td><td>> CreditSignlnfo新增字段 tempAmount tempRemainAmount tempAmtExpireDate maxBorrowAmount ◇调额通知（credit.change.notify）新增入参 tempAmount tempRemainAmount tempAmtExpireDate 新增错误码 E050121</td><td>陈志江</td></tr><tr><td>2023-11- 09</td><td></td><td>borrow.order.query 新增入参ascending 新增错误码E050122、E050123、E050124</td><td>陈志江</td></tr><tr><td>2023-11- 21</td><td></td><td>创建订单、查询用信申请结果接口新增字段 protocolExtendMsg 协议补充信息</td><td>陈志江</td></tr><tr><td>2024-01- 12</td><td></td><td>查询用信申请结果接口新增字段changeOffer</td><td>林思洁</td></tr><tr><td>2024-02- 22</td><td></td><td>0101-用户准入接口(account.check)新增响应字段 protocolinfos 公共数据对象 BorrowOrderlnfo 中usage新增枚举值11～15 默认试算(borrow.trial.default）、借贷试算 (borrow.trial）、创建订单（borrow.apply）、订单确认 (borrow.confirm）等接口中usage 字段新增枚举值11～15 新增0807-协议信息查询（protocol.info.query）接口</td><td>林思洁</td></tr><tr><td>2024-03- 07</td><td></td><td>公共数据对象 CreditSignlnfo 中canCreditChange 字段新增枚 举值2-提额申请中 新增接口0808-线下提额申请 (offline.raise.amount.apply)</td><td>林思洁</td></tr><tr><td>2024-03- 15</td><td></td><td>公共数据对象 CreditSignlnfo 中 signStatus 字段新增枚举值7</td><td>林思洁</td></tr><tr><td>2024-03- 28</td><td></td><td>0808-线下提额申请（offline.raise.amount.apply）修改请求 参数cityName为cityCode</td><td>林思洁</td></tr><tr><td>2024-04- 11</td><td></td><td>3.1.1公共错误码中，userinfo.supplement接口新增错误码 E011201-详细地址校验未通过；repay.confirm接口新增错误 码E060027一确认还款存在异常</td><td>林思洁</td></tr></table></body></html>  

<html><body><table><tr><td>2024-04- 16</td><td></td><td></td><td>3.1.1公共错误码中，repay.confirm接口新增错误码 E060027一确认还款存在异常</td><td>林思洁</td></tr><tr><td>2024-05- 09</td><td></td><td></td><td>3.1.1公共错误码中，repay.confirm 接口新增错误码 E060028、E060029、E060031; borrow.trial.default、borrow.trial接口新增 E050125 错误码</td><td>林思洁</td></tr><tr><td>2024-05- 16</td><td></td><td></td><td>3.1.1公共错误码中 borrow.trial.default,borrow.trial接口新增 E050126错误码 ◇ 3.2.2 公共数据对象中CreditSignlnfo 对象</td><td>林思洁 林思洁</td></tr><tr><td>2024-05- 16</td><td></td><td>0104-授信结果查询接口 和protocollnfos字段</td><td>signStatus字段新增枚举值8-审批中待补录 0103-授信确认接口(credit.confirm)请求中 operation字段新增枚举credit.again-回捞补录 二次授信确认，新增lastMsgld字段; (credit.status.query)响应 status 字段新增 NEED_SUPPLEMENT枚举，新增operations 0603-通知授信结果 (credit.status.notify) 请求中 status 字段新增NEED_SUPPLEMENT枚 举 新增0809-补录项查询 □</td><td></td></tr><tr><td>2024-05- 16</td><td></td><td></td><td>(supplement.operations.query）接口 3.2.2 公共数据对象中CreditSignlnfo 对象 signStatus字段新增枚举值7-清退</td><td>焦克文</td></tr><tr><td>2024-05- 23</td><td></td><td>></td><td>0104-授信结果查询接口响应status修改长度 为16 0603-通知授信结果 (credit.status.notify) 请求中 status字段修改长度为16</td><td>林思洁</td></tr><tr><td>2024-05- 28</td><td></td><td></td><td>◇3.1.1公共错误码中borrow.trial.default接口删 除E050126错误码，新增 E050127错误码; borrow.apply接口新增 E050126 错误码</td><td>林思洁</td></tr></table></body></html>  

<html><body><table><tr><td>2024-05- 28</td><td></td><td>0809-补录项查询 (supplement.operations.query）接口请求中 scene字段新增枚举值: borrow.first.supplement 0405-绑卡查询（bindcard.query）接口响应 中新增 repayCardlnfos 字段 0203-创建订单（borrow.apply）响应中新增 字段externalOptInfo，operations 新增枚举 21 0206-查询用信申请结果 (borrow.status.query）响应中新增字段 externalOptInfo，operations新增枚举21 0809-补录项查询 (supplement.operations.query）响应中新增 externalOptInfo字段 0803－外部操作地址查询 (sp.operation.addr.query）请求中 operation 字 段新增枚举 secondCardAdd 新增0215－企业信息补充接口 (enterprise.supplement) 新增0216－文书送达信息补充接口 (documents.delivery.supplement) 新增0409－添加对公账户 (corporate.account.add) 新增0410－对公账户状态查询 □</td><td>林思洁 焦克文</td></tr><tr><td>30</td><td></td><td>(supplement.operations.query)接口请求中 scene字段新增枚举值: credit.active.condition.query一激活条件查询 3.2.2 公共数据对象中CreditSignlnfo 对象 entrepreneurTag 新增枚举：2--个体工商户 0803外部操作地址查询 (sp.operation.addr.query)接口operation新 增枚举：invoicing—开具发票 0408外部操作地址查询 (protocol.list)接口 type 枚举增加3--用户协议 与授权查询</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>2024-06- 07</td><td>21 修改为企业联系信息补充接口 (enterprise.contact.Info.supplement），请</td><td>0809-补录项查询 (supplement.operations.query）接口响应中 operations字段新增枚举值22-企业信息，响应 新增 dynamicFields字段 0203-创建订单（borrow.apply）响应中去除 字段externalOptInfo，operations去除枚举 0206-查询用信申请结果 (borrow.status.query）响应中新增字段 enterpriseConcatInfo，operations 新增枚举 23-企业联系信息 0216－文书送达信息补充接口 (documents.delivery.supplement） 接口名</td><td>林思洁</td></tr><tr><td>2024-06- 15</td><td>></td><td>legalPersonEmail 公共数据对象Notifylnfo 中im 模板 im.loan.repay.overdue 改为 loan.repay.overdue 公共数据对象 BorrowOrderlnfo 中 status 字段 新增枚举值11：放款成功后冲销 0501-通知借贷结果（order.notify）请求参数 中notifyType新增枚举06：放款成功后冲销 0215-企业信息补充接口 (enterprise.supplement）请求参数修改 0301-查询月账单/所有借贷 (repay.plan.query）接口删除无用字段 punishInterest 0302-还款试算(repay.trial）接口请求新增 cardNo 字段，响应新增balanceEnough-余额 是否充足字段 0405-绑卡查询（bindcard.query）新增请求 参数 scene 新增0410-查询支持的银行网点 (bank.branches.support.query)</td><td>林思洁</td></tr></table></body></html>  

<html><body><table><tr><td>2024-06- 18</td><td>> ></td><td>公共数据对象新增 Industylnfo 对象体，删除 DynamicFiel d、EnterpriseConcatlnfo、Beneficiary 对象 体 0201-默认试算（borrow.trial.default）和 0202-借贷试算（borrow.trial）响应新增 extend字段 0203-创建订单（borrow.apply）请求新增 industylnfo、extend字段，响应新增 externalOptInfo字段 ◇0204-订单确认（borrow.confirm）响应删除 enterpriseConcatlnfo、externalOptlnfo字 段，operations 删除枚举值22/23 0809-补录项查询 (supplement.operations.query）响应删除 dynamicFields字段、operations删除枚举值 22 删除0410－对公账户状态查询 (corporate.account.status.query）接口 删除0215-企业信息补充接口 (enterprise.supplement）接口 删除0216－企业联系信息补充接口</td><td>林思洁 (enterprise.contact.Info.supplement）接口</td></tr><tr><td>2024-06- 18</td><td></td><td>新增开具文件材料接口 (account.material.send) 新增活体人脸视频信息对象FaceVideolnfo 活体人脸校验接口(face.check)新增人脸视频信 息字段faceVideolnfo ◇补录项查询 (supplement.operations.query) 接口scene字段删除枚举值 credit.active.condition.query一激活条件查询</td><td>焦克文</td></tr><tr><td>2024-06- 20</td><td></td><td>0102授信检查接口（credit.check）operation|焦克文 字段新增枚举：credit.again—重新授信核额</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>2024-06- 24</td><td>公</td><td>公共数据对象Banklnfo中icon字段改为非必需 0409－添加对公账户 (corporate.account.add) 接口请求删除 enterpriseEmail、legalPersonEmail 字段 0803－外部操作地址查询 (sp.operation.addr.query） 请求 operation 中二 类户添加的枚举值改为secondAccountAdd 0410－查询支持的银行网点 (bank.branches.support.query）接口新增银行 编码和网点编码</td><td>林思洁</td></tr><tr><td>2024-06- 28</td><td></td><td>0409－添加对公账户 (corporate.account.add) 接口请求新增bankCode 和bankBranchCode 字 段 0203－创建订单（borrow.apply）请求新增 repayCardNo字段， 0303-确认还款（repay.confirm）请求删除无用 字段borrowOrderld</td><td>林思洁</td></tr><tr><td>2024-07- 03</td><td>></td><td>3.2.2公共数据对象中CreditSignlnfo 对象新增字 段：enterpriseName-企业名称 0109-face.check接口新增字段： isAgreeProtocol-是否同意协议</td><td>焦克文</td></tr><tr><td>2024-07- 03</td><td></td><td>新增0215－查询借款试算优惠券列表 (borrow.trial. couponlnfos.query）接口 新增0216－查询借款试算还款计划列表 (borrow.trial.repayPlanlnfos.query）接口</td><td>林思洁 林思洁</td></tr><tr><td>2024-07- 04</td><td>必传</td><td>公共数据对象中 RepayTypelnfo 中 totalAmt、rate 改为条件必传 0201-默认试算(borrow.trial.default）接口响应 totalInterestAmt、firstBillAmt、repayPlanInfos改为条件</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>2024-07- 15</td><td></td><td>公共错误码中corporate.account.add接口新增错误 码E040901 0201-默认试算（borrow.trial.default）响应中 extend 字段修改为 trialExtend、0203-创建订单 (borrow.apply）请求中extend 字段修改为 trialExtend 0215－查询借款试算优惠券列表 (borrow.trial.couponlnfos.query）、0216 - 查询借款试算还款计划列表 (borrow.trial.repayPlanlnfos.query）接口新增请 求参数trialExtend</td><td>林思洁</td></tr><tr><td>2024-07- 16</td><td></td><td>公共错误码中credit.confirm接口新增错误码 E010312，删除无用错误码E010311-用户身份证 信息超30分钟失效 公共数据对象 ChangeOffer新增</td><td>林思洁 焦克文</td></tr><tr><td>2024-07- 24</td><td></td><td>scen e、preSignlnfo、curSignlnfo、preOrderlnfo、cur Orderlnfo、curRepayPlanlnfos、protocollnfos 字 段，去除老字段 新增公共对象 ChangeOfferSignlnfo 新增公共对象ChangeOfferOrderInfo</td><td></td></tr><tr><td>2024-07- 22</td><td></td><td>0803外部操作地址查询 (sp.operation.addr.query)接口 operation 新增枚 举：negotiatePrice一议价 公共数据对象Notifylnfo 中新增im 模板 新增 0811－通用资格查询 (qualification.query) 接口</td><td>刘伟</td></tr><tr><td>2024-08- 14</td><td></td><td>部分字段必传项修正 0105－授信关系查询接口(credit.account.query) 中userlnfo 修正为条件必传 0206-查询用信申请结果 (borrow.status.query）中 orgBorrowOrderld 修 正为条件必传</td><td>林思洁</td></tr><tr><td>2024-10- 11</td><td></td><td>新增公共对象 DynamicParam 0803－外部操作地址查询 (sp.operation.addr.query） operation 新增枚 举：activityQuery一活动；新增接口字段: dynamicParams</td><td>谢玉方</td></tr><tr><td>2024-10- 14</td><td></td><td>新增0812活动数据上报(activity.data.report)接口|焦克文</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>2024-10- 15</td><td></td><td>0101、0102、0103接口入参hwEmployeeTag字 段描述修改</td><td>林思洁</td></tr><tr><td>2024-10- 16</td><td></td><td>0812－活动数据上报接口将 has_activity_qualification 修改为 is_activity_reach(活动是否触达用户)</td><td>焦克文</td></tr></table></body></html>  

<html><body><table><tr><td>> 2024-11- 30 ? 段</td><td>公共数据对象CreditSignlnfo 新增 enterpriseld、accountNo 字段；Banklnfo 新 增 category 字段；IndustyInfo 新增 sublnfos 字段； 0102-授信检查接口(credit.check)请求新增 creditScore 字段、operation 字段增加 credit.more 枚举，响应新增 regular 字 段、accountNo字段 新增0117－存量企业信息查询接口 (enterprise.info.query) 0201-默认试算 (borrow.trial.default）、0202-借贷试算 (borrow.trial） 响应新增 defaultlndustylnfos 字 、minAmount、maxAmount、amountStep 字段 0301-查询月账单/所有借贷 (repay.plan.query）响应新增 cardBalancelnfos字段 0401-查询支持的银行列表 (bank.support.list）响应 banklnfos 字段改为 非必需，新增url字段 0405-绑卡查询（bindcard.query）接口请求 新增cardNo 字段，scene新增 transfer枚举， 响应新增 transferCardlnfos字段 删除接口0410－查询支持的银行网点 (bank.branches.support.query) 新增0611－借款逾期通知 (borrow.overdue.notify）接口 ◇新增 0612-进件通知 (credit.create.notify) ◇新增8.130613－企业变化通知 (enterprise.change.notify) 0803－外部操作地址查询 (sp.operation.addr.query）请求operation 字段新增 borrowUsageU p、bankBranchQuery、balanceQuery 枚举 0811-通用资格查询 (qualification.query) 请求scene字段新增 borrowUsageUp 枚举 新增0813-文件材料开具查询接口 (account.material.query) 新增0814-枚举配置查询接口</td></tr></table></body></html>  

<html><body><table><tr><td>2024-12- 05</td><td></td><td>公共数据对象Couponlnfo 新增 discountAnnualRate字段，type 新增枚举值3 ◇0201-默认试算 (borrow.trial.default）、0202-借贷试算 (borrow.trial）响应新增 oldAnnualRate、oldScheduleAnnualRate字 段</td><td>林思洁</td></tr><tr><td>2024-12- 11</td><td>□</td><td>公共数据对象 CreditSignlnfo 中 signStatus枚 举新增99 0102-授信检查接口(credit.check)、0103- 授信确认接口(credit.confirm)请求中operation 字段长度修改为32 0109-活体人脸校验接口(face.check)接口请 求新增 operation字段 0102-授信检查接口(credit.check)接口响应 isRegular字段改成enableCredit 0603-通知授信结果 (credit.status.notify) 请求新增newAccountNo字段 0215－查询借款试算优惠券列表 (borrow.trial.couponlnfos.query）、0701 - 查询优惠券（coupon.query）请求新增 pageSize、beginlndex字段 0809-补录项查询 (supplement.operations.query）接口请求新 增 callbackUrl字段，scene字段增加枚举 active.supplement ? 删除0612－进件通知</td><td>林思洁</td></tr></table></body></html>  

<html><body><table><tr><td>2024-12- 16</td><td>> CardBalancelnfo对象 接口请求新增pageSize字段 0301-查询月账单/所有借贷 cardBalancelnfos字段 删除0611-借款逾期通知 (borrow.overdue.notify)</td><td>公共数据对象BorrowOrderlnfo 新增 repayDesc、canRepayDate字段，删除 0207-查询借钱记录 (borrow.order.query) 0405-绑卡查询（bindcard.query）请求 scene枚举值修改，删除cardNo字段 (repay.plan.query）响应去掉 新增0309-查询还款卡是否余额充足 (repay.balance.enough.query）接口</td><td>林思洁</td></tr><tr><td>2024-12- 16</td><td></td><td>公共数据对象BorrowOrderlnfo对象中新 增resonCode 字段、Couponlnfo 对象新增 source字段 0605-调额通知（credit.change.notify）请求 新增source</td><td>林思洁</td></tr></table></body></html>  

# 目  录  

# 1 概述.  

1.1 目的..  
1.2 范围..  
1.3 假设与依赖..  
1.4 术语、定义和缩略语..  

# 2 接口规范.  

2.1 接口协议..  
2.2 URL 定义..  
2.3 报文定义..  
2.4 参数命名约定.  
2.5 安全要求..  
2.5.1 加密签名机制..  
2.5.1 秘钥更新机制.  
2.5.2 加密机制..  
2.5.1 签名机制..  

# 3 报文结构说明.  

3.1 报文结构.  

3.1.1 请求消息.   
3.1.2 响应消息.   
3.1.1 公共错误码..  

3.2 数据对象定义.3.2.1 数据对象定义说明.3.2.2 公共数据对象.  

# 4 准入与授信管理接口 (借贷平台提供).  

4.1 0101 – 用户准入接口(account.check)..  

4.1.1 请求data 数据结构.  
4.1.2 响应消息.  

4.2 0102 – 授信检查接口(credit.check)..  

4.2.1 请求data 数据结构.  
4.2.2 响应消息.  

# 4.3 0103 – 授信确认接口(credit.confirm)..  

4.3.1 请求data 数据结构.  
4.3.2 响应消息.  

4.4 0104 – 授信结果查询接口(credit.status.query)..  

4.4.1 请求data 数据结构.  
4.4.2 响应消息.  

4.5 0105 – 授信关系查询接口(credit.account.query)..  

4.5.1 请求data 数据结构.  

4.5.2 响应消息.4.6 0106 – 用户开具证明接口(account.settlement.query)..4.6.1 请求 data 数据结构..4.6.2 响应消息..4.7 0107 – 发送开具证明接口(account.settlement.send).4.7.1 请求 data 数据结构..4.7.2 响应消息.4.8 0108 – 更换手机号接口(account.phoneno.change)..4.8.1 请求 data 数据结构..4.8.2 响应消息.4.9 0109 – 活体人脸校验接口(face.check)..4.9.1 请求 data 数据结构..4.9.2 响应消息..4.10 0110 – 身份证上传接口(eid.upload)..4.10.1 请求 data 数据结构..4.10.2 响应消息.4.11 0111 – 联系人添加接口(contact.add)..4.11.1 请求data 数据结构.4.11.2 响应消息..4.12 0112 – 个人资料补充接口(userinfo.supplement)..4.12.1 请求 data 数据结构..4.13 0113 – 销户检查(account.cancel.check)..4.13.1 请求 data 数据结构..4.13.2 响应消息..4.14 0114 – 生命周期操作(account.lifecycle)....4.14.1 请求data 数据结构.4.14.2 响应消息..4.15 0115 –身份证查询操作(eid.upload.query)..4.15.1 请求 data 数据结构....4.15.2 响应消息..5 用信接口(借贷平台提供)...  

5.1 0201 - 默认试算（borrow.trial.default）  

5.1.1 请求 data 数据结构.  
5.1.2 响应消息.  

5.2 0202 - 借贷试算（borrow.trial）  

5.2.1 请求 data 数据结构.  
5.2.2 响应消息.  

5.3 0203 – 创建订单（borrow.apply）  

5.3.1 请求 data 数据结构.  
5.3.2 响应消息.  

5.4 0204 – 订单确认（borrow.confirm）  

# 5.4.1 请求 data 数据结构.  

5.4.2 响应消息.  

5 0205 – offer 变更确认（borrow.offer.confirm）  

5.5.1 请求 data 数据结构.  
5.5.2 响应消息.  

5.6 0206 - 查询用信申请结果（borrow.status.query）  

5.6.1 请求 data 数据结构.  
5.6.2 响应消息.  

5.7 0207 - 查询借钱记录（borrow.order.query）  

5.7.1 请求 data 数据结构.  
5.7.2 响应消息.  

5.8 0208 – 取消未完成订单（borrow.order.cancel）  

5.8.1 请求 data 数据结构.  
5.8.2 响应消息.  

5.9 0209 – 查询借款订单详情（borrow.order.detail）  

5.9.1 请求 data 数据结构.  
5.9.2 响应消息.  

# 6 还款接口(借贷平台提供).  

# 6.1 0301 - 查询月账单/所有借贷（repay.plan.query）  

6.1.1 请求 data 数据结构.  
6.1.2 响应消息.  

6.2 0302 – 提前还款试算（repay.trial）  

6.2.1 请求 data 数据结构.  
6.2.2 响应消息.  

6.3 0303 - 确认还款（repay.confirm）  

6.3.1 请求 data 数据结构.  
6.3.2 响应消息.  

6.4 0304 - 查询还款结果（repay.status.query）  

6.4.1 请求 data 数据结构.  
6.4.2 响应消息.  

6.5 0305 - 查询还款记录（repay.order.query）  

6.5.1 请求 data 数据结构.  
6.5.2 响应消息.  

6.6 0306 - 查询还款记录详情（repay.order.detail.query）  

6.6.1 请求 data 数据结构..  
6.6.2 响应消息.  

# 7 绑卡接口(借贷平台提供).  

7.1 0401 - 查询支持的银行列表（bank.support.list）[公开]..  

7.1.1 请求 data 数据结构.7.1.2 响应消息.7.2 0402 - 请求绑卡（bindcard.apply）7.2.1 请求 data 数据结构.  

# 7.2.2 响应消息.  

.3 0403 - 绑卡确认（bindcard.confirm）  

7.3.1 请求 data 数据结构.  
7.3.2 响应消息.  

7.4 0404 - 用户自动还款设置（account.repay.set）  

7.4.1 请求 data 数据结构.  
7.4.2 响应消息.  

7.5 0405 - 绑卡查询（bindcard.query）  

7.5.1 请求 data 数据结构.  
7.5.2 响应消息.  

7.6 0406 – 删除绑定卡（bindcard.delete）  

7.6.1 请求 data 数据结构.  
7.6.2 响应消息.  

7.7 0407 - 卡 Bin 校验（card.bin.check）  

7.7.1 请求 data 数据结构.  
7.7.2 响应消息.  

# 8 回调接口（华为提供）  

8.1 0501 - 通知借贷结果（order.notify）  

8.1.1 请求 data 数据结构.  
8.1.2 响应消息.  

.2 0602 - 通知还款结果（repay.notify）  

8.2.1 请求 data 数据结构.  
8.2.2 响应消息.  

8.3 0603 - 通知授信结果（credit.status.notify）  

8.3.1 请求 data 数据结构.  
8.3.2 响应消息.  

8.4 0604 – 推送优惠券（coupon.send）  

8.4.1 请求 data 数据结构.  
8.4.2 响应消息.  

8.5 0605 – 调额通知（credit.change.notify）  

8.5.1 请求 data 数据结构.  
8.5.2 响应消息.  

8.6 0606 – 营销通知（promotion.notify）  

8.6.1 请求 data 数据结构.  
8.6.2 响应消息.  

# 9 优惠券.  

9.1 0701 – 查询优惠券（coupon.query）  

9.1.1 请求 data 数据结构.  
9.1.2 响应消息.  

# 1 概述  

# 1.1 目的  

本文的主要目的是为了规范华为零钱服务器与借贷平台之间的接口定义，确保开放的接口格式和定义统一。  

# 1.2 范围  

华为零钱服务器与借贷平台之间的接口。  

# 1.3 假设与依赖无。  

# 1.4 术语、定义和缩略语  

<html><body><table><tr><td>术语</td><td>英文</td><td>中文</td></tr><tr><td>HTTP</td><td>HyperTextTransferProtocol</td><td>超文本传输协议</td></tr><tr><td>XML</td><td>Extensible Markup Language</td><td>可扩展标记语言</td></tr><tr><td>JSON</td><td>JavaScript ObjectNotation</td><td>是一种轻量级的数据交换格式</td></tr></table></body></html>  

# 2 接口规范  

# 2.1 接口协议  

零钱服务器与借贷平台之间的接口采用HTTPS 单向认证协议(HTTP 仅用于测试)；  
数据格式：请求和响应都只支持Json（UTF-8）的报文格式，遵循[RFC 7159]规范；  
请求方法：支持POST。  

# 2.2 URL 定义  

各个借贷平台提供的接口url 由各银行自行定义，所有的接口均提供统一的url：  

# 2.3 报文定义  

对数据格式的要求如下：  

报文需要符合URL 和JSON 的规范JSON 中传输的URL，首先进行URL 转义，然后再进行JSON 转义  

# 2.4 参数命名约定  

接口参数采用驼峰模式，多个单词拼接而成的参数名，除了第一个单词的首字母小写，后续每个单词的首字母都大写。如果本文中参数命名违反此原则，可能由于word 自动排版导致，建议沟通确认后遵守此原则。  
对于业界通用，大家所熟知的缩写，可以使用，否则不建议使用，如有人把version 缩写为 ver 则让人无法理解；  

# 2.5 安全要求  

# 2.5.1 加密签名机制  

报文发起方(包括请求和响应)：  

1. 加密秘钥ID 对应的加密秘钥对公共部分之外的业务域做全加密，加密之后的字符串设置在data 字段中  

2. 使用客户端签名秘钥ID 对应的秘钥对消息体做签名  

报文接收方(包括请求和响应)：  

1. 获取请求中的签名秘钥ID，找到对应的验签秘钥，对消息体做验签  
2. 获取请求中的加密秘钥ID，找到对应的加密秘钥，对业务域做解密。  

# 2.5.1 秘钥更新机制  

当需要更新秘钥时，其秘钥更新流程如下(以加密秘钥为例)：  

1. 双方协商新的加密秘钥，分配新的加密秘钥ID。  
2. 服务端上线新加密秘钥，同时支持老的加密秘钥，根据加密秘钥ID 来找到对应的加密  
秘钥解密。  
3. 客户端替换成新的加密秘钥ID 和加密秘钥。  
4. 客户端替换完成之后，服务端下线老的加密秘钥。  

# 2.5.2 加密机制  

对报文中的data 部分采用约定的密码使用国密SM4(CBC)算法进行加密示例：  

<html><body><table><tr><td>参与加</td><td>{"mobileNo":"2111","templateld":"createAccount","fields":[]}</td></tr><tr><td>密的部 分</td><td></td></tr><tr><td>加密秘 钥</td><td>85FF92A845C3541785FF92A845C35417</td></tr><tr><td>加密盐</td><td>20190304175131988558792796635675</td></tr><tr><td>值</td><td></td></tr><tr><td>SM4加</td><td>PuQHwuYq9RhC2qqsg48xY9lu8oiesHtIV6Dl4Ta+83XgDUGgfuhXtDPR0FOfpQDklaADMEin1N9bRSLjA36tCw==</td></tr><tr><td>密并</td><td></td></tr><tr><td>base6</td><td></td></tr><tr><td>4编码</td><td></td></tr><tr><td>后</td><td></td></tr><tr><td>data</td><td>"data": "20190304175131988558792796635675:</td></tr><tr><td>加密后</td><td>PuQHwuYq9RhC2qqsg48xY9lu8oiesHtIV6DI4Ta+83XgDUGgfuhXtDPR0FOfpQDklaADMEin1N9bRSLjA36tCw==",</td></tr></table></body></html>  

# 2.5.1 签名机制  

首先，对报文中出现签名域（signature、router）之外的所有数据元采用key=value的形式按照名称字典排序，然后以&作为连接符拼接成待签名串。  
其次，发起方的私钥对待签名串使用国密 SM2 算法生成签名，签名后的结果用 BASE64进行编码。  

示例：  

<html><body><table><tr><td>j</td><td>{"version":"1.0.0",</td></tr></table></body></html>  

<html><body><table><tr><td>mp=20190304175131&version=1.0.0 &encCertld=1&msgld=552186377913921536&msgType=otp.send&signType=SM2&timesta</td><td>串</td><td></td><td>bRSLjA36tCw==</td><td>:PuQHwuYq9RhC2qqsg48xY9Iu80iesHtIV6DI4Ta+83XgDUGgfuhXtDPR0FOfpQDklaADMEin1N9 channeld=HuaWeiWallet001&clientCertld=1&data=20190304175131988558792796635675</td><td></td><td></td><td></td><td></td><td></td><td></td><td>乙</td><td>W</td><td>s 04fb9227f4f9d07a514cc065718862ccb02524fbd7f177f1006a312fdd55365142805bladda25</td><td></td><td></td><td></td><td></td><td></td><td>W s</td><td>000b6e22a77fdbe9de7793b59c689d0157263cf918937bc10fbde2ea01f2fb03d5 Ta+83XgDUGgfuhXtDPR0FOfpQDklaADMEin1N9bRSLjA36tCw=="}</td><td>"data":"20190304175131988558792796635675:PuQHwuYq9RhC2qqsg48xY9lu80iesHtiV6D14</td><td>YHF3jKeTja2EkLfm7oHGdFriXAiEA60Z1b83HptV3zduNwskbV2V4Xq2kpmBuZ1jaEcVEUPc="，</td><td>"signature":"MEUCIHSaGKTfu2NDmDx4iy/</td><td></td><td>"encCertld":"1",</td><td>X "clientCertld":"1"，</td><td></td><td>"signType":"SM2",</td><td></td><td></td><td></td><td>u</td><td></td><td></td><td></td><td></td><td></td><td></td><td>"msgType":"otp.send”，</td><td></td><td></td><td></td><td></td><td>"timestamp":"20190304175131",</td><td></td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td>签</td><td>MEUCIHSaGKTfu2NDmDx4iy/</td></tr><tr><td>名</td><td>YHF3jKeTja2EkLfm7oHGdFriXAiEA60Z1b83HptV3zduNwskbV2V4Xq2kpmBuZ1jaEcVEUPc=</td></tr><tr><td>的</td><td></td></tr><tr><td>结</td><td></td></tr><tr><td>果</td><td></td></tr></table></body></html>  

# 3 报文结构说明  

# 3.1 报文结构  

所有的报文必须按以下结构提交，所有接口的业务域元素必须以json 字符串的形式加密之后放在data 下，包括请求和响应。  

# 3.1.1 请求消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>version</td><td>String</td><td>10</td><td>M</td><td>应用版本号，当前默认为1.0.0</td></tr><tr><td>sourceld</td><td>String</td><td>10</td><td>M</td><td>调用方标识</td></tr><tr><td>msgId</td><td>String</td><td>20</td><td>M</td><td>消息流水号，保证唯一</td></tr><tr><td>timestamp</td><td>String</td><td>14</td><td>M</td><td>消息发送时间戳，yyyymmddhhmmss 本地时间(北京时间)</td></tr><tr><td>msgType</td><td>String</td><td>50</td><td>M</td><td>消息类型，参考各个接口中的定义</td></tr><tr><td>signType</td><td>String</td><td>10</td><td>M</td><td>签名方式，当前默认为SM2</td></tr><tr><td>clientCertld</td><td>String</td><td>128</td><td>M</td><td>客户端使用请求签名的秘钥对的ID，由服务端分配</td></tr><tr><td>encCertld</td><td>String</td><td>128</td><td>M</td><td>加密的秘钥ID，由双方协商</td></tr><tr><td>signature Data</td><td>String String</td><td>2048 4000</td><td>M</td><td>签名，参考安全要求中的签名说明</td></tr><tr><td colspan="2" rowspan="2"></td><td rowspan="2">000</td><td rowspan="2">M</td><td rowspan="2">iv:data</td></tr><tr><td>iv:加密使用的盐值 data：加密之后的业务域JSON字符串，参考安全要 求中的加密说明</td></tr></table></body></html>  

<html><body><table><tr><td rowspan="5"></td><td>"timestamp":"**************",</td></tr><tr><td>"msgType":"account.create",</td></tr><tr><td>"signType": "SM2",</td></tr><tr><td>"clientCertld":"*********",</td></tr><tr><td>"encCertld": "**********",</td></tr><tr><td>"signature":"xxxxxxxx", "data": "xxxxxxxx" }</td><td></td></tr></table></body></html>  

# 3.1.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>version</td><td>String</td><td>10</td><td>M</td><td>应用版本号，当前默认为1.0.0</td></tr><tr><td>sourceld</td><td>String</td><td>10</td><td>M</td><td>调用方标识，由被调用方分配</td></tr><tr><td>msgId</td><td>String</td><td>20</td><td>M</td><td>消息流水号，保证唯一</td></tr><tr><td>timestamp</td><td>String</td><td>14</td><td>M</td><td>消息发送时间戳，yyyymmddhhmmss 本地时间(北京时间)</td></tr><tr><td>msgType</td><td>String</td><td>50</td><td>M</td><td>消息类型，参考各个接口中的定义</td></tr><tr><td>signType</td><td>String</td><td>10</td><td>M</td><td>签名方式，当前默认为SM2</td></tr><tr><td>serverCertld</td><td>String</td><td>128</td><td>M</td><td>服务端使用响应签名的秘钥对的ID，由客户端分配</td></tr><tr><td>encCertld</td><td>String</td><td>128</td><td>M</td><td>加密的秘钥ID，由双方协商</td></tr><tr><td>signature</td><td>String</td><td>2048 4098</td><td>M</td><td>签名，参考安全要求中的签名说明</td></tr><tr><td colspan="2">data</td><td>String</td><td>M</td><td>iv:加密使用的盐值 data：加密之后的业务域JSON字符串，参考安全要 求中的加密说明</td></tr><tr><td>{ }</td><td colspan="4">"version": "1.0.0", "channelld": "LEXIN", "msgld": "0a2d9783687911f7816879d1f78c0000", "timestamp": "**************", "msgType": "account.create", "signType": "SM2", "serverCertld": "**********", "encCertld": "**********", "signature":"xxxxxxxx", "data": "xxxxxxxx"</td></tr></table></body></html>  

# 3.1.3 公共错误码  

<html><body><table><tr><td>resultco</td><td>Resultdesc</td></tr><tr><td>de 0</td><td>success(成功)</td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td>E900001</td><td>参数检查失败</td></tr><tr><td>E900002</td><td>消息格式错误</td></tr><tr><td>E900003</td><td>报文长度超限</td></tr><tr><td>E900004</td><td>接口流量超限</td></tr><tr><td>E900005</td><td>安全认证失败</td></tr><tr><td>E900006</td><td>msgType不存在</td></tr></table></body></html>  

<html><body><table><tr><td>sp 侧接口</td><td>接口名</td><td>错误码</td><td>错误信息</td><td>场景描述</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010101</td><td></td><td>实名信息校验失败</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010102</td><td></td><td>黑名单用户</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010103</td><td></td><td>准入失败</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010104</td><td></td><td>手机号已使用</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010105</td><td></td><td>只支持身份证申请</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010106</td><td></td><td>客户或者配偶存在在途的信用 贷款</td></tr><tr><td>account.check</td><td>用户准入</td><td>E010107</td><td></td><td>您（或您的共同借款人）为我 行关联人或关系人，不符合本 笔贷款申请或出账要求</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010304</td><td></td><td>用户注册失败</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010306</td><td></td><td>用户已注销</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010307</td><td></td><td>用户注销失败</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010308</td><td></td><td>授信审核不通过</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010309</td><td></td><td>添加联系人失败</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010310</td><td></td><td>用户信息缺失</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010311</td><td></td><td>用户身份证信息超-30分钟失</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>效</td></tr><tr><td>credit.confirm</td><td>授信确认</td><td>E010312</td><td></td><td>用户人脸信息超30分钟失效</td></tr><tr><td>credit.status.query</td><td>授信结果查询</td><td>E010401</td><td></td><td>身份信息校验失败</td></tr><tr><td>credit.status.query</td><td>授信结果查询</td><td>E010402</td><td></td><td>不满足18岁</td></tr><tr><td>credit.status.query</td><td>授信结果查询</td><td>E010403</td><td></td><td>无效的身份证信息</td></tr><tr><td>credit.account.query</td><td>授信关系查询</td><td>E010501</td><td></td><td>账号无效</td></tr><tr><td>account.phoneno.cha nge</td><td>更换手机号</td><td>E010801</td><td></td><td>无效手机号</td></tr><tr><td>face.check</td><td>活体人脸校验</td><td>E010901</td><td></td><td>图片不清晰，验证失败</td></tr><tr><td>face.check</td><td>活体人脸校验</td><td>E010902</td><td></td><td>人脸和身份信息不匹配</td></tr><tr><td>account.lifecycle</td><td>生命周期操作</td><td>E011401</td><td></td><td>生命周期操作类型不支持</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011001</td><td></td><td>图片不清晰，验证失败</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011002</td><td></td><td>身份证件和实名信息不匹配</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011003</td><td></td><td>ocr校验失败</td></tr><tr><td>userinfo.supplement</td><td>个人资料补充</td><td>E011004</td><td></td><td>内容包含敏感词，请重新填写</td></tr><tr><td>userinfo.supplement</td><td>个人资料补充</td><td>E011201</td><td></td><td>详细地址校验未通过</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011005</td><td></td><td>您上传的图片中不包含身份证 身份证影像，请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011006</td><td></td><td>您上传的身份证影像模糊，请 重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011007</td><td></td><td>您上传的身份证影像关键字段 不清晰，请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011008</td><td></td><td>您上传的身份证影像亮度过 低，请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011009</td><td></td><td>您上传的身份证影像为复印 件，请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011010</td><td></td><td>您上传的身份证影像为临时身 份证，请上传正式身份证影像</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011011</td><td></td><td>您上传的身份证影像为翻拍， 请重新上传</td></tr></table></body></html>  

<html><body><table><tr><td>eid.upload</td><td>身份证上传</td><td>E011012</td><td></td><td>需要您上传身份证影像的原始 照片，请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011013</td><td></td><td>您上传的照片不是身份证影 像，请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011014</td><td></td><td>您的身份证影像正反面颠倒， 请重新上传</td></tr><tr><td>eid.upload</td><td>身份证上传</td><td>E011015</td><td></td><td>您上传的身份证已过有效期， 请办理后重新上传</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040201</td><td></td><td>卡bin不支持，请查询支持的 卡列表</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040202</td><td></td><td>卡号和实名信息不一致</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040203</td><td></td><td>银行卡预留手机号不一致</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040205</td><td></td><td>银行卡已绑定</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040204</td><td></td><td>发送验证码失败，请稍后再试</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040206</td><td></td><td>获取短信验证码次数超限，请 明天再试</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040207</td><td></td><td>获取验证码频率过高</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040208</td><td></td><td>不支持添加信用卡，请更换借记卡</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040209</td><td></td><td>通道不支持或发卡行不支持的 交易时间</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040210</td><td></td><td>系统正忙 (同一用户在1分钟 内重复调用发短)</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040211</td><td></td><td>暂停非柜面交易</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040212</td><td></td><td>卡号已销户</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040213</td><td></td><td>其他签约失败</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040214</td><td></td><td>支付机构无权发起的交易类型</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040215</td><td></td><td>卡号状态异常</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040216</td><td></td><td>卡号已挂失</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040217</td><td></td><td>风控拦截</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040218</td><td></td><td>该卡已关闭快捷支付</td></tr></table></body></html>  

<html><body><table><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040219</td><td></td><td>短信触发时间间隔不能短于 50秒</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040220</td><td></td><td>签约人账号请求签约频率超过 付款行限制</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040221</td><td></td><td>签约行的身份验证授权信息下 发失败</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040222</td><td></td><td>请稍后，正在处理中…</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040223</td><td></td><td>银行预留证件信息已过期</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040224</td><td></td><td>暂停非柜面交易</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040225</td><td></td><td>卡号已销户</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040226</td><td></td><td>其他签约失败</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040227</td><td></td><td>支付机构无权发起的交易类型</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040228</td><td></td><td>卡号状态异常</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040229</td><td></td><td>卡号已挂失</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040230</td><td></td><td>风控拦截</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040231</td><td></td><td>该卡已关闭快捷支付</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040232</td><td></td><td>签约行的身份验证授权信息下 发失败</td></tr><tr><td>bindcard.apply</td><td>请求绑卡</td><td>E040233</td><td></td><td>绑卡数量达到上限</td></tr><tr><td>bindcard.confirm</td><td>绑卡确认</td><td>E040301</td><td></td><td>卡 bin 不支持，请查询支持的 卡列表</td></tr><tr><td>bindcard.confirm</td><td>绑卡确认</td><td>E040302</td><td></td><td>卡号和实名信息不一致</td></tr><tr><td>bindcard.confirm</td><td>绑卡确认</td><td>E040303</td><td></td><td>银行卡预留手机号不一致</td></tr><tr><td>bindcard.confirm</td><td>绑卡确认</td><td>E040305</td><td></td><td>绑卡失败，请重试</td></tr><tr><td>bindcard.confirm</td><td>验证码校验</td><td>E040304</td><td></td><td>验证码错误</td></tr><tr><td>bindcard.confirm</td><td>验证码校验</td><td>E040306</td><td></td><td>验证码已失效，请重试</td></tr><tr><td>bindcard.confirm</td><td>验证码校验</td><td>E040307</td><td></td><td>验证短信无效的序列号</td></tr><tr><td>bindcard.confirm</td><td>绑卡确认</td><td>E040308</td><td></td><td>预签约未成功，请重新预签约</td></tr><tr><td>account.repay.set</td><td>用户自动还款设置</td><td>E040401</td><td></td><td>银行卡不支持设置自动还款</td></tr></table></body></html>  

<html><body><table><tr><td>account.repay.set</td><td>用户自动还款设置</td><td>E040402</td><td></td><td>用户自动还款设置失败</td></tr><tr><td>bindcard.delete</td><td>删除绑定卡</td><td>E040606</td><td></td><td>不允许解绑</td></tr><tr><td>bindcard.delete</td><td>删除绑定卡</td><td>E040607</td><td></td><td>解绑失败，请重试</td></tr><tr><td>bindcard.delete</td><td>删除绑定卡</td><td>E040608</td><td></td><td>用户绑卡不存在</td></tr><tr><td>bindcard.delete</td><td>删除绑定卡</td><td>E040609</td><td></td><td>银行卡未绑定</td></tr><tr><td>bindcard.delete</td><td>删除绑定卡</td><td>E040610</td><td></td><td>有未结清账单，不允许进行 解绑操作</td></tr><tr><td>card.bin.check</td><td>卡Bin校验</td><td>E040701</td><td></td><td>卡bin不支持，请查询支持的 卡列表</td></tr><tr><td>corporate.account.add</td><td>添加对公账户</td><td>E040901</td><td></td><td>重复添加</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y,borrow.confirm,borr ow.offer.confirm</td><td>用信</td><td>E050101</td><td></td><td>不支持所选分期数</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y,borrow.confirm,borr ow.offer.confirm</td><td>用信</td><td>E050102</td><td></td><td>不存在对应费率信息</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y,borrow.confirm,borr ow.offer.confirm</td><td>用信</td><td>E050103</td><td></td><td>不存在对应还款计划信息</td></tr><tr><td>borrow.confirm,borro w.offer.confirm</td><td>订单确认</td><td>E050104</td><td></td><td>订单放款失败</td></tr><tr><td>borrow.apply</td><td>用信</td><td>E050105</td><td></td><td>有申请中的订单</td></tr><tr><td>borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050106</td><td></td><td>核身校验失败，请重新进行 核身校验</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl</td><td>用信</td><td>E050107</td><td></td><td>额度不足</td></tr></table></body></html>  

<html><body><table><tr><td>y,borrow.confirm,borr ow.offer.confirm</td><td></td><td></td><td></td><td></td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y,borrow.confirm,borr ow.offer.confirm</td><td>用信</td><td>E050108</td><td></td><td>未绑定银行卡</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y,borrow.confirm,borr ow.offer.confirm</td><td>用信</td><td>E050109</td><td></td><td>下单失败</td></tr><tr><td>borrow.status.query,b orrow.order.cancel,bo rrow.order.detail,borr ow.order.query</td><td>用信查询</td><td>E050110</td><td></td><td>订单不存在</td></tr><tr><td>borrow.order.cancel</td><td>取消未完成订单</td><td>E050111</td><td></td><td>订单取消失败</td></tr><tr><td>borrow.order.cancel</td><td>取消未完成订单</td><td>E050112</td><td></td><td>订单状态不合法，无法取消</td></tr><tr><td>borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050113</td><td></td><td>订单状态不合法，无法确认 订单</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y,borrow.confirm,borr ow.offer.confirm</td><td>用信</td><td>E050114</td><td></td><td>用户授信已过期</td></tr><tr><td>borrow.trial.default,bo rrow.trial,borrow.appl y</td><td>用信</td><td>E050115</td><td></td><td>用信申请金额非法</td></tr><tr><td>borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050116</td><td></td><td>身份证件和实名信息不匹配</td></tr><tr><td>borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050117</td><td></td><td>人脸和身份信息不匹配</td></tr><tr><td>borrow.apply,borrow. confirm,borrow.offer.c onfirm</td><td>用信</td><td>E050118</td><td></td><td>有放款中的订单</td></tr></table></body></html>  

<html><body><table><tr><td>borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050119</td><td></td><td>确认的订单不存在</td></tr><tr><td>borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050120</td><td></td><td>补录地址格式错误</td></tr><tr><td>borrow.trial.default,bo rrow.trial borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050121</td><td></td><td>借款金额超限</td></tr><tr><td>borrow.trial.default,bo rrow.trial borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050122</td><td></td><td>优惠券已过期</td></tr><tr><td>borrow.trial.default,bo rrow.trial borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050123</td><td></td><td>当前订单不支持该优惠券</td></tr><tr><td>borrow.trial.default,bo rrow.trial borrow.confirm,borro w.offer.confirm</td><td>用信</td><td>E050124</td><td></td><td>当前优惠券活动未开始</td></tr><tr><td>borrow.trial.default,bo rrow.trial</td><td>用信</td><td>E050125</td><td></td><td>订单申请中，不允许试算</td></tr><tr><td>borrow.trial,borrow.ap ply</td><td>用信</td><td>E050126</td><td></td><td>借款时间已超额度有效期</td></tr><tr><td>borrow.trial.default</td><td>用信</td><td>E050127</td><td></td><td>无可用期限</td></tr><tr><td>repay.trial</td><td>提前还款试算</td><td>E060001</td><td></td><td>还款试算失败</td></tr><tr><td>repay.plan.query</td><td>查询月账单/所有 借贷</td><td>E060002</td><td></td><td>查询账单信息异常</td></tr><tr><td>repay.order.detail.que ry</td><td>查询还款记录详 情</td><td>E060003</td><td></td><td>查询账单详情异常</td></tr></table></body></html>  

<html><body><table><tr><td>repay.order.detail.que ry,repay.status.query, repay.order.query,rep ay.plan.query</td><td>查询还款</td><td>E060004</td><td></td><td>查询账单为空</td></tr><tr><td>repay.order.detail.que ry,repay.status.query, repay.order.query,rep ay.plan.query</td><td>查询还款</td><td>E060005</td><td></td><td>没有匹配的账单</td></tr><tr><td>repay.order.detail.que ry,repay.status.query, repay.order.query,rep ay.plan.query</td><td>查询还款</td><td>E060006</td><td></td><td>获取订单失败</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060007</td><td></td><td>不支持的还款类型</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060008 E060009</td><td></td><td>无待还款账单</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td></td><td></td><td>还款期数异常(：入参还款账 单转换成乐信侧还款账单为 空；入参还款订单没有待还 账单；传入账单不是待还账 单；传入账单期数存在跳期 还款)</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060011</td><td></td><td>还款金额不正确</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060012</td><td></td><td>还款订单为空</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060013</td><td></td><td>还款账单为空</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060014</td><td></td><td>不支持混合还款类型</td></tr></table></body></html>  

<html><body><table><tr><td>repay.confirm</td><td>确认还款</td><td>E060015</td><td></td><td>创建还款单失败</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060016</td><td></td><td>还款类型不匹配</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060017</td><td></td><td>订单状态已结清</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060018</td><td></td><td>订单当期状态已结清</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060019</td><td></td><td>订单处于禁还期，借款当日还 款</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060020</td><td></td><td>借款正在还款处理中，不可再 次发起还款</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060021</td><td></td><td>还款金额小于最小自定义还款 金额</td></tr><tr><td>repay.confirm</td><td>还款</td><td>E060022</td><td></td><td>自动扣款中，请稍后再还</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060023</td><td></td><td>不支持全部提前还款</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060024</td><td></td><td>不支持提前还款</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060025</td><td></td><td>逾期用户进入系统准备还款的 同时三方已经自动扣款，导致 用户主动失败</td></tr><tr><td>repay.trial,repay.confi rm</td><td>还款</td><td>E060026</td><td></td><td>存在逾期，不支持提前还款</td></tr><tr><td>repay.confirm</td><td>确认还款</td><td>E060027</td><td></td><td>确认还款存在异常 (广发)</td></tr><tr><td>repay.confirm</td><td>确认还款</td><td>E060028</td><td></td><td>还款失败，您的交易金额超 过银行卡限额</td></tr><tr><td>repay.confirm</td><td>确认还款</td><td>E060029</td><td></td><td>还款失败，账户可用余额不</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>足</td></tr><tr><td>repay.plan.query</td><td>查询月账单</td><td>E060030</td><td></td><td>二类户无交易锁定</td></tr><tr><td>repay.confirm</td><td>确认还款</td><td>E060031</td><td></td><td>还款失败，当前银行卡不支 持还款 (广发专用)</td></tr><tr><td>account.settlement.qu|用户开具证明接口 ery</td><td></td><td>E070101</td><td></td><td>订单未结清</td></tr><tr><td>account.settlement.qu|用户开具证明接口 ery</td><td></td><td>E070102</td><td></td><td>结清次数已用完</td></tr><tr><td>account.settlement.se|用户开具证明接口 nd</td><td></td><td>E070103</td><td></td><td>用户同一天用同一个邮箱 （已结清的借据相同）开具 结清证明</td></tr><tr><td>account.settlement.qu ery,account.settlemen t.send</td><td>用户开具、发送结 清证明</td><td>E070105</td><td></td><td>操作过于频繁</td></tr><tr><td>account.settlement.qu ery,account.settlemen t.send</td><td>用户开具、发送结 清证明</td><td>E070106</td><td></td><td>未到开具的时间</td></tr><tr><td>account.settlement.qu ery,account.settlemen t.send</td><td>用户开具、发送结 清证明</td><td>E070107</td><td></td><td>资方不支持开具结清证明</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  

# 3.2 数据对象定义  

# 3.2.1 数据对象定义说明  

数据对象定义表格中包含如下几个字段，其说明如下  

<html><body><table><tr><td></td><td>Description</td></tr><tr><td>字段名称</td><td>JSON消息体中的key</td></tr><tr><td>类型</td><td>JSON 消息体中的Value类型，支持的类型有： String:字符串 Boolean：true/false。该value 在json 中无需加上引号 Integer：整数。该value在json中无需加上引号 其他：自定义的对象类型，该值为对象类型名称 如果是数组，定义为List<类型>，比如List<String>代表字符串数组</td></tr><tr><td>长度</td><td>字符串最大长度</td></tr><tr><td>M/0</td><td>是否必选 M：必选 O：可选 C：条件必选</td></tr><tr><td>描述</td><td>参数描述</td></tr></table></body></html>  

# 3.2.2 公共数据对象  

# UserInfo 用户信息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>certifld</td><td>String</td><td>18</td><td>M</td><td>身份证号码</td></tr></table></body></html>  

<html><body><table><tr><td>name</td><td>String</td><td>30</td><td>M 姓名</td><td></td></tr><tr><td>phoneNo</td><td>String</td><td>11</td><td>M</td><td>手机号，不带+86</td></tr><tr><td>job</td><td>String</td><td>2</td><td>C 职业</td><td>01党政机关/事业单位 02企业职员 03工人/服务人员</td></tr><tr><td>income</td><td>String</td><td>10 C</td><td>10其他</td><td>05学生 06 自由职业 07 律师/会计等专业技术人员 08 金融行业从业人员 09 农/林/牧/渔从业人员 01无稳定收入</td></tr><tr><td></td><td></td><td></td><td></td><td>023千以下 03 3千-5千 045千-1万 051万-2万</td></tr><tr><td>company</td><td>String</td><td>128 NA</td><td>单位名称</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td>2</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>062万-3万</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>073万以上</td></tr><tr><td>addressInfo</td><td>AddressInfo</td><td></td><td>C</td><td></td></tr><tr><td>degree</td><td>String</td><td></td><td>C 地址</td><td></td></tr><tr><td></td><td></td><td></td><td>C 学历</td><td></td></tr><tr><td></td><td></td><td></td><td>1-本科</td><td></td></tr><tr><td></td><td></td><td></td><td>2-大专</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>3-硕士以上</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>4-初中及以下</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>5-中专</td><td></td></tr><tr><td></td><td></td><td></td><td>6-高中</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>industry</td><td>String</td><td>8</td><td>C 行业投向</td><td></td></tr></table></body></html>  

# ImageInfo 身份证影像信息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td>facelmageType</td><td>String</td><td>2</td><td>M</td><td>身份证正面图片类型 01-jpg 02-png 03-jpeg</td></tr><tr><td>facelmageContent</td><td>String</td><td>500K</td><td>M</td><td>04-bmp 身份证正面图片，deflate压缩算法之后再 Base64</td></tr><tr><td>backlmageType</td><td>String</td><td>2</td><td>M</td><td>身份证背面图片类型 01-jpg 02-png 03-jpeg</td></tr><tr><td>backlmageContent</td><td>String</td><td>500K</td><td>M</td><td>04-bmp 身份证正面图片，deflate压缩算法之后再 Base64</td></tr><tr><td>compressionType</td><td>String</td><td>2</td><td>0</td><td>压缩类型 不传、为空：deflate压缩 0：不压缩 1:deflate压缩</td></tr></table></body></html>  

# FaceImageInfo 活体人脸信息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>facelmageType</td><td>String</td><td>2</td><td>M</td><td>正面图片类型 01-jpg 02-png 03-jpeg</td></tr><tr><td>facelmageConten t</td><td>String</td><td>500K</td><td>M</td><td>04-bmp 正面图片内容，deflate压缩算法之后再 Base64</td></tr></table></body></html>  

# FaceVideoInfo 活体人脸视频信息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td rowspan="3">faceVideoType</td><td rowspan="3">String</td><td rowspan="3">2</td><td>M</td><td>视频类型</td></tr><tr><td></td><td>01-mp4</td></tr><tr><td>02-avi 03-mov</td><td></td></tr><tr><td rowspan="2"></td><td rowspan="2"></td><td rowspan="2"></td><td rowspan="2"></td><td>04-flv</td></tr><tr><td>05-webm</td></tr><tr><td>faceVideoContent</td><td>String</td><td>500K</td><td>M</td><td>视频内容，deflate压缩算法之后再 Base64</td></tr></table></body></html>  

# ContactInfo 联系人信息  

OtpInfo 短信信息  


<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>name</td><td>String</td><td>128</td><td>M</td><td>姓名</td></tr><tr><td>phoneNo</td><td>String</td><td>11</td><td>M</td><td>手机</td></tr><tr><td rowspan="3">type</td><td rowspan="3">String</td><td rowspan="3">2</td><td rowspan="3">0</td><td>类型</td></tr><tr><td>01-父母 02-配偶</td></tr><tr><td>03-兄弟姐妹</td></tr><tr><td></td><td></td><td></td><td></td><td>04-其他</td></tr></table></body></html>  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/ 。</td><td>字段描述</td></tr><tr><td>codeld</td><td>String</td><td>36</td><td>M</td><td>OTP流水号</td></tr><tr><td>otpValue</td><td>String</td><td>8</td><td>M</td><td>OTP验证码</td></tr><tr><td>otpType</td><td>String</td><td>32</td><td>M</td><td>短信类型 用信：borrowApplySP 还款卡重签约：repayResignSP</td></tr></table></body></html>  

# CreditSignInfo 授信签约关系  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C M</td><td>签约单号(成功时返回) 当前签约状态:</td></tr><tr><td>signStatus</td><td>String</td><td>2</td><td></td><td>0：授信中断 1:审核中 2:授信成功 (已签约) 3:审批未通过 4:已过期 5：冻结 7：清退 8：审批中待补录/待激活 99：未授信 (微业贷)</td></tr><tr><td>totalAmount</td><td>String</td><td>20</td><td>C</td><td>总额度，单位是分，已签约状态返回，</td></tr><tr><td>tempAmount</td><td></td><td></td><td></td><td>返回最新的</td></tr><tr><td>tempRemainAmount</td><td>String String</td><td>20 20</td><td>C C</td><td>临时额度，单位是分 剩余临时额度，单位是分</td></tr></table></body></html>  

<html><body><table><tr><td>tempAmtExpireDate</td><td>String</td><td>16</td><td>0</td><td>临额有效截止日期: 不传、为空：无有效期，</td></tr><tr><td>remainAmount</td><td>String</td><td>20</td><td>C</td><td>格式为yyyy-MM-dd 剩余额度，单位是分，已签约状态返回， 返回最新的</td></tr><tr><td>repayAmount</td><td>String</td><td>20</td><td>C</td><td>已用额度,单位是分，已签约状态返回， 返回最新的</td></tr><tr><td>dailyRate</td><td>String</td><td>20</td><td>C</td><td>日利率，单位是%，已签约状态返回，返 回最新的</td></tr><tr><td>comDailyRate</td><td>String</td><td>20</td><td>C</td><td>综合日利率，单位是%，已签约状态返 回，返回最新的</td></tr><tr><td>annualRate</td><td>String</td><td>20</td><td>C</td><td>年利率，单位是%，已签约状态返回，返 回最新的</td></tr><tr><td>comAnnualRate</td><td> String</td><td>20</td><td>C</td><td>综合年利率，单位是%，已签约状态返 回，返回最新的</td></tr><tr><td>effectiveDate</td><td> String</td><td>16</td><td>C</td><td>签约关系生效时间，yyyy-mm-dd， 已签约状态返回</td></tr><tr><td>expiredDate</td><td>String</td><td>16</td><td>C</td><td>签约关系失效时间，yyyy-mm-dd， 已签约状态返回</td></tr><tr><td>repaymentDay</td><td>String</td><td>2</td><td>C</td><td>每月还款日，每月10日还款，该值为 10，已签约状态返回</td></tr><tr><td>nextSignDate</td><td>String</td><td>16</td><td>C</td><td>如果审批拒绝，允许再次申请的最早日 期</td></tr><tr><td>couponInfos</td><td>List <CouponInfo></td><td>NA</td><td>C</td><td>格式为yyyy-MM-dd 如果有可享受的营销活动</td></tr><tr><td>regular</td><td>String</td><td>1</td><td>0</td><td>1老用户0新用户，已签约状态返回</td></tr><tr><td>entrepreneurTag</td><td>String</td><td>1</td><td>0</td><td>1企业用户0普通用户2个体工商户, 营业执照验证后，签约成功、失败都需 告知是否是企业用户 (微业贷需要)</td></tr><tr><td>loanProduetType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan: 小微企业贷 ereditLoan:信用贷</td></tr><tr><td>businessLicenseNum</td><td>String</td><td>32</td><td>0</td><td>不传：默认为信用贷 营业执照编号</td></tr><tr><td>licenseNumType</td><td>String</td><td>2</td><td>0</td><td>编号类型1：营业执照、2：工商注册号</td></tr><tr><td>enhanceAmount</td><td>String</td><td>20</td><td>0</td><td>可主动提额时返回可提额金额，单位分</td></tr><tr><td>changelnfos</td><td>List<Changelnfo</td><td>NA</td><td>0</td><td>额度或利率发生变化时返回</td></tr><tr><td>miniBorrowAmount</td><td>V String</td><td>20</td><td>C</td><td>最低借款金额，已签约状态返回</td></tr><tr><td>maxBorrowAmount</td><td>String</td><td>20</td><td>C</td><td>最高借款金额，已签约状态返回</td></tr><tr><td>autoRepay</td><td>String</td><td>1</td><td>C</td><td>自动还款0开启1未开启，已签约状态 返回</td></tr></table></body></html>  

RiskInfo（风控信息）  


<html><body><table><tr><td>overdueAmount</td><td>String</td><td>20</td><td>C</td><td>如果存在逾期账单，返回总逾期金额， 单位分</td></tr><tr><td>loanChannel</td><td>String</td><td>32</td><td>0</td><td>渠道信息 -normal:借贷渠道 -hirePurchase:分期购渠道</td></tr><tr><td>tempDailyRate</td><td>String</td><td>20</td><td>C</td><td>临时日利率，单位是%</td></tr><tr><td>tempAnnualRate</td><td>String</td><td>20</td><td>C</td><td>临时年利率，单位是%</td></tr><tr><td>tempExpireDate</td><td>String</td><td>16</td><td>0</td><td>临价有效截止日期: 不传、为空：无有效期, 格式为yyyy-MM-dd</td></tr><tr><td>dailyRepayAmt Prome</td><td>String</td><td>20</td><td>E</td><td>每目还款额宣传信息（1千用1天）</td></tr><tr><td>scheduleRepay</td><td>String</td><td></td><td></td><td>2:仅支持按期还 1:支持按期还、支持灵活还 0、不传：仅支持灵活还 备注：该字段用于判断用户支持的还款 方式，区别于用信接口上送的 scheduleRepay，由于历史版本原因</td></tr><tr><td>canCreditChange</td><td>String</td><td>2</td><td>0</td><td>2：提额申请中 1：支持申请提额降息 0、不传：不支持提额降息</td></tr><tr><td>enterpriseName</td><td>String</td><td>128</td><td>0</td><td>企业名称 (微业贷需要)</td></tr><tr><td>enterpriseId</td><td>String</td><td>64</td><td>0</td><td>企业id (微业贷需要)</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td></td><td>平台分配账户号（微业贷使用，从未授 信过的企业可不分配)</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>deviceld</td><td>String</td><td>12 8</td><td>M</td><td>设备码， los: IDFV 安卓:androidID</td></tr><tr><td>positionlnfo</td><td>String</td><td>64</td><td>M如</td><td>位置信息，坐标系安卓：bd09Il（百度坐标系），例 42.4697721,121.2540492</td></tr></table></body></html>  

<html><body><table><tr><td>isUsbDebug</td><td>S wiredEarphoneStatu</td><td>blueEarphoneStatus</td><td> systemCallStatus</td><td></td><td>voiceCallStatus system</td><td>systemVersion</td><td>screenHeight</td><td>screenWidth</td><td>imei</td><td>wifiFinger</td><td>currWifiFinger</td><td>deviceName</td><td>clientVersion</td><td> simSerialNum</td><td> isVirtual</td><td>isRoot</td><td></td><td>imsi ipAddress</td></tr><tr><td>String</td><td>String</td><td></td><td>String</td><td>String</td><td>String String</td><td>String 6</td><td>String String 6 6</td><td>String</td><td>String</td><td></td><td>String</td><td>String</td><td>String</td><td>String</td><td>String</td><td>String</td><td>String</td><td>String</td></tr><tr><td>8</td><td>8</td><td>8</td><td>8</td><td>8</td><td>8</td><td>8 0 0</td><td>8</td><td>8 2E 0</td><td>95z 0</td><td></td><td>8π</td><td>95</td><td></td><td>8</td><td>8 W</td><td>8 W</td><td>2E 0</td><td>2E</td></tr><tr><td>0 增 否开启usb调试</td><td>0 ↓ 单 未知，0 有线耳机状 否，1是</td><td>0 知，0 蓝牙耳机状态 否，1是 举</td><td>0 工裆 未知，0 统通话状态 否，1是</td><td>未知，0 否，1是</td><td>0 0 01-Android, ( 吾音通话状态 02-i0S</td><td>操作系统版本 本号</td><td>率高</td><td>0 率宽</td><td>IMEI （用户设备 设备信息</td><td>wifi1 信号列表</td><td>0 -LINK_ {"mac":"0:1:22:3:4:5""si":"ssid":" 原 _111"} Wi-Fi指纹， 例如：</td><td>0 设备名，例如： xxx's mateX</td><td>0 吾</td><td>0</td><td>01-是;02-否;03-未知 备是否虚拟机</td><td>01-是;02-否；03-未矢 机是否被ROOT</td><td>W IMSI ( （用户SIM卡信 端IP 信息）</td></tr></table></body></html>  

RepayTypeInfo （还款方式宣传信息）  


<html><body><table><tr><td></td><td></td><td></td><td></td><td>01-是;02-否;03-未知</td></tr><tr><td>isInjection</td><td>String</td><td>8</td><td></td><td>APP 是否被注入 01-是;02-否;03-未知</td></tr><tr><td>isRepackage</td><td>String</td><td>8</td><td>0</td><td>宿主是否被 repackage 01-是;02-否;03-未知</td></tr><tr><td>isUsbConnected</td><td> String</td><td>8</td><td>o</td><td>当前是否连接流量USB 01-是;02-否;03-未知</td></tr><tr><td>debugState</td><td>String</td><td>8</td><td></td><td>手机是否调试状态 01-是;02-否;03-未知</td></tr><tr><td>isVpn</td><td>String</td><td>8</td><td>0</td><td>是否使用vpn 01-是;02-否;03-未知</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>还款类型 1：每月等额（还款总金额一 致，不保证每一期的本金及利息 是一致的) 2：先息后本 3：等本等息（每一期的本金 和利息都相等) 4：等额本金 (每个月按固定 的金额计算利息，既本金每一期</td></tr><tr><td>totalAmt</td><td>String</td><td></td><td></td><td>都相等) 合计需还，单位分</td></tr><tr><td>principalAmt</td><td>String</td><td>20 20</td><td>C M</td><td>本金，单位分</td></tr><tr><td>serviceAmt</td><td>String</td><td>20</td><td>M</td><td>服务费用，单位分</td></tr><tr><td>rate</td><td>String</td><td>20</td><td>C</td><td>利率，单位是%（非微众必</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>传）</td></tr><tr><td>fqNums</td><td>String</td><td>64</td><td>0</td><td>支持的分期数，如:3|6|9。 注意：为空时支持所有分期场景</td></tr></table></body></html>  

# RepayFqInfo（分期宣传信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>分期数，如：12 (期)</td></tr><tr><td>remark</td><td> String</td><td>64</td><td>0</td><td>标签文案，如：“长账期，压力 小”</td></tr></table></body></html>  

# RepayPlanInfo （还款计划信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号（默认试算、试算接 □响应中为非必传)</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>分期数 (借款订单的)</td></tr><tr><td>borrowDate</td><td>String</td><td>16</td><td>M</td><td>借款时间，格式yyyy-mm-dd</td></tr><tr><td>principalAmt</td><td>String</td><td>20</td><td>M</td><td>借款本金，单位分（借款订单 的)</td></tr><tr><td>fqIndex</td><td>String</td><td>8</td><td>M</td><td>第几期，1-N</td></tr><tr><td>repayDate</td><td>String</td><td>16</td><td>M</td><td>本期还款日，格式yyyy-mm-dd</td></tr><tr><td>leftRepayTotalA mt</td><td>String</td><td>20</td><td>M</td><td>本期总待还金额，单位分</td></tr><tr><td>repayTotalAmt</td><td>String</td><td>20</td><td>M</td><td>本期共还金额，单位分 本期未结清，返回总待还 本期结清，返回共还</td></tr><tr><td>repayPrincipalAmt</td><td>String</td><td>20</td><td>M</td><td>本期总本金，单位分（还款试算 接口响应中为“本期待还本</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>金”）</td></tr><tr><td>repaylnterestAmt</td><td>String</td><td>20</td><td>M</td><td>本期总利息，单位分（还款试算 接口响应中为“本期待还利</td></tr><tr><td>repayDiscount</td><td> String</td><td>20</td><td>0</td><td>息”） 本期优惠信息，单位分</td></tr><tr><td>couponNum</td><td>String</td><td>64</td><td>0</td><td>优惠券号</td></tr><tr><td>punishlnterest</td><td>String</td><td>20</td><td>0</td><td>罚息，单位分</td></tr><tr><td>interestDays</td><td> String</td><td>8</td><td></td><td>记息天数</td></tr><tr><td> scheduleRepay</td><td>String</td><td>1</td><td>0</td><td>1:按期还</td></tr><tr><td>violateAmount</td><td>String</td><td>20</td><td>C</td><td>0：灵活还 本期违约金</td></tr><tr><td> penaltyRate</td><td>String</td><td>20</td><td>0</td><td>提前结清违约金利率，单位是%</td></tr><tr><td> status</td><td>String 8</td><td></td><td>M</td><td>是否已还清（默认试算、试算接 口响应中为非必传) 0：已还清 1：正常未还清 2：已逾期 3：自动还款失败</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>4:禁还 还款类型 1：每月等额（还款总金额一 致，不保证每一期的本金及利息 是一致的) 2：先息后本 3：等本等息（每一期的本金 和利息都相等) 4：等额本金 (每个月按固定 的金额计算利息，既本金每一期 都相等)</td></tr></table></body></html>  

<html><body><table><tr><td>capitalType</td><td>String</td><td>2</td><td>C</td><td>资方类型 1:自营 2：联合贷 3：助贷</td></tr><tr><td>capitalName</td><td>String</td><td>128</td><td>C</td><td>资方名称，如: 重庆度小满小贷 度小满&合作机构 江苏银行</td></tr></table></body></html>  

# hirePlanInfo（分期计划信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>fqNum</td><td>String</td><td>8號</td><td>M</td><td>分期数</td></tr><tr><td>totalAmt</td><td>String</td><td>20</td><td>M</td><td>借款总还款额</td></tr><tr><td>principalAmt</td><td> String</td><td>20</td><td>M</td><td>借款总本金，单位分</td></tr><tr><td>interestAmt</td><td>String</td><td>20</td><td>M</td><td>借款总利息</td></tr><tr><td>discount</td><td>String</td><td>20</td><td>M</td><td>总优惠，单位分</td></tr><tr><td>repayTotalAmt</td><td>String</td><td>20</td><td>M</td><td>每期待还，单位分</td></tr><tr><td>repayPrincipalAmt</td><td>String</td><td>20</td><td>M</td><td>每期本金，单位分</td></tr><tr><td>repaylnterestAmt</td><td>String</td><td>20</td><td>M</td><td>每期利息，单位分</td></tr><tr><td>repayDiscount</td><td>String</td><td>20</td><td>0</td><td>每期优惠，单位分</td></tr></table></body></html>  

# RepayPlanSummary （还款计划汇总）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td> repayDate</td><td> String</td><td>16</td><td>M</td><td>本期还款日，格式yyyy-mm-dd</td></tr><tr><td>curRepayAmt</td><td> String</td><td>20</td><td>M</td><td>本期应还总金额，单位分</td></tr><tr><td>curPlansCnt</td><td>String</td><td>16</td><td>M</td><td>本期待还计划总笔数</td></tr></table></body></html>  

# BorrowOrderInfo（借据信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/</td><td>说明</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td colspan="2"></td><td></td></tr><tr><td>borrowOrderld</td><td>String</td><td colspan="2">32 M</td><td>借款订单号</td></tr><tr><td>orgBorrowOrderId</td><td> String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr><tr><td>mallOrderId</td><td>String</td><td>64</td><td>C</td><td>商城订单号</td></tr><tr><td rowspan="2">subMallOrderInfos</td><td>List</td><td rowspan="2">NA</td><td rowspan="2">C</td><td rowspan="2">子订单信息列表</td></tr><tr><td></td></tr><tr><td>activityCode</td><td><SubMallOrderInfo> String</td><td>64</td><td>C</td><td>活动Id</td></tr><tr><td>status</td><td>String</td><td>2</td><td>M</td><td>订单状态 00：订单未确认 01：Offer 变化未确 认 02：订单申请审批中 03：申请成功 04:申请失败 05：放款成功 06：放款失败 07：退款中 08：全额退款成功</td></tr><tr><td>resonCode</td><td>String</td><td>4 0</td><td></td><td>09：部分退款成功 10：待电话核实 11：放款成功后冲销 原因码（申请失败或 放款失败时返回)</td></tr><tr><td>principalAmt</td><td>String</td><td>20 M</td><td>回</td><td>借款本金，单位分(申 请成功或者失败后返</td></tr><tr><td>leftPrincipalAmt</td><td>String</td><td>20 C</td><td>请成功后返回)</td><td>待还本金，单位分(申</td></tr><tr><td>repayAmt</td><td>String</td><td>20 C</td><td></td><td>已还本金，单位分(申</td></tr></table></body></html>  

<html><body><table><tr><td>refundAmt</td><td>String</td><td>20</td><td>C</td><td>请成功后返回) 退款金额</td></tr><tr><td>overdueAmount</td><td>String</td><td>20</td><td>C</td><td>逾期金额，有逾期时 返回，单位分</td></tr><tr><td>fistOverdueDate</td><td>String</td><td>16</td><td>C</td><td>逾期起始日，有逾期</td></tr><tr><td>punishlnterest</td><td>String</td><td>20</td><td>C</td><td>时返回 总罚息，单位分</td></tr><tr><td>overduePrincipalAmount</td><td>String</td><td>20</td><td></td><td>逾期本金，有逾期时</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>返回，单位分 分期数，如：12 （期）（申请成功或</td></tr><tr><td>leftFqCnt</td><td>String</td><td></td><td>C</td><td>者失败后返回) 剩余分期数(申请成功</td></tr><tr><td>interestAmt</td><td>String</td><td>8 20</td><td>C</td><td>后返回) 总利息，单位分(申请</td></tr><tr><td>discount</td><td>String</td><td>20</td><td>0</td><td>成功后返回) 优惠，单位分(申请成</td></tr><tr><td></td><td></td><td></td><td></td><td>功后返回) 优惠券号（用信申请</td></tr><tr><td>couponNum</td><td>String</td><td>64</td><td>0</td><td>时使用了优惠券场 景，之后的流程都须 返回) 借款时间，格式</td></tr><tr><td>borrowDate</td><td>String</td><td>16c</td><td></td><td>yyyy-mm-dd(申请成 功或失败后返回)</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>C</td><td>收款卡SP侧绑定 ID(申请成功或者失败 后返回)</td></tr><tr><td> transParty</td><td>String</td><td>128</td><td>C</td><td>收款卡信息，银行名 称＋（卡号后四</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td>位），如:</td></tr><tr><td></td><td></td><td>128</td><td>招商银行 (2121) 放款公司名称(申请成 C</td></tr><tr><td>loanCompany protocolinfos</td><td>String List<Protocollnfo></td><td>NA</td><td>功后返回) C 借款协议(申请成功后</td></tr><tr><td></td><td></td><td></td><td>返回) 是否支持提前还款</td></tr><tr><td>canPreRePay</td><td>String</td><td>1 C</td><td>0：支持 1:不支持</td></tr><tr><td>scheduleRepay</td><td>String</td><td>1 0</td><td>(申请成功后返回) 1:按期还 0：灵活还</td></tr><tr><td> penaltyRate</td><td>String</td><td>20 0</td><td>提前结清违约金利 率，单位是%</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>还款类型 1：每月等额（还 款总金额一致，不保 证每一期的本金及利 息是一致的) 2：先息后本 3：等本等息（每 一期的本金和利息都 C 相等) 4:等额本金（每 个月按固定的金额计 算利息，既本金每一</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td>贷涉及，如前3月先 息后本后21月等额本 息）</td></tr><tr><td>repayDesc String</td><td>204 8</td><td>C</td><td>还款说明，不规则还 款需要（微业贷涉 及） 资金用途</td></tr><tr><td>usage String annualRate String</td><td>8 20</td><td>C C</td><td>1：个人日常消费 2：房屋装修 3：旅游出行 4：在职深造 5：其他消费 6:生产经营 7：日常周转 8：购买原材料 9：购买生产设备 10：经营场所装修 11:购自用车 12：文教娱乐 13：美容医疗 14:数码通讯 15：健康养老 (申请成功或失败后返 请成功后返回)</td></tr><tr><td>dailyRate String</td><td>20</td><td>C</td><td>回) 日利率，单位是%(申 请成功后返回) 年利率，单位是%(申</td></tr></table></body></html>  

<html><body><table><tr><td>repayStatus</td><td>String</td><td>2</td><td>C</td><td>还款计划状态 1：正常还款,未还清 2：已还清 3：已逾期 4:借款请求受理中 (包含待放款状态) 5：禁还（该笔订单暂 时不允许还款，申请</td></tr><tr><td>canRepayDate</td><td>String</td><td>16</td><td>C</td><td>成功后返回) 禁还时可还日期，格 式yyyy-mm-dd（微</td></tr><tr><td>capitalType</td><td>String</td><td>2</td><td>C</td><td>业贷涉及) 资方类型 1：自营 2：联合贷</td></tr><tr><td>capitalName</td><td>String</td><td>128</td><td>C</td><td>3：助贷 资方名称，如： 重庆度小满小贷 度小满&合作机构 江苏银行</td></tr></table></body></html>  

# RepayTrialInfo （提前还款试算订单信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/</td><td>说明</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借据订单号</td></tr><tr><td>type</td><td>String</td><td>1</td><td>M</td><td>提前还款类型： 1：正常计划还款 2：剩余计划全部还清 3：自定义金额还款</td></tr><tr><td>prepaymentAmt</td><td> String</td><td>20</td><td>C</td><td>type为3时，填写自 定义还款总金额，单 位分</td></tr></table></body></html>  

RepayOrderInfo（还款记录）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 说明 0</td><td></td></tr><tr><td>repayOrderld</td><td>String</td><td>32 32</td><td>M 0</td><td>还款订单号</td></tr><tr><td>repayChannel</td><td>String</td><td></td><td></td><td>还款渠道信息（非端 外，可不传) outside:端外 wallet:钱包</td></tr><tr><td>repayDate</td><td>String</td><td>19</td><td>M</td><td>还款时间，精确到时 分秒，格式yyy- MM-DD HH:mm:sS</td></tr><tr><td>totalRepayAmt</td><td>String</td><td>20</td><td>M</td><td>本记录还款总金额， 单位分 还款类型:</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>0：自动还款 1：还款日还款 2：提前还款</td></tr><tr><td>status</td><td>String</td><td>1</td><td>M</td><td>还款状态: 0：进行中 1：成功 2：失败</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>0 在</td><td>还款银行卡绑定号 (此处如果不返回，可 RepayOrderDetail</td></tr><tr><td>bankCardNo</td><td>String</td><td>32</td><td>0</td><td>返回) 还款银行卡号(此处如</td></tr></table></body></html>  

RepayOrderDetail（还款订单详情）  


<html><body><table><tr><td></td><td></td><td></td><td>返回)</td><td>果不返回，可在 RepayOrderDetail</td></tr><tr><td>bankName</td><td>String</td><td>32</td><td></td><td>还款银行名称(此处如 果不返回，可在 RepayOrderDetail 返回) 1、确认还款接口</td></tr><tr><td>repayOrderDetai Is</td><td>List<RepayOrderDetai I></td><td>NA</td><td>C</td><td>0303、查询还款结果 接口0304、通知还 款结果接口0602， 还款详情需要一并返 回 2、查询还款记录接□ 0305可以不返回详 情，用户可以通过 0306查询详情</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号</td></tr><tr><td>repayOrderld</td><td>String</td><td>32</td><td>M</td><td>还款订单号</td></tr><tr><td>borrowDate</td><td>String</td><td>16</td><td>M</td><td>借款时间，格式yyyy-mm-dd</td></tr><tr><td>overdueDays</td><td>String</td><td>8號</td><td>0</td><td>逾期天数</td></tr><tr><td>principalAmt</td><td>String</td><td>20</td><td>M</td><td>借款本金，单位分</td></tr><tr><td>leftPrincipalAmt</td><td>String</td><td>20</td><td>C</td><td>剩余待还本金，单位分</td></tr><tr><td>repayAmt</td><td>String</td><td>20</td><td>M</td><td>还款金额，单位分</td></tr><tr><td>repayPrincipalAmt</td><td>String</td><td>20</td><td>M</td><td>本次还了多少本金，单位分</td></tr></table></body></html>  

<html><body><table><tr><td>repaylnterestAmt</td><td>String</td><td>20</td><td>M</td><td>本次还了多少利息，单位分</td></tr><tr><td>repayDiscount</td><td>String</td><td>20</td><td>C</td><td>本次优惠了多少金额，单位分</td></tr><tr><td>punishInterest</td><td>String</td><td>20</td><td>0</td><td>罚息，单位分</td></tr><tr><td>violateAmount</td><td>String</td><td>20</td><td>0</td><td>本次违约金</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>0</td><td>还款银行卡绑定号，如果 ReplayOrderlnfo 中未返回，此 处要返回</td></tr><tr><td>bankCardNo</td><td>String</td><td>32</td><td>0</td><td>还款银行卡号，如果 ReplayOrderlnfo 中未返回，此 处要返回</td></tr><tr><td>bankName</td><td>String</td><td>32</td><td>0</td><td>还款银行名称，如果 ReplayOrderlnfo 中未返回，此 处要返回</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  

# ProtocolInfo（协议条款信息）  

BankInfo（银行卡信息）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>protocolName</td><td>String</td><td>128</td><td>M</td><td>协议条款名称</td></tr><tr><td>protocolLink</td><td>String</td><td>4096</td><td>C</td><td>协议条款链接</td></tr><tr><td>forceRead</td><td>String</td><td>1</td><td>C</td><td>是否强制阅读：0强制，其他不强 制</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>bankName</td><td>String</td><td>32</td><td>M</td><td>银行名称</td></tr><tr><td>bankCode</td><td>String</td><td>32</td><td>M</td><td>银行编码</td></tr><tr><td>bankType</td><td>String</td><td>2</td><td>M</td><td>银行类型：1借记卡2信用卡</td></tr></table></body></html>  

CardInfo（绑卡信息）  


<html><body><table><tr><td>quotaPerDay</td><td>String</td><td>32</td><td>M</td><td>单日限额，单位分</td></tr><tr><td>quotaPerTime</td><td>String</td><td>32</td><td>M</td><td>单笔限额，单位分</td></tr><tr><td>icon</td><td>String</td><td>256</td><td></td><td>银行图标地址，格式 http(s)://xx</td></tr><tr><td>category</td><td>String</td><td>1</td><td>0</td><td>账户分类： (微业贷使用) 0/不传：个人账户 1：对公账户 2：内部账户</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>cardNo</td><td>String</td><td>64</td><td>C</td><td>卡号</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>M</td><td>借贷平台侧绑定卡 Id</td></tr><tr><td>banklnfo</td><td>Bankln fo</td><td>/</td><td>M</td><td>银行基本卡信息</td></tr></table></body></html>  

# CouponInfo （优惠券信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>couponNum</td><td>String</td><td>64</td><td>M</td><td>优惠券号</td></tr><tr><td>status</td><td>String</td><td>8</td><td>M</td><td>优惠券状态 0：可用 1：已过期 2：未生效 3：已核销</td></tr><tr><td>title</td><td>String</td><td>32</td><td>M</td><td>优惠券名称，如“首期免息券”</td></tr><tr><td>description</td><td>String</td><td>512</td><td>M</td><td>优惠券详细说明</td></tr><tr><td>promotionTag</td><td>String</td><td>128</td><td>M</td><td>营销标签，如“新客专享”</td></tr><tr><td>promotionContent</td><td>String</td><td>128</td><td>C</td><td>营销描述</td></tr><tr><td>type</td><td>String</td><td>2</td><td>C</td><td>优惠券类型:</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>1：会员券, 2/不传：免息折扣券 3：利率优惠券</td></tr><tr><td>subType</td><td>String</td><td>20</td><td>C</td><td>券子类型: huawei_music:华为音乐 huawei_cloud:华为云空间</td></tr><tr><td>voucherCode</td><td>String</td><td>32</td><td>C</td><td>huawei_video:华为视频 会员券需返回兑换码</td></tr><tr><td>discount</td><td> String</td><td>20</td><td>C</td><td>可减免金额，单位分</td></tr><tr><td>discountAnnualRat</td><td>String</td><td>20</td><td>0</td><td>优惠后的年利率（利率优惠券存</td></tr><tr><td>e effectiveDate</td><td>String</td><td>16</td><td>M</td><td>在) 生效日期，格式yyyy-MM-DD</td></tr><tr><td>invalidDate</td><td>String</td><td>16</td><td>M</td><td>失效时间，格式yyyy-MM-DD</td></tr><tr><td>writeOffDate</td><td>String</td><td>32</td><td>C</td><td>核销时间，格式yyyy-MM-DD HH:mm:ss</td></tr><tr><td>source</td><td>String</td><td>32</td><td>0</td><td>来源： negotiate_price:议价中心</td></tr></table></body></html>  

ChangeInfo（授信内容变化信息）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td> M/0</td><td>说明</td></tr><tr><td rowspan="3">type</td><td rowspan="3">String</td><td rowspan="3">10</td><td rowspan="3">M</td><td>变化类型:</td></tr><tr><td>1-协议变化 2-额度提高</td></tr><tr><td>3-额度降低</td></tr><tr><td>preValue</td><td>String</td><td>20</td><td>C</td><td>5-利率降低 原始值，比如额度之前为</td></tr></table></body></html>  

AddressInfo（地址信息）  


<html><body><table><tr><td></td><td></td><td></td><td></td><td>20000分</td></tr><tr><td>curValue</td><td>String</td><td>32</td><td>C</td><td>目前值，比如额度变化为 40000分</td></tr><tr><td>protocollnfos</td><td>List<Protocollnfo ></td><td>NA</td><td>C</td><td>借款信息变化需更新协议</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>province</td><td>String</td><td>128</td><td>0</td><td>省</td></tr><tr><td>city</td><td>String</td><td>128</td><td>0</td><td>市</td></tr><tr><td>area</td><td>String</td><td>128</td><td>0</td><td>区/县</td></tr><tr><td>address</td><td>String</td><td>256</td><td>M</td><td>详细地址 (不含省市区)</td></tr></table></body></html>  

NotifyInfo（营销推广信息）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>type</td><td>String</td><td>10</td><td>M</td><td>通知类型: IM：钱包首页IM消息 SMS:短信</td></tr><tr><td>templateld</td><td>String</td><td>64</td><td>0</td><td>IM 消息模板ID；按以下模 板使用</td></tr><tr><td>message</td><td>String</td><td>102 4</td><td>M</td><td>消息内容</td></tr></table></body></html>  

IM 模板  

<html><body><table><tr><td>templateld</td><td>场景描述</td><td>message</td></tr></table></body></html>  

<html><body><table><tr><td>loan.repay.overdue</td><td>逾期</td><td>{"re_amount":"60000","re_date":"2021-07-07"} (re_amount单位分，re_data为逾期起始日)</td></tr><tr><td>loan.can.credit.change</td><td>提额降息中 信</td><td>0</td></tr></table></body></html>  

# SubMallOrderInfo（商城子订单信息）  

GoodInfo（商品信息）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>subMallOrderId</td><td>String</td><td>64</td><td>M</td><td>商城子订单号</td></tr><tr><td>mallOrderId</td><td>String</td><td>64</td><td>M</td><td>商城主订单号</td></tr><tr><td>subMallOrderAmt</td><td>String</td><td>20</td><td>M</td><td>子订单金额</td></tr><tr><td>supplier</td><td>String</td><td>64</td><td>M</td><td>供应商</td></tr><tr><td> goodinfos</td><td>List<GoodInfo></td><td>128</td><td>C</td><td>商品信息</td></tr><tr><td>subRefundAmt</td><td> String</td><td>20</td><td>C</td><td>子订单退款金额</td></tr></table></body></html>  

SettleBill（结算账单）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>goodName</td><td> String</td><td>128</td><td>M</td><td>商品名称</td></tr><tr><td>count</td><td> String</td><td>8號</td><td>C</td><td>商品数量</td></tr><tr><td>goodAmt</td><td>String</td><td>20</td><td>C</td><td>商品单价</td></tr></table></body></html>  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>settleBilld</td><td> String</td><td>128</td><td>M</td><td>结算id/结算单号</td></tr><tr><td>realSaleAmount</td><td>String</td><td>20</td><td>M</td><td>生成账单金额，单位：分</td></tr><tr><td>commission</td><td>String</td><td>20</td><td>M</td><td>商户贴息金额，单位：分</td></tr><tr><td>refundAmount</td><td>String</td><td>20</td><td>M</td><td>退款金额，单位：分</td></tr><tr><td>settleAmount</td><td>String</td><td>20</td><td>M</td><td>结算金额 (结算金额=生成账单金额</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>商户贴息金额-退款金额），单位：分</td></tr><tr><td>downloadUrl</td><td>String</td><td>2048</td><td>M</td><td>对账单下载地址</td></tr><tr><td>beginDate</td><td>String</td><td>19</td><td>M</td><td>结算周期起始时间yyyy-MM-dd HH:mm:ss</td></tr><tr><td>endDate</td><td>String</td><td>19</td><td>M</td><td>结算周期截至时间yyyy-MM-dd HH:mm:ss</td></tr></table></body></html>  

# AmountChangeInfo（额度变化信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>oldTotalAmount</td><td>String</td><td>20</td><td>M</td><td>原总额度，单位是分</td></tr><tr><td>totalAmount</td><td>String</td><td>20</td><td>M</td><td>新总额度，单位是分</td></tr><tr><td>tempExpireDate</td><td>String</td><td>16</td><td>o</td><td>临价有效截止日期: 格式为yyyy-MM-dd</td></tr></table></body></html>  

# RateChangeInfo （利率变化信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>oldDailyRate</td><td>String</td><td>20</td><td>M</td><td>原日利率，单位是%</td></tr><tr><td>oldAnnualRate</td><td>String</td><td>20</td><td>M</td><td>原年利率，单位是%</td></tr><tr><td>dailyRate</td><td> String</td><td>20</td><td>M</td><td>新日利率，单位是%</td></tr><tr><td>annualRate</td><td>String</td><td>20</td><td>M</td><td>新年利率，单位是%</td></tr><tr><td>tempExpireDate</td><td>String</td><td>16</td><td>o</td><td>临价有效截止日期: 格式为yyyy-MM-dd</td></tr></table></body></html>  

# ExternalOptInfo（外部操作信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>url</td><td>String</td><td>4096</td><td>M</td><td>外部h5链接(动态链接，包含用户 标识信息在url中)</td></tr><tr><td>validTime</td><td>String</td><td>2</td><td>M</td><td>有效期，单位分钟</td></tr></table></body></html>  

# ChangeOfferSignInfo（Offer 变更签约信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>amount</td><td>String</td><td>20</td><td>M</td><td>额度，单位分</td></tr><tr><td>annualRate</td><td>String</td><td>20</td><td>M</td><td>年利率，单位%</td></tr><tr><td>scheduleRepay</td><td>String</td><td>1</td><td>M</td><td>还款方式枚举值 2:仅支持按期还 1:支持按期还、支持灵活还 0：仅支持灵活还</td></tr><tr><td>fqNums</td><td>String</td><td>36</td><td>M</td><td>可选分期数，多个英文逗号 分割</td></tr></table></body></html>  

# ChangeOfferOrderInfo(Offer 变更订单信息)  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>principalAmt</td><td>String</td><td>20</td><td>M</td><td>借款本金，单位分</td></tr><tr><td>fqNum</td><td>String</td><td>8號</td><td>M</td><td>分期数</td></tr><tr><td>annualRate</td><td>String</td><td>20</td><td>M</td><td>借款订单年利率，单位%</td></tr><tr><td>interestAmt</td><td>String</td><td>20</td><td>M</td><td>借款利息，单位分</td></tr></table></body></html>  

# ChangeOffer（Offer 变更信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>scene</td><td>String</td><td>1</td><td>M</td><td>Offer 变化场景 1：仅协议变化 2:Offer变化，原订 单取消 3:Offer变化，原借 据未变化</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>4：Offer变化，原借 据发生变化</td></tr><tr><td>preSignInfo</td><td>ChangeOfferSignInf 0</td><td>NA</td><td>C</td><td>原签约信息</td></tr><tr><td>curSignInfo</td><td>ChangeOfferSignInf o</td><td>NA</td><td>C</td><td>当前签约信息</td></tr><tr><td>preOrderInfo</td><td>ChangeOfferOrderl nfo</td><td>NA</td><td>C</td><td>原借据订单信息</td></tr><tr><td>curOrderInfo</td><td>ChangeOfferOrderl nfo</td><td>NA</td><td>C</td><td>当前借据订单信息</td></tr><tr><td>curRepayPlanInfos</td><td>List<RepayPlanlnfo V</td><td>NA</td><td>C</td><td>当前还款计划</td></tr><tr><td>protocolinfos</td><td>List<Protocollnfo></td><td>NA</td><td>C</td><td>借款信息变化需更新 协议</td></tr></table></body></html>  

# IndustyInfo （投向行业信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>code</td><td>String</td><td>16</td><td>M</td><td>类别</td></tr><tr><td>name</td><td>String</td><td>64</td><td>M</td><td>类别名称</td></tr><tr><td>level</td><td>Integer</td><td>1</td><td>M</td><td>所属层级</td></tr><tr><td>parentld</td><td>String</td><td>32</td><td>M</td><td>父id</td></tr><tr><td>industryCode</td><td> String</td><td>20</td><td>0</td><td>代码码值</td></tr><tr><td>sublnfos</td><td>List< IndustyInfo V</td><td>NA</td><td>0</td><td>投向行业子节点列表</td></tr></table></body></html>  

# DynamicParam（动态参数信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td> key</td><td>String</td><td>20</td><td>M</td><td>字段标识，例：activityCode</td></tr></table></body></html>  

<html><body><table><tr><td>value</td><td>String</td><td>64</td><td>M</td><td>字段值，例： 202410101633010211</td></tr></table></body></html>  

# EnterpriseInfo （企业信息）  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>enterpriseld</td><td>String</td><td>64</td><td>M</td><td>企业id</td></tr><tr><td>enterpriseName</td><td>String</td><td>128</td><td>0</td><td>企业名称</td></tr><tr><td>status</td><td>String</td><td>16</td><td>M</td><td>UNSIIGN一未授信 FAILED一授信失败</td></tr><tr><td>nextSignDate</td><td>String</td><td>16</td><td>C</td><td>如果审批拒绝，允许再次申请的最早 日期</td></tr><tr><td>entrepreneurTag</td><td>String</td><td>1</td><td>M</td><td>格式为yyyy-MM-dd 1企业用户2个体工商户</td></tr></table></body></html>  

# 4 准入与授信管理接口 (借贷平台提供)  

# 4.1 0101 – 用户准入接口(account.check)  

# 4.1.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>userInfo</td><td>Userln fo</td><td>NA</td><td>M</td><td>实名三要素 (姓名、身份证号、手机号)</td></tr><tr><td>creditScore</td><td> String</td><td>512</td><td>0</td><td>模型值（信用分），json 格式字符串</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>c</td><td>活动Id</td></tr><tr><td>hwEmployeeTag</td><td> String</td><td>2</td><td>o</td><td>华为员工标签: 1-华为自有员工</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>2-其他非华为员工 3-华为合作方</td></tr><tr><td>enterpriseEmail</td><td>String</td><td>128</td><td>0</td><td>企业邮箱</td></tr><tr><td>loanChannel</td><td>String</td><td>32</td><td>0</td><td>渠道信息 normal:借贷渠道 hirePurchase:分期购渠道</td></tr><tr><td>reserved</td><td>String</td><td>1024</td><td>0</td><td>html:端外h5 保留域信息</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan：小微企业贷 creditLoan:信用贷</td></tr><tr><td>enterpriseAddr</td><td>String</td><td>256</td><td>0</td><td>不传：默认为信用贷 企业所在省市区</td></tr><tr><td>enterpriseAreaCode</td><td>String</td><td>32</td><td>0</td><td>企业所在区/县编码</td></tr><tr><td>DATA样例</td><td colspan="5">{ "userlnfo": { "certifld": "320282197*********", "name":"张三”， "phoneNo":“***********", " imageNo": "1001" }</td></tr></table></body></html>  

# 4.1.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>25 6</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>0</td><td>平台分配的账户号</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA</td><td>0</td><td>收集用户信息前需呈现的相关隐私协议</td></tr><tr><td>Data样例:</td><td colspan="4">{ "resultcode": "0", "resultdesc":"success", "accountNo": "6225789456187951288"</td></tr><tr><td rowspan="3">} 返回码定义：</td><td colspan="4"></td></tr><tr><td>resultCode</td><td colspan="3">resultDesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr></table></body></html>  

<html><body><table><tr><td rowspan="5"></td><td>E999999</td><td>服务器内部错误</td></tr><tr><td>E010101</td><td>实名信息校验失败</td></tr><tr><td>E010102</td><td>黑名单用户</td></tr><tr><td colspan="2"></td></tr></table></body></html>  

# 4.2 0102 – 授信检查接口(credit.check)  

# 4.2.1 请求 data 数据结构  

<html><body><table><tr><td></td><td></td><td></td><td>M/O</td><td></td></tr><tr><td>参数列表</td><td>类型</td><td>长度</td><td></td><td>字段描述</td></tr><tr><td>userInfo accoutNo</td><td>UserInfo String</td><td>NA 128</td><td>M C</td><td>授信检查携带三要素 (姓名、身份证号、手机号) 如果在准入环节已返回accountNo，授信检查携带</td></tr><tr><td></td><td></td><td></td><td></td><td>accountNo</td></tr><tr><td>activityCode operation</td><td>String String</td><td>64 32</td><td>C M</td><td>活动Id 类型:</td></tr><tr><td></td><td></td><td></td><td></td><td>credit.create:初次授信 credit.again:重新授信核额 credit.more:申请更多(微业贷申请其他企 业)</td></tr><tr><td>riskInfo hwEmployeeTa</td><td>RiskInfo String</td><td>NA 2</td><td>C 0</td><td>风控信息 华为员工标签：</td></tr><tr><td>g</td><td></td><td></td><td></td><td>1-华为自有员工 2-其他非华为员工 3-华为合作方</td></tr><tr><td>loanChannel</td><td>String</td><td>32</td><td>0</td><td>渠道信息 normal:借贷渠道 hirePurchase:分期购渠道</td></tr><tr><td>loanProductTyp e</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan：小微企业贷 creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>callbackUrl</td><td>String</td><td>409 6</td><td>C</td><td>回调地址</td></tr><tr><td>creditScore</td><td>String {</td><td>512</td><td>0</td><td>模型值（信用分），json格式字符串</td></tr><tr><td>DATA样例</td><td colspan="4">"accountNo": "6225789456187951288" }</td></tr></table></body></html>  

# 4.2.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型 长 度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String 8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc operations</td><td>String 256 String 20</td><td>0 C</td><td>返回结果描述，可以是成功信息或失败原因 如需补充材料需返回对应类型，格式为1|2|</td></tr><tr><td></td><td></td><td></td><td>3 1：身份证影像 2：活体 3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信 7：职业和收入 8：工作单位 9：地址 10：学历 11:仅职业 12：银行短信验证码 13：华为短信验证码 14：营业执照影像 (预留) 15：税务信息 (预留) 16：电话核实 (用信阶段) 17：须跳转外部补录</td></tr><tr><td>protocollnfos</td><td>List<ProtocolInfo> NA</td><td>C</td><td>18：行业投向 如果operation为5或6，需返回协议信</td></tr><tr><td>externalOptIn</td><td>ExternalOptInfo NA</td><td>息 C</td><td>跳转外部信息(operations返回17时，</td></tr><tr><td>fo creditOrderId</td><td>String 36 String 1</td><td>C 0</td><td>返回) 签约单号 (小微贷场景返回) 是否需授信 (微业贷需要)</td></tr><tr><td>enableCredit</td><td></td><td></td><td>0/不传：不可授信（存在核额成功或均在封 闭期内) 1：可授信（除了上述0之外的场景，需同时 返回补录项)</td></tr><tr><td>accountNo</td><td> String</td><td>1280</td><td>平台分配账户号，可授信时需分配（微众使 用，需授信新企业时才分配)</td></tr><tr><td>DATA样例</td><td>{ "resultCode":"0",</td><td></td><td></td></tr></table></body></html>  

<html><body><table><tr><td></td><td colspan="2">"resultDesc":"success"</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="2"></td></tr><tr><td>resultCode</td><td>resuItDesc</td></tr><tr><td>0</td><td>success(成功)</td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td colspan="2"></td></tr></table></body></html>  

# 4.3 0103 – 授信确认接口(credit.confirm)  

# 4.3.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>operation</td><td>String</td><td>32</td><td>M</td><td>类型: credit.create:初次授信 credit.again:回捞补录二次授信确认</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>M</td><td>用户信息（姓名、身份证号、手机号、地 址、单位、职业)</td></tr><tr><td>accoutNo</td><td>String</td><td>12 8</td><td>C</td><td>如果在准入环节已返回accountNo，授信检 查携带accountNo</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>C</td><td>活动 ld (credit.again场景不传)</td></tr><tr><td>contactinfos</td><td>List<Contactlnfo</td><td>NA</td><td>0</td><td>联系人信息</td></tr><tr><td>riskInfo</td><td>RiskInfo</td><td>NA</td><td>0</td><td>风控信息</td></tr><tr><td>facelmagelnfo</td><td>Facelmagelnfo</td><td>NA</td><td>0</td><td>活体信息 (credit.again场景不传)</td></tr><tr><td>facelmageld</td><td>String</td><td>32</td><td>0</td><td>活体识别编号ID（有编号，必 传，credit.again场景不传)</td></tr><tr><td>creditScore</td><td>String</td><td>51 2</td><td>0</td><td>模型值 (信用分)</td></tr><tr><td>hwEmployeeTa g</td><td>String</td><td>2</td><td>0</td><td>华为员工标签: 1-华为自有员工 2-其他非华为员工 3-华为合作方</td></tr><tr><td>loanChannel</td><td>String</td><td>32</td><td>0</td><td>渠道信息 (credit.again场景不传) normal:借贷渠道 hirePurchase：分期购渠道</td></tr><tr><td>loanProductTyp</td><td>String</td><td>32</td><td>0</td><td>html:端外h5 enterpriseLoan：小微企业贷</td></tr></table></body></html>  

<html><body><table><tr><td>e</td><td></td><td></td><td></td><td>creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>lastMsgId</td><td>String</td><td>20</td><td>C</td><td>上次授信确认的msgId (credit.again场 景必传)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "userlnfo": { "ertified": "320282197*********", "name":"张三”，</td></tr></table></body></html>  

# 4.3.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>C</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>授信状态： SUCCESS：授信成功 PROCESS：授信进行中 FAILED：授信失败</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台账户号</td></tr><tr><td>creditSignInfo</td><td>CreditSignInfo</td><td>NA</td><td>C</td><td>授信签约信息 (成功和失败时返回，失败返 回signStatus、nextSignDate)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success" }</td></tr><tr><td colspan="2">返回码定义：</td><td colspan="4"></td></tr><tr><td rowspan="8"></td><td>resultCode resultDesc</td><td colspan="3"></td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td>E010301</td><td colspan="3">身份信息校验失败</td></tr><tr><td></td><td colspan="3"></td></tr><tr><td>E010302</td><td colspan="3">不满足18岁</td></tr><tr><td>E010303</td><td colspan="3">无效的身份证信息</td></tr><tr><td colspan="4"></td></tr></table></body></html>  

# 4.4 0104 – 授信结果查询接口(credit.status.query)  

# 4.4.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>Strin g</td><td>12 8</td><td>M</td><td>平台账户号</td></tr><tr><td>msgld</td><td>Strin g</td><td>20</td><td>C</td><td>0103接口请求msgld (非小微贷场景必传)</td></tr><tr><td>creditOrderId</td><td>Strin g</td><td>36</td><td>C</td><td>签约单号 (授信确认接口未返回时，不传)</td></tr><tr><td>msgType</td><td>Strin g {</td><td>10</td><td>M</td><td>交易类型: credit.confirm</td></tr><tr><td>DATA样例</td><td colspan="4">"msgld":“***********", "msgType":"credit.confirm"</td></tr></table></body></html>  

# 4.4.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>25 6</td><td>0</td><td>返回结果描述，可以是成功信息或失败 原因</td></tr><tr><td>userTypeCollectFlag</td><td>String</td><td>8</td><td>0</td><td>收集用户类型信息标志，1：需要收 集，0：无需收集</td></tr><tr><td>status</td><td>String</td><td>16</td><td>M</td><td>操作状态： SUCCESS:操作成功 PROCESS：操作进行中 FAILED：操作失败 ABORTED：操作中断 NEED_SUPPLEMENT:操作进行中, 需补录或激活</td></tr><tr><td>creditSignInfo</td><td>CreditSignInfo</td><td>NA</td><td>C</td><td>授信单 成功时：返回 失败时：返回 signStatus、nextSignDate</td></tr></table></body></html>  

<html><body><table><tr><td>operations String</td><td></td><td></td><td>进行中时：不返回</td></tr><tr><td></td><td></td><td>20 C 13：华为短信验证码</td><td>状态为NEED_SUPPLEMENT时需返 回 如需补充材料需返回对应类型，格式为 1|2|3 1：身份证影像 2：活体 3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信 7：职业和收入 8:工作单位 9：地址 10：学历 11:仅职业 12：银行短信验证码</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA C</td><td>16：电话核实 (用信阶段) 17：须跳转外部补录 18：行业投向 如果operation为5或6，需返回协</td></tr><tr><td>DATA样例</td><td colspan="3">议信息 { "resultCode": "0", "resultDesc":"success"</td></tr><tr><td rowspan="7">返回码定义：</td><td colspan="3">}</td></tr><tr><td>resultcod</td><td colspan="2">resultdesc</td></tr><tr><td>e</td><td colspan="2"></td></tr><tr><td>0 E010401</td><td colspan="2">success(成功) 身份信息校验失败</td></tr><tr><td>E010402</td><td colspan="2">不满足18岁</td></tr><tr><td>E010403</td><td colspan="2">无效的身份证信息</td></tr><tr><td></td><td colspan="2"></td></tr></table></body></html>  

# 4.5 0105 – 授信关系查询接口(credit.account.query)  

# 4.5.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>userlnfo</td><td>UserInf 0</td><td>NA</td><td>C</td><td>实名信息（姓名、身份证号、手机号）（广发/民生必 传)</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>M</td><td>有accountNo传入</td></tr><tr><td>loanProductTyp e</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan：小微企业贷 creditLoan:信用贷</td></tr><tr><td>creditOrderId</td><td>String {</td><td>36</td><td>C</td><td>不传：默认为信用贷 签约单编号 (不传时，返回最新的签约关系)</td></tr><tr><td>DATA样例</td><td colspan="4">"acc0untNo":"6225789456187951288" }</td></tr></table></body></html>  

# 4.5.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>creditSignInfo</td><td>CreditSignInfo</td><td>NA</td><td>C</td><td>签约信息查询</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc": "success" }</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td>E010501</td><td colspan="3">账号无效</td></tr><tr><td></td><td colspan="5"></td></tr></table></body></html>  

# 4.6 0106 – 用户开具证明接口(account.settlement.query)  

# 4.6.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>Strin g</td><td>128</td><td>M</td><td>账户号</td></tr><tr><td>coinAccountN 0</td><td>Strin g</td><td>128</td><td>C</td><td>零钱账户协议编号 (对接银行场景，存在，必传)</td></tr><tr><td>creditOrderId</td><td>Strin g</td><td>36</td><td>M</td><td>签约单号</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo":"6225789456187951288" }</td></tr></table></body></html>  

# 4.6.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>settlementContent</td><td>String</td><td>500K</td><td></td><td>结清证明图片</td></tr><tr><td>settlementUrl</td><td>String</td><td>2048</td><td>C</td><td>图片链接(与 settlementContent二选一)</td></tr><tr><td>remainTimes</td><td>String</td><td>10</td><td>C</td><td>邮件可发送次数：5|3 每月可发送5次，剩余3次 为空不限制</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0", "resultDesc":"success" }</td></tr><tr><td rowspan="4">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td colspan="4"></td></tr></table></body></html>  

# 4.7 0107 – 发送开具证明接口(account.settlement.send)  

# 4.7.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>M</td><td>账户号</td></tr><tr><td>coinAccountN 0</td><td>String</td><td>12 8</td><td>C</td><td>零钱账户协议编号（银行场景使用， 存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>M</td><td>申请书编号</td></tr><tr><td>email</td><td>String</td><td>12 8</td><td>M</td><td>邮箱地址</td></tr><tr><td>DATA样例</td><td>{ "accountNo": "622578945618795128 8" }</td><td></td><td></td><td></td></tr><tr><td></td><td colspan="4"></td></tr></table></body></html>  

# 4.7.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>remainTimes</td><td>String</td><td>10</td><td>C</td><td>邮件可发送次数：5|3 每月可发送5次，剩余3次 为空不限制</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc": "success" }</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E070102</td><td colspan="3">开具证明次数超上限</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td></td><td colspan="5"></td></tr></table></body></html>  

# 4.8 0108 – 更换手机号接口(account.phoneno.change)  

# 4.8.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountN 0</td><td>Strin g</td><td>128</td><td>M 账户号</td><td></td></tr><tr><td>phoneNo</td><td>Strin g</td><td>11</td><td>M</td><td>新手机号码</td></tr><tr><td>DATA样 例</td><td colspan="4">{ "accountNo": “6225789456187951288, " phoneNo": "***********"</td></tr></table></body></html>  

# 4.8.2 响应消息  

<html><body><table><tr><td>参数列表</td><td colspan="2">类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td colspan="2">String 8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td colspan="2">String</td><td>256 0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0", "resultDesc":"success"</td></tr><tr><td rowspan="6">返回码定义：</td><td colspan="5">广</td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td>E010801</td><td colspan="3">无效手机号</td></tr><tr><td colspan="3"></td></tr></table></body></html>  

# 4.9 0109 – 活体人脸校验接口(face.check)  

# 4.9.1 请求 data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>C</td><td>账户号 (若没有，不传)</td></tr><tr><td>userlnfo</td><td>UserInfo</td><td>NA</td><td>M</td><td>客户信息 (必传校验姓名、身份证号、手机号)</td></tr><tr><td>facelmagelnfo</td><td>Facelmagelnf 0</td><td>N/A</td><td>C</td><td>人脸图片信息 (非微业贷传图片)</td></tr><tr><td>faceVideolnfo</td><td>FaceVideolnf 0</td><td>NA</td><td>C</td><td>人脸视频信息 (微业贷传视频)</td></tr><tr><td>isAgreeProtoco 一</td><td>String</td><td>1</td><td>C</td><td>是否同意协议1-同意0-不同意 (微业贷需要)</td></tr><tr><td>operation</td><td>String</td><td>32</td><td>0</td><td>操作类型（微业贷需要）： credit.create:初次授信 credit.again：重新授信核额 credit.more:申请更多</td></tr><tr><td>DATA样例</td><td colspan="5">{ "accountNo": "6225789456187951288, "facelmagelnfo": {""} }</td></tr></table></body></html>  

# 4.9.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>facelmageld</td><td>String</td><td>32</td><td>C</td><td>人脸识别编号ID</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success" }</td></tr><tr><td rowspan="4">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td></td><td>E010901</td><td colspan="3">图片不清晰，验证失败</td></tr></table></body></html>  

<html><body><table><tr><td></td><td>E010902</td><td>人脸和身份信息不匹配</td></tr><tr><td></td><td colspan="3"></td></tr></table></body></html>  

# 4.10 0110 – 身份证上传接口(eid.upload)  

# 4.10.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>C</td><td>借贷平台用户账号</td></tr><tr><td>userlnfo</td><td>UserInfo</td><td>NA</td><td>M</td><td>客户信息 (校验身份证号、姓名、手机号)</td></tr><tr><td>imageInfo</td><td>ImageInfo</td><td>NA</td><td>M</td><td>身份证影像信息</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号 (0102或0103接口返回，未返回不传)</td></tr><tr><td>validDate</td><td>String {</td><td>32</td><td>C</td><td>身份证有效期:，如：********-********</td></tr><tr><td>DATA样例</td><td colspan="4">"accountNo":"6225789456187951288, "imagelnfo": {""}</td></tr></table></body></html>  

# 4.10.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success" }</td></tr><tr><td rowspan="7">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcode</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td>E011001</td><td colspan="3">图片不清晰，验证失败</td></tr><tr><td>E011002</td><td colspan="3">身份证件和实名信息不匹配</td></tr><tr><td></td><td colspan="5"></td></tr></table></body></html>  

# 4.11 0111 – 联系人添加接口(contact.add)  

# 4.11.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>C</td><td>借贷平台用户账号</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>C</td><td>客户信息 (授信流程还没有账户号，需携带用户 三要素)</td></tr><tr><td>contactInfos</td><td>List<ContactInfo></td><td>NA</td><td>M</td><td>联系人信息</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（0102或0103接口返回，未返回不 传)</td></tr><tr><td>DATA样例 }</td><td colspan="5">{ "accountNo":"6225789456187951288", "contactlnfos":[ { "name":"zhangsan", "phone":"***********", "type":"01" }</td></tr></table></body></html>  

# 4.11.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0", "resultDesc": "success" }</td></tr><tr><td rowspan="4">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcode</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td></td><td colspan="5"></td></tr></table></body></html>  

# 4.12 0112 – 个人资料补充接口(userinfo.supplement)  

# 4.12.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>C</td><td>借贷平台用户账号</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>C</td><td>用户资料补充</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号 (0102或0103接口返回，未返回不传)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo":"6225789456187951288", }</td></tr></table></body></html>  

# 4.13 0113 – 销户检查(account.cancel.check)  

# 4.13.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountNo</td><td>Strin g</td><td>128</td><td>M</td><td>借贷平台用户账号</td></tr><tr><td>coinAccountN 0</td><td>Strin g</td><td>128</td><td>C</td><td>零钱账户协议编号 (银行场景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>Strin g</td><td>36</td><td>C</td><td>签约单号(若存在，必传)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo":"6225789456187951288" }</td></tr></table></body></html>  

# 4.13.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>isAllowedCancel</td><td>String</td><td>5</td><td>M</td><td>是否允许销户false/true</td></tr><tr><td>reason</td><td>String</td><td>1024</td><td>C</td><td>不允许销户的原因 在不允许销户的时候存在</td></tr><tr><td>protoeollnfos</td><td>List<Protocol</td><td>NA</td><td></td><td>如果允许销户，并需要签署协议，则</td></tr></table></body></html>  

<html><body><table><tr><td></td><td>Infe></td><td></td><td>返回协议信息</td><td></td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success" }</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="4"></td></tr><tr><td>resultcode</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td></td><td colspan="3"></td></tr></table></body></html>  

# 4.14 0114 – 生命周期操作(account.lifecycle)  

# 4.14.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>Strin g</td><td>128</td><td>M</td><td>借贷平台用户账号</td></tr><tr><td>coinAccountN 0</td><td>Strin g</td><td>128</td><td>C</td><td>零钱账户协议编号 (银行场景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>Strin g</td><td>36</td><td>C</td><td>签约单号(若存在，必传)</td></tr><tr><td>operaction</td><td>Strin g</td><td>20</td><td>M</td><td>账户操作 CANCEL：账户销户</td></tr><tr><td>facelmageld</td><td>Strin g</td><td>32</td><td>C</td><td>人脸识别编号ID</td></tr><tr><td>DATA样例</td><td>{ "accountNo":"6225789456187951288" }</td><td></td><td></td><td></td></tr></table></body></html>  

# 4.14.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0",</td></tr></table></body></html>  

<html><body><table><tr><td></td><td colspan="2">}</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="2"></td></tr><tr><td>resultcode</td><td>resultdesc</td></tr><tr><td>0</td><td>success(成功)</td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td colspan="2"></td></tr></table></body></html>  

# 4.15 0115 –身份证查询操作(eid.upload.query)  

# 4.15.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountN</td><td>String</td><td>128</td><td>C</td><td>借贷平台用户账号</td></tr><tr><td>userlnfo</td><td>UserInf 0</td><td>NA</td><td>C</td><td>客户信息 (授信流程还没有账户号，需携带用户三要素)</td></tr><tr><td>DATA样 例</td><td colspan="4">{ "accountNo":"6225789456187951288"</td></tr></table></body></html>  

# 4.15.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>isNeedUpload</td><td>String</td><td>5</td><td>M</td><td>是否需要上传false/true</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0", "resultDesc":"success" }</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcode</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td></td><td colspan="3"></td></tr></table></body></html>  

# 4.16 0116 –短信发送（otp.apply）  

# 4.16.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>otpType</td><td>String</td><td>32</td><td>M</td><td>短信类型 用信：borrowApplySP 还款卡重签约： repayResignSP</td></tr><tr><td>phoneNo</td><td>String</td><td>32</td><td>M</td><td>手机号</td></tr><tr><td>repayTrialld</td><td>String</td><td>64</td><td>C</td><td>试算流水号 (还款卡重签 约流程需传)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo": "6225789456187951288", " otpType ": "verificationCodeOfBank", "phoneNo": "***********" }</td></tr></table></body></html>  

# 4.16.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr></table></body></html>  

<html><body><table><tr><td>codeld</td><td>String</td><td>36</td><td>M</td><td>校验短信的Id</td></tr><tr><td>validTime</td><td>String</td><td>2</td><td>M</td><td>有效期，单位分 钟</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0", "resultDesc":"success", "authld":“123456" 广</td></tr><tr><td rowspan="4">返回码定义：</td><td colspan="5">resultdesc</td></tr><tr><td>resultcod e</td><td colspan="4"></td></tr><tr><td>0</td><td colspan="4">success(成功)</td></tr><tr><td>E999999</td><td colspan="4">服务器内部错误</td></tr></table></body></html>  

# 4.17 0117 – 全量企业信息查询接口(enterprise.info.query)  

# 4.17.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>M</td><td>实名信息 (姓名、身份证号、手机号)</td></tr></table></body></html>  

# 4.17.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>mainEnterpriseId</td><td>String</td><td>64</td><td>0</td><td>主推企业id</td></tr><tr><td>enterpriseInfos</td><td>List<CreditSi gnInfo></td><td>NA</td><td>C</td><td>企业信息列表</td></tr><tr><td>DATA样例</td><td colspan="6">{ "resultCode": "0", "resultDesc":"success"</td></tr></table></body></html>  

<html><body><table><tr><td></td><td colspan="2">}</td></tr><tr><td rowspan="7">返回码定义：</td><td colspan="2"></td></tr><tr><td>resultcod e</td><td>resultdesc</td></tr><tr><td>0</td><td>success(成功)</td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td>E010501</td><td>账号无效</td></tr><tr><td colspan="2"></td></tr></table></body></html>  

# 5 用信接口(借贷平台提供)  

# 5.1 0201 - 默认试算（borrow.trial.default）  

# 5.1.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：分</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>M</td><td>签约单号</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>c</td><td>活动Id</td></tr><tr><td>scheduleRepay</td><td>String</td><td>1</td><td>0</td><td>1:按期还 0、为空：灵活还</td></tr></table></body></html>  

# 5.1.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr></table></body></html>  

<html><body><table><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成</td></tr><tr><td>dailyRate</td><td>String</td><td>20</td><td>0</td><td>功信息或失败原因 日利率,单位是%</td></tr><tr><td></td><td>String</td><td>20</td><td>0</td><td>综合日利率,单位是%</td></tr><tr><td>comDailyRate annualRate</td><td>String</td><td>20</td><td>0</td><td>年利率,单位是%</td></tr><tr><td>oldAnnualRate</td><td>String</td><td>20</td><td>0</td><td>原始年利率，单位是%（存</td></tr><tr><td></td><td></td><td></td><td></td><td>在临价或利率优惠时需要)</td></tr><tr><td>comAnnualRate</td><td>String</td><td>20</td><td>0</td><td>综合年利率,单位是%</td></tr><tr><td>scheduleDailyRate</td><td>String</td><td>20</td><td>0</td><td>按期还日利率，单位是%</td></tr><tr><td>scheduleAnnualRate</td><td>String</td><td>20</td><td>0</td><td>按期还年利率，单位是%</td></tr><tr><td>oldScheduleAnnualR ate</td><td> String</td><td>20</td><td>0</td><td>按期还原始年利率，单位是% （存在临价或利率优惠时需 要)</td></tr><tr><td>penaltyRate</td><td>String</td><td>20</td><td>0</td><td>提前结清违约金利率，单位是 %</td></tr><tr><td>couponNum</td><td>String</td><td>64</td><td>0</td><td>默认选择的优惠券号 默认还款类型</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>1：每月等额（还款总 金额一致，不保证每一期 的本金及利息是一致的) 2：先息后本 3：等本等息（每一期 的本金和利息都相等) 4:等额本金 （每个月</td></tr><tr><td></td><td></td><td></td><td></td><td>按固定的金额计算利息， 既本金每一期都相等) 5:每月等额(锁期)</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>默认分期数</td></tr><tr><td>repayDay</td><td>String</td><td>8</td><td>M</td><td>默认还款日，每月xx日还</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>款</td></tr><tr><td>totalInterestAmt</td><td>String</td><td>20</td><td>C</td><td>默认条件下总利息，单位</td></tr><tr><td> principalAmt</td><td>String</td><td>20</td><td>M</td><td>分 (非微众必传) 本金，单位分</td></tr><tr><td>firstBillAmt</td><td>String</td><td>20</td><td>C</td><td>默认条件下首期应还，单 位分 (非微众必传)</td></tr><tr><td>bindCardId</td><td>String</td><td>32</td><td>0</td><td>默认收款卡绑定Id（二类 户场景不需要)</td></tr><tr><td>transParty</td><td>String</td><td>128</td><td>0</td><td>收款卡信息，银行名称十 （卡号后四位），如： 招商银行 (2121) (二类户场景不需要)</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td></td><td>产品类型：entrepriseLoan- 小微企业贷 creditLoan-信 用贷 默认资金用途 1:个人日常消费</td></tr><tr><td>usage</td><td>String</td><td>8</td><td>0</td><td>2：房屋装修 3：旅游出行 4：在职深造 5：其他消费 6:生产经营 7：日常周转 8：购买原材料 9：购买生产设备 10：经营场所装修 11:购自用车 12：文教娱乐 13：美容医疗</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>14：数码通讯 15：健康养老 51：日常经营周转</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan:经营贷 creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>100</td><td>0</td><td>借钱协议列表(单独流程展 示时，可不返回)</td></tr><tr><td>couponInfos</td><td>List<CouponInfo></td><td>NA</td><td>0</td><td>可用券列表</td></tr><tr><td>repayTypeInfos</td><td>List<RepayTypeInfo></td><td>NA NA</td><td>M M</td><td>还款方式列表</td></tr><tr><td>repayFqInfos</td><td>List<RepayFqInfo> List<RepayPlanInfo></td><td>NA</td><td>M</td><td>分期方式列表 本借据还款计划列表</td></tr><tr><td>repayPlanInfos</td><td></td><td></td><td></td><td>推荐的投向行业（微业</td></tr><tr><td>defaultIndustyInfos</td><td>List<IndustyInfo></td><td>NA</td><td>0</td><td>贷） 扩展信息，sp在后续创建</td></tr><tr><td>trialExtend</td><td>String</td><td>1024</td><td>0</td><td>订单需透传时使用（微业 贷使用) 最低借款金额，单位分</td></tr><tr><td>minAmount</td><td>String</td><td>20</td><td>0</td><td>(微业贷使用) 最高借款金额，单位分</td></tr><tr><td>maxAmount</td><td>String</td><td>20</td><td>0</td><td>(微业贷使用)</td></tr><tr><td>amountStep</td><td>String</td><td>20</td><td>0</td><td>借款金额步长，单位分， 最低100 (微业贷使用)</td></tr></table></body></html>  

# 5.2 0202 - 借贷试算（borrow.trial）  

# 5.2.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>M</td><td>签约单号</td></tr><tr><td> activityCode</td><td>String</td><td>64</td><td>c</td><td>活动Id</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：分</td></tr><tr><td>couponNum</td><td>String</td><td>64</td><td>0</td><td>选择的优惠券号</td></tr><tr><td> scheduleRepay</td><td>String</td><td>1</td><td></td><td>1:按期还 0、为空：灵活还 还款类型</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>1：每月等额（还款总 金额一致，不保证每一期 的本金及利息是一致的) 2：先息后本 3：等本等息（每一期 的本金和利息都相等) 4：等额本金（每个月 按固定的金额计算利息， 既本金每一期都相等)</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>5:每月等额(锁期) 分期数</td></tr><tr><td>bindCardId</td><td>String</td><td>32</td><td>0</td><td>收款卡（二类户场景不 传）</td></tr><tr><td>usage</td><td>String</td><td>8</td><td></td><td>资金用途 1:个人日常消费</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>2：房屋装修 3：旅游出行</td></tr><tr><td></td><td></td><td></td><td></td><td>4：在职深造</td></tr><tr><td></td><td></td><td></td><td>5：其他消费</td></tr><tr><td></td><td></td><td>6:生产经营</td><td></td></tr><tr><td></td><td></td><td>7：日常周转</td><td></td></tr><tr><td></td><td></td><td>8：购买原材料 9：购买生产设备</td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>10：经营场所装修</td></tr><tr><td></td><td></td><td></td><td>11:购自用车</td></tr><tr><td></td><td></td><td></td><td>12：文教娱乐</td></tr><tr><td></td><td></td><td></td><td>13：美容医疗</td></tr><tr><td></td><td></td><td></td><td>14：数码通讯</td></tr><tr><td></td><td></td><td></td><td>15：健康养老</td></tr><tr><td></td><td></td><td></td><td>51：日常经营周转</td></tr></table></body></html>  

# 5.2.2 响应消息  

与 borrow.trial.default 响应相同格式  

# 5.3 0203 – 创建订单（borrow.apply）  

# 5.3.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr></table></body></html>  

<html><body><table><tr><td>COmAccouTtNO</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号（银行 场景使用，必传)</td></tr><tr><td>creditOrderId</td><td> String</td><td>36</td><td>M</td><td>签约单号</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>C</td><td>活动Id</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：分</td></tr><tr><td>couponNum</td><td> String</td><td>64</td><td>0</td><td>优惠券号</td></tr><tr><td rowspan="9">repayType</td><td rowspan="9">String</td><td rowspan="9">1</td><td rowspan="9"></td><td>还款类型</td></tr><tr><td>1：每月等额（还款 总金额一致，不保证每</td></tr><tr><td></td></tr><tr><td>一期的本金及利息是一 致的)</td></tr><tr><td>2：先息后本</td></tr><tr><td>3：等本等息（每一</td></tr><tr><td>M 期的本金和利息都相</td></tr><tr><td>等） 4：等额本金（每个</td></tr><tr><td>月按固定的金额计算利</td></tr><tr><td>fqNum</td><td> String</td><td></td><td>等）</td><td>息，既本金每一期都相</td></tr><tr><td></td><td></td><td>8</td><td></td><td>5:每月等额(锁期)</td></tr><tr><td>bindCardId</td><td>String</td><td>32</td><td>M</td><td>分期数</td></tr><tr><td>cardNo</td><td>String</td><td>32</td><td>M M</td><td>收款卡 收款卡号</td></tr><tr><td>repayCardNo</td><td> String</td><td>32</td><td>C</td><td>还款卡号（微众个体贷 场景需传)</td></tr><tr><td>usage</td><td>String</td><td>8</td><td>M</td><td>信用贷资金用途 1：个人日常消费 2：房屋装修</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>3：旅游出行 4：在职深造 5：其他消费 6:生产经营 7:日常周转 8：购买原材料 9：购买生产设备 经营贷资金用途</td></tr><tr><td></td><td></td><td></td><td>15：健康养老</td><td>10：经营场所装修 11:购自用车 12：文教娱乐 13：美容医疗 14：数码通讯</td></tr><tr><td>riskInfo</td><td>RiskInfo</td><td>/</td><td>0 风控信息</td><td>51：日常经营周转</td></tr><tr><td>confirmInformationShar e</td><td>String</td><td>1</td><td>M</td><td>是否确认信息共享授 权。1：是，其他：未授</td></tr><tr><td>creditScore</td><td>String</td><td>512</td><td>权 0</td><td>信用分</td></tr><tr><td> scheduleRepay</td><td>String</td><td>1</td><td></td><td>1:按期还 0、为空：灵活还</td></tr><tr><td>loanProductType</td><td> String</td><td>32</td><td>0 贷</td><td>enterpriseLoan:经营 creditLoan:信用贷</td></tr><tr><td> industyInfo</td><td>IndustyInfo</td><td>NA</td><td>0</td><td>不传：默认为信用贷 投向行业 (叶子节点)</td></tr><tr><td>trialExtend</td><td>String</td><td>1024</td><td>0 息</td><td>试算步骤返回的扩展信</td></tr></table></body></html>  

# 5.3.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是 成功信息或失败原因 授信状态:</td></tr><tr><td> status</td><td>String</td><td>10</td><td>C</td><td>SUCCESS：成功，代表 不需要补充资料，继续 走borrow.confirm接口 PROCESS：进行中，代 表需要补充资料，响应 中可带operations 同步返 回，也可以在 borrow.status.query 接口 中异步返回 FAILED:失败 status 为 PROCESS 时,</td></tr><tr><td>operations</td><td>String</td><td>20</td><td>C</td><td>如果需要补充资料则返 回如下信息： 如需补充材料需返回对 应类型，格式为1|2|3 1：身份证影像 2：活体 3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信 7：职业和收入 8：工作单位 9：地址 10:学历 11：仅职业 12：银行短信验证</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>14：营业执照影像(预留) 15：税务信息(预留) 16：电话核实 17：须跳转外部 18：行业投向 20:银行卡用信重签约 待补充资料分别调用第</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA</td><td>C</td><td>四章节对应的接口 如果operation 为5或</td></tr><tr><td>protocolExtendMsg</td><td>String</td><td>NA</td><td>C</td><td>6，需返回协议信息 协议补充信息</td></tr><tr><td>borrowOrderInfo</td><td>BorrowOrderInfo</td><td>NA</td><td>C</td><td>借据单，可只返回必选 字段，非必选字段可由 0209查询详情时返回</td></tr><tr><td>externalOptInfo</td><td>ExternalOptInfo</td><td>NA</td><td>C</td><td>跳转外部信息(operations 返回17或21时，返回)</td></tr></table></body></html>  

# 5.4 0204 – 订单确认（borrow.confirm）  

# 5.4.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号（银行 场景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号 时，必传)</td></tr></table></body></html>  

<html><body><table><tr><td>borrowOrderId</td><td>String</td><td>32</td><td>C 时，必传)</td><td>平台侧订单号(存在</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>C</td><td>活动Id</td></tr><tr><td>contactinfos</td><td>List<ContactInfo</td><td>NA</td><td>0</td><td>联系人信息</td></tr><tr><td></td><td>></td><td></td><td></td><td></td></tr><tr><td>riskInfo</td><td>RiskInfo</td><td>NA</td><td>0</td><td>风控信息</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>0</td><td>用户信息 (百度需要)</td></tr><tr><td>creditScore</td><td>String</td><td>512</td><td>0</td><td>信用分</td></tr><tr><td>facelmagelnfo facelmageld</td><td>Facelmagelnfo</td><td>NA</td><td>0</td><td>活体信息</td></tr><tr><td></td><td>String</td><td>32</td><td>0</td><td>活体识别编号ID（有编</td></tr><tr><td></td><td></td><td></td><td></td><td>号，必传)</td></tr><tr><td>borrowAmount</td><td> String</td><td>20 64</td><td>M</td><td>借款金额，单位：分</td></tr><tr><td>couponNum</td><td>String</td><td></td><td>0</td><td>优惠券号</td></tr><tr><td rowspan="8">repayType</td><td rowspan="8">String</td><td rowspan="8"></td><td>还款类型</td><td></td></tr><tr><td></td><td>1：每月等额（还</td></tr><tr><td></td><td>款总金额一致，不保证</td></tr><tr><td>每一期的本金及利息是</td><td></td></tr><tr><td>一致的)</td><td></td></tr><tr><td></td><td>2：先息后本</td></tr><tr><td>1 M</td><td>3：等本等息（每</td></tr><tr><td></td><td>一期的本金和利息都相</td></tr><tr><td rowspan="5"></td><td rowspan="5"></td><td></td><td>等)</td></tr><tr><td>4：等额本金（每</td><td></td></tr><tr><td></td><td>个月按固定的金额计算</td></tr><tr><td></td><td>利息，既本金每一期都</td></tr><tr><td colspan="2">相等)</td></tr><tr><td></td><td></td><td></td><td></td><td>5:每月等额(锁期)</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M 分期数</td><td></td></tr></table></body></html>  

<html><body><table><tr><td>bindCardId</td><td> String</td><td>32</td><td>M</td><td>收款卡</td></tr><tr><td>cardNo</td><td>String</td><td>32</td><td>M</td><td>收款卡号</td></tr><tr><td>phoneNo</td><td>String</td><td>32</td><td>M</td><td>手机号</td></tr><tr><td>usage</td><td>String</td><td>8</td><td>M</td><td>资金用途 1：个人日常消费 2：房屋装修 3：旅游出行 4：在职深造 5：其他消费 6:生产经营 7:日常周转 8：购买原材料 9：购买生产设备 10：经营场所装修 11:购自用车 12：文教娱乐 13：美容医疗 14：数码通讯 15：健康养老</td></tr><tr><td>otpInfo</td><td>OtpInfo</td><td>NA</td><td>C</td><td>添加验证码（银行场 景必传)</td></tr><tr><td>hwEmployeeTag</td><td>String</td><td>2</td><td>o</td><td>华为员工标签: 1-华为自有员工 其他非华为员工</td></tr></table></body></html>  

# 5.4.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>借款状态： SUCCESS：成功，代表不 需要补充资料，返回借据 订单信息BorrowOrderInfo PROCESS：进行中，代表 需要额度或利率发生变 化，响应中可带changeInfo 同步返回，也可以在 borrow.status.query 接口中 异步返回</td></tr><tr><td>borrowOrderInfo</td><td>BorrowOrderInfo</td><td>NA</td><td>C</td><td>FAILED:失败 借据单，状态为PROCESS 时可只返回必选字段，非 必选字段可由0209查询详 情时返回</td></tr><tr><td>changeInfos</td><td>List<ChangeInfo></td><td>NA</td><td>C</td><td>如果发生协议、额度、利 率变化须告知用户，status 为PROCESS，用户确认后 再次调用该确认接口 状态 为 SUCCESS、FAILED时， 需返回所有字段</td></tr></table></body></html>  

# 5.5 0205 – offer 变更确认（borrow.offer.confirm）  

# 5.5.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderId</td><td>String</td><td>32</td><td>M</td><td>平台侧订单号</td></tr><tr><td>creditScore</td><td>String</td><td>512</td><td>0</td><td>信用分</td></tr></table></body></html>  

# 5.5.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String String</td><td>256 10</td><td>0 C</td><td>返回结果描述，可以是成 功信息或失败原因 授信状态:</td></tr><tr><td>status</td><td></td><td></td><td></td><td>SUCCESS：审批成功，返 回借据订单信息 BorrowOrderInfo PROCESS：审批中，在 borrow.status.query 查询接 □中异步返回最终结果 FAILED：审批失败</td></tr><tr><td>borrowOrderInfo</td><td>BorrowOrderInfo</td><td>NA</td><td>C</td><td>借据单，状态为PROCESS 时可只返回必选字段，非 必选字段可由0209查询详 情时返回 状 态 为 SUCCESS、FAILED时， 需返回所有字段</td></tr></table></body></html>  

# 5.6 0206 - 查询用信申请结果（borrow.status.query）  

# 5.6.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderId</td><td>String</td><td>32</td><td>C</td><td>平台侧订单号 (存在，必</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>传)</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>C</td><td>华为侧借款订单号（广发 /民生必传)</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号（银行场 景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时， 必传)</td></tr></table></body></html>  

# 5.6.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长 度</td><td>M/ 0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0： 表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信 息或失败原因</td></tr><tr><td>userTypeCollectFlag</td><td>String</td><td>8</td><td>o</td><td>收集用户类型信息标志，1: 需要收集，0：无需收集</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>用信状态: status：成功，代表 borrow.apply或 borrow.confirm或 borrow.offer.confirm 成功 PROCESS：进行中，代表 borrow.apply需要额外补 充资料或者borrow.confirm 额度或利率发生变化或者 borrow.offer.confirm</td></tr><tr><td>operations</td><td>String</td><td>20</td><td>C</td><td>如需补充材料需返回对应 类型，格式为1|2|3 1：身份证影像 2：活体</td></tr></table></body></html>  

<html><body><table><tr><td rowspan="11"></td><td rowspan="4"></td><td rowspan="9"></td><td rowspan="9"></td><td rowspan="9">13：华为短信验证码</td><td>3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信</td></tr><tr><td>7：职业和收入</td></tr><tr><td>8：工作单位 9：地址</td></tr><tr><td>10:学历</td></tr><tr><td></td></tr><tr><td>11:职业</td></tr><tr><td>12:短信验证码</td></tr><tr><td>14：营业执照影像(预留)</td></tr><tr><td rowspan="8"></td><td>15：税务信息(预留)</td></tr><tr><td></td></tr><tr><td>16:电话核实</td></tr><tr><td>17：须跳转外部</td></tr><tr><td>18：行业投向</td></tr><tr><td>21：视频录制验证</td></tr><tr><td>待补充资料分别调用第四</td></tr><tr><td>章节对应的接口</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo> NA</td><td>C</td><td>如果operation为5或6,</td></tr><tr><td>protocolExtendMsg</td><td>String</td><td>NA C</td><td>需返回协议信息</td></tr><tr><td>changeInfos</td><td>List<ChangeInfo></td><td>NA C</td><td>协议补充信息 如果发生协议、额度、利 率变化须告知用户，status</td></tr><tr><td>borrowOrderlnfo</td><td>BorrowOrderlnfo NA</td><td>C</td><td>为PROCESS，用户确认 后再次调用确认接口 借据单，状态为 PROCESS时可只返回必 选字段，非必选字段可由 0209查询详情时返回</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>需返回所有字段 失败、处理中、不返回</td></tr><tr><td>changeOffer</td><td>ChangeOffer</td><td>NA</td><td>C</td><td>Offer变化信息</td></tr><tr><td>needPhoneVerify</td><td>String</td><td>+</td><td></td><td>1：需要电话核实 0、不传：不需要</td></tr></table></body></html>  

# 5.7 0207 - 查询借钱记录（borrow.order.query）  

# 5.7.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号 (银行场景 使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时，必 传 查询类型，不传查询所有</td></tr><tr><td>type</td><td>String</td><td>1</td><td>0</td><td>0：未还清(包括申请成功 待放款的单子) 1：已还清 2：返回未申请完成订 单。(BorrowOrderInfo的 status 为00、01、02的订</td></tr><tr><td>beginIndex</td><td>String</td><td>36</td><td>0</td><td>单） (银行类无该接口) 开始记录数，首次查询无</td></tr><tr><td>paging</td><td>String</td><td>1</td><td>0</td><td>0：不分页 1、不传：分页</td></tr><tr><td>ascending</td><td> String</td><td>1</td><td></td><td>0,不传：倒序</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>1：正序</td></tr><tr><td>pageSize</td><td>String</td><td>36</td><td>0</td><td>分页大小，不传且是分页 场景时，默认10</td></tr></table></body></html>  

# 5.7.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td>nextBeginIndex</td><td> String</td><td>36</td><td>o</td><td>下次查询的起始记录 借钱订单</td></tr><tr><td>borrowOrderInfos</td><td>List<BorrowOrderlnfo</td><td>100</td><td></td><td>1、查询未结清和已结清 订单时，可只返回借款时 间、待还本金、已还本 金、还款计划状态和必选 字段，其他非必选字段可 由0209查询详情时再返 回 2、查询未申请完成订单 时，可只返回必选字段, 非必选字段可由0209查 询详情时再返回 3、查询所有订单时，可 只返回借款时间、待还本 金、已还本金、还款计划 状态和必选字段，其他非</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>必选字段可由0209查询 详情时再返回</td></tr></table></body></html>  

# 5.8 0208 – 取消未完成订单（borrow.order.cancel）  

# 5.8.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>C</td><td>借款订单号(存在，必传)</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr></table></body></html>  

# 5.8.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr></table></body></html>  

# 5.9 0209 – 查询借款订单详情 （borrow.order.detail）  

# 5.9.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号</td></tr></table></body></html>  

# 5.9.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td>borrowOrderlnfo</td><td>BorrowOrderInfo</td><td>NA</td><td>c</td><td>借据</td></tr><tr><td>repayPlanlnfos</td><td>List<RepayPlanInf 0V</td><td>NA</td><td>M</td><td>还款计划</td></tr></table></body></html>  

5.10 0210 – 分期购默认试算（borrow.trial.default.purchase）  

# 5.10.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>C</td><td>借贷平台的用户账号</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：分</td></tr></table></body></html>  

<html><body><table><tr><td>fqNums</td><td>String</td><td>32</td><td>C</td><td>试算的期数，竖线分隔：3| 6/12</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>C</td><td>活动Id</td></tr></table></body></html>  

# 5.10.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M /0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td>hirePlanInfos</td><td>List<hirePlanInfo></td><td>NA</td><td>M</td><td>分期计划列表</td></tr></table></body></html>  

# 5.11 0211 – 分期购创建订单（borrow.apply.purchase）  

# 5.11.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长 度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账 号</td></tr><tr><td>mallOrderId</td><td>String</td><td>64</td><td>M</td><td>商城订单号</td></tr><tr><td>loanChannel</td><td>String</td><td>32</td><td>0</td><td>渠道信息 normal:借贷渠道 hirePurchase:分 期购渠道</td></tr><tr><td>subMallOrderInfos</td><td>List<SubMallOrderInfo></td><td>NA</td><td>C</td><td>子订单信息列表</td></tr><tr><td>interestFreeTag</td><td>String</td><td>2</td><td>C</td><td>免息标识，1：免息</td></tr></table></body></html>  

<html><body><table><tr><td>coinAccountNo</td><td>String</td><td>12 8</td><td>C</td><td>零钱账户协议编号 （银行场景使用，必</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>M</td><td>传） 签约单号</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr><tr><td> activityCode</td><td>String</td><td>64</td><td>c</td><td>活动Id</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：</td></tr><tr><td>couponNum</td><td>String</td><td>64</td><td>0</td><td>分 优惠券号</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>还款类型 1：每月等额 2：先息后本 3：等本等息 4：等额本金</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>分期数</td></tr><tr><td>bindCardId</td><td>String</td><td>32</td><td>C</td><td>收款卡</td></tr><tr><td>cardNo</td><td>String</td><td>32</td><td>C</td><td>收款卡号</td></tr><tr><td>riskInfo</td><td>RiskInfo</td><td>/</td><td>0</td><td>风控信息</td></tr><tr><td>confirmInformation Share</td><td>String</td><td>1</td><td>M</td><td>是否确认信息共享授 权。1：是，其他: 未授权</td></tr><tr><td>creditScore</td><td>String</td><td>512</td><td>0</td><td>信用分</td></tr></table></body></html>  

# 5.11.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr></table></body></html>  

<html><body><table><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是 成功信息或失败原因</td></tr><tr><td>status operations</td><td>String</td><td>10</td><td>C C</td><td>授信状态: SUCCESS：成功，代表 不需要补充资料，继续 走 borrow.confirm 接口 PROCESS：进行中，代 表需要补充资料，响应 中可带 operations 同步返 回，也可以在 borrow.status.query 接口 中异步返回 FAILED:失败 status 为 PROCESS 时,</td></tr><tr><td></td><td>String</td><td>20</td><td></td><td>如果需要补充资料则返 回如下信息： 如需补充材料需返回对 应类型，格式为1|2|3 1：身份证影像 2：活体 3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信 7：职业和收入 8：工作单位 9：地址 10：学历 11:仅职业 12：银行短信验证 待补充资料分别调用第</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA</td><td>C</td><td>四章节对应的接口 如果operation为5或 6，需返回协议信息</td></tr><tr><td>borrowOrderInfo</td><td>BorrowOrderInfo</td><td>NA C</td><td></td><td>借据单，可只返回必选 字段，非必选字段可由</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>0209查询详情时返回</td></tr></table></body></html>  

# 5.12 0212 – 分期购订单确认（borrow.confirm.purchase）  

# 5.12.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderId</td><td>String</td><td>32</td><td>M</td><td>平台侧订单号</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>4</td><td>华为侧借款订单号</td></tr><tr><td>contactInfos</td><td>List<Contactlnfo</td><td>NA</td><td>o</td><td>联系人信息</td></tr><tr><td>riskInfo</td><td>RiskInfo</td><td>NA</td><td>0</td><td>风控信息</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>0</td><td>用户信息</td></tr><tr><td>creditScore</td><td>String</td><td>512</td><td>0</td><td>信用分</td></tr><tr><td>facelmagelnfo</td><td>Facelmagelnfo</td><td>NA</td><td>o</td><td>活体信息</td></tr><tr><td>facelmageld</td><td>String</td><td>32</td><td>0</td><td>活体识别编号ID（有编 号，必传)</td></tr></table></body></html>  

# 5.12.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td> status</td><td>String</td><td>10</td><td>C</td><td>授信状态: SUCCESS：成功，代表不</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>需要补充资料，返回借据 订单信息BorrowOrderInfo PROCESS：进行中，代表 需要额度或利率发生变 化，响应中可带changeInfo 同步返回，也可以在 borrow.status.query 接口中 异步返回 FAILED:失败</td></tr><tr><td>borrowOrderInfo</td><td>BorrowOrderInfo</td><td>NA</td><td>C</td><td>借据单，状态为PROCESS 时可只返回必选字段，非 必选字段可由0209查询详 情时返回</td></tr><tr><td>changeInfos</td><td>List<ChangeInfo></td><td>NA</td><td>C</td><td>如果发生协议、额度、利 率变化须告知用户，status 为PROCESS，用户确认后 再次调用该确认接口 状态 为 SUCCESS、FAILED时，</td></tr></table></body></html>  

5.13 0213 分 期 购 查 询 用 信 申 请 结 果（borrow.status.query.purchase）  

# 5.13.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td> String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderId</td><td>String</td><td>32</td><td>M</td><td>平台侧订单号</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号（银行场 景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时， 必传)</td></tr></table></body></html>  

# 5.13.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示 失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息 或失败原因</td></tr><tr><td> status</td><td>String</td><td>10</td><td>C</td><td>用信状态: status：成功，代表 borrow.apply 或 borrow.confirm或 borrow.offer.confirm 成功 PROCESS：进行中，代表 borrow.apply需要额外补充资 料或者borrow.confirm额度或 利率发生变化或者 borrow.offer.confirm</td></tr><tr><td>operations</td><td>String</td><td>20</td><td>C</td><td>FAILED：失败 如需补充材料需返回对应类 型，格式为1|2|3 1：身份证影像 2：活体 3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信 7：职业和收入 8：工作单位 9：地址 10：学历 11:职业 12:短信验证码</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>待补充资料分别调用第四章 节对应的接口</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA</td><td>C</td><td>如果operation为5或6，需 返回协议信息</td></tr><tr><td>changeInfos</td><td>List<ChangeInfo></td><td>NA</td><td>C</td><td>如果发生协议、额度、利率 变化须告知用户，status 为 PROCESS，用户确认后再次 调用确认接口</td></tr><tr><td>borrowOrderlnfo</td><td>BorrowOrderlnfo</td><td>NA</td><td>C</td><td>借据单，状态为PROCESS 时 可只返回必选字段，非必选 字段可由0209查询详情时返 回 状态为 SUCCESS、FAILED 时，需返回所有字段 失败、处理中、不返回</td></tr></table></body></html>  

5.14 0214 取 消 分 期 购 未 完 成 订 单（borrow.order.cancel.purchase）  

# 5.14.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号</td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr></table></body></html>  

# 5.14.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr></table></body></html>  

<html><body><table><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr></table></body></html>  

5.15 0215 查 询 借 款 试 算 优 惠 券 列 表（borrow.trial.couponInfos.query）  

# 5.15.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：分</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>M</td><td>签约单号 还款类型</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>1：每月等额（还款总 金额一致，不保证每一期的 本金及利息是一致的) 2：先息后本 3：等本等息（每一期 的本金和利息都相等) 4：等额本金 （每个月 按固定的金额计算利息，既 本金每一期都相等) 5:每月等额(锁期)</td></tr><tr><td>fqNum</td><td>String</td><td>8</td><td>M</td><td>分期数</td></tr></table></body></html>  

<html><body><table><tr><td>trialExtend</td><td>String</td><td>1024</td><td>0</td><td>试算步骤返回的扩展信息</td></tr><tr><td>pageSize</td><td> String</td><td>36</td><td>0</td><td>分页大小 (不传默认查全部)</td></tr><tr><td>beginIndex</td><td>String</td><td>36</td><td>0</td><td>查询开始记录数（分页场景 需要，不传默认0)</td></tr></table></body></html>  

# 5.15.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td>couponInfos</td><td>List<CouponInfo></td><td>NA</td><td>0</td><td>可用券列表</td></tr></table></body></html>  

5.16 0216  – 查 询 借 款 试 算 还 款 计 划 列 表（borrow.trial.repayPlanInfos.query）  

# 5.16.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowAmount</td><td>String</td><td>20</td><td>M</td><td>借款金额，单位：分</td></tr><tr><td>creditOrderId</td><td> String</td><td>36</td><td>M</td><td>签约单号</td></tr><tr><td>repayType</td><td>String</td><td>1</td><td>M</td><td>还款类型 1：每月等额 (还款总</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>金额一致，不保证每一期的 本金及利息是一致的) 2：先息后本 3：等本等息 (每一期 的本金和利息都相等) 4：等额本金 (每个月 按固定的金额计算利息，既 本金每一期都相等)</td></tr><tr><td></td><td></td><td></td><td></td><td>5:每月等额(锁期)</td></tr><tr><td>fqNum</td><td>String String</td><td>8 64</td><td>M 0</td><td>分期数 选择的优惠券号</td></tr><tr><td>couponNum trialExtend</td><td>String</td><td>1024</td><td>0</td><td>试算步骤返回的扩展信息</td></tr></table></body></html>  

# 5.16.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非 0：表示失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成 功信息或失败原因</td></tr><tr><td>totalAmt</td><td>String</td><td>20</td><td>M</td><td>合计需还，单位分</td></tr><tr><td>rate</td><td>String</td><td>20</td><td>M</td><td>利率，单位是%</td></tr><tr><td>totalInterestAmt</td><td>String</td><td>20</td><td>M</td><td>默认条件下总利息， 单位分</td></tr><tr><td>repayPlanInfos</td><td>List<RepayPlanInfo></td><td>NA</td><td>M</td><td>本借据还款计划列表</td></tr></table></body></html>  

# 6 还款接口(借贷平台提供)  

# 6.1 0301 - 查询月账单/所有借贷（repay.plan.query）  

# 6.1.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td> String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>c</td><td>零钱账户协议编号（银行场 景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时， 必传)</td></tr><tr><td>repayDay</td><td>String</td><td>16</td><td>C</td><td>还款日（不传默认返回当 期)</td></tr></table></body></html>  

# 6.1.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td></td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>totalOrderCnt</td><td>String</td><td>16</td><td>M</td><td>待还款总笔数（依 据</td></tr></table></body></html>  

<html><body><table><tr><td>totalPrincipalAm t</td><td>String</td><td>20</td><td>M</td><td>soroworaerm os 中待还借据) 待还款总金额，单 位分（依据 borrowOrderlnf os 中待还金额总</td></tr><tr><td>curOrderCnt</td><td>String</td><td>16</td><td>M</td><td>和) 本期还款笔数（截 止日前的当期账单 用户借还总比数。 用户未逾期情况 下，过当期还款日 更新至下一期账</td></tr><tr><td>curRepayAmt</td><td> String</td><td>20</td><td>M</td><td>单) 本期应还总金额， 单位分 (同上) 还款银行名称＋</td></tr><tr><td>transParty</td><td> String</td><td>128</td><td>C</td><td>（卡号后四位）， 如： 民生银行 (2121)</td></tr><tr><td>balanceEnough</td><td>String</td><td>1</td><td>C</td><td>还款卡余额是否充 足：充足1，不 足：0</td></tr><tr><td>repayDate</td><td>String</td><td>16</td><td>C</td><td>本期还款日，格式 yyyy-mm-dd( 无 固定还款日可返</td></tr><tr><td>fistOverdueDate</td><td>String</td><td>16</td><td>C</td><td>回 逾期起始日，格式</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>yyyy-mm-dd</td></tr><tr><td>canRepayDate</td><td>String</td><td>16</td><td>C</td><td>本期可还日，格式 yyyy-mm-dd (无此概念，可不 返回)</td></tr><tr><td>curReplayPlans</td><td>List<RepayPlanlnfo></td><td>NA</td><td>C</td><td>本期账单的还款计 划列表(查询指定 还款日的还款计 划，在此字段返 回,逾期不返回该</td></tr><tr><td>repayPlanSummarys</td><td>List <RepayPlanSummary ></td><td>NA</td><td>C</td><td>字段) 未来期还款计划概 要</td></tr><tr><td>overdueOrderlnf 0S</td><td>List<BorrowOrderlnfo ></td><td>NA</td><td>C</td><td>逾期订单信息，逾 期时返回，必传字 段+逾期金额+总 罚息+逾期起始日</td></tr><tr><td>borrowOrderlnfos</td><td>List<BorrowOrderlnfo></td><td>100</td><td>o</td><td>借款订单 (未结清 的)</td></tr><tr><td>nextReplayPlans</td><td>List<RepayPlanlnfo></td><td>NA</td><td>C</td><td>未来账单还款计划 列表</td></tr></table></body></html>  

# 需要注意二点：  

1：用户在还款日前把当期账单全部还清了，不要直接更新至下一期，只有等过了还款日且没逾期才更新到最新一期2：当前的还款计划表是只增不减的，用户还清了不会过滤掉，只需改状态，用户在截止日借钱了，需更新至当期计划表里。6 月10 日至7 月10 日之间的借据纳入到7 月20 日还款日。7 月10 日至8 月10 号纳入到到8 月20 日还款日。  

情况一：6 月10 日至7 月10 日之间的借据纳入到7 月20 日还款日。7 月10 日至8 月10 号纳入到到8 月20 日还款日。  

7 月15 日用户结清7 月账单，本期应还都为0，本期还款计划表状态改为已结清状态。7 月  
16 日借了一笔，纳入8 月账单。  

7 月25 日本期应还和本期计划表为7 月10 日起的用户的借据。此时用户可以走正常还款，如果用户还清了，则本期应还为0，本期账单还款计划表状态改为已结清。用户在26 日又接了一笔金额6000，但未过8 月10 日截止日，该笔借据应纳入本期账单。此时的本期应还应为6000。本期还款计划表该加入此借据，此时本期还款计划表应为7 月25 已结清的加上26 日未结清的。  

# 6.2 0302 – 还款试算（repay.trial）  

# 6.2.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td> String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>c</td><td>零钱账户协议编号 (银行 场景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号 时，必传)</td></tr><tr><td>activityCode</td><td> String</td><td>64</td><td>C</td><td>活动Id</td></tr><tr><td>curRepayAmt</td><td> String</td><td>20</td><td>C</td><td>还款总额（用户限额场 景</td></tr><tr><td>type</td><td>String</td><td>1</td><td>M</td><td>还款类型: 1：正常计划还款 2：剩余计划全部还 清</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>3：自定义金额还款 传1时：若不逾期: 查询额度合同下的多 个借据最近一个还款 日信息； 传1时，若有逾期： 查询额度合同下的所 有逾期借据信息 传2、3时，部分平 台为单一借据提前还</td></tr><tr><td>repayTriallnfos</td><td>List<RepayTriallnfo V</td><td>NA</td><td>C</td><td>款试算 还款订单信息 还款类型1也传 还款类型2、3时， 部分平台只允许上送 单一借据（端侧适</td></tr><tr><td>bindCardId</td><td>String</td><td>32</td><td>C</td><td>配）提前还款 还款卡绑定id</td></tr><tr><td>cardNo</td><td>String</td><td>32</td><td></td><td>还款卡号（微众必 传</td></tr></table></body></html>  

# 6.2.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td> String</td><td>256</td><td>0</td><td>返回结果描述，可</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>以是成功信息或失 败原因</td></tr><tr><td>curRepayAmt</td><td>String</td><td>20</td><td>M</td><td>本期应还总额，单 位分</td></tr><tr><td>repayPrincipalAm t</td><td>String</td><td>20</td><td>M</td><td>本期应还本金，单 位分</td></tr><tr><td>repaylnterestAmt</td><td>String</td><td>20</td><td>M</td><td>本期应还利息，单 位分</td></tr><tr><td>repayReduceAmt</td><td>String</td><td>20</td><td></td><td>本期减免金额，单 位分</td></tr><tr><td>punishInterest</td><td>String</td><td>20</td><td></td><td>本期罚息，单位分 当前账单的还款计</td></tr><tr><td>curReplayPlans</td><td>List<RepayPlanlnfo></td><td>100</td><td>M</td><td>划列表 提前还款时，按借 据维度返回 逾期、正常还款，</td></tr><tr><td>repayTrialld</td><td>String</td><td>64</td><td>C</td><td>按期数维度返回 试算流水号</td></tr><tr><td>needResign</td><td>String</td><td>1</td><td>C</td><td>是否要重新签约 1:需要 0、不传：不需要</td></tr><tr><td>balanceEnough</td><td>String</td><td>1</td><td>C</td><td>还款卡余额是否充 足：充足1，不 足：0</td></tr></table></body></html>  

# 6.3 0303 - 确认还款（repay.confirm）  

# 6.3.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号（银 行场景使用，存在，必</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>传）） 签约单号（有签约单号</td></tr><tr><td>orgRepayOrderId</td><td>String</td><td>32</td><td>M</td><td>时，必传) 华为侧还款申请订单号</td></tr><tr><td>activityCode</td><td>String</td><td>64</td><td>C</td><td>活动Id</td></tr><tr><td>curRepayAmt</td><td>String</td><td>20</td><td>M</td><td>本期应还总金额，单 位分</td></tr><tr><td>curReplayPlans</td><td>List<RepayPlanlnfo V</td><td>100</td><td>M</td><td>本期账单的还款计划 列表，从还款计划或 还款试算中返回</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>C</td><td>借贷平台侧银行卡绑 定号</td></tr><tr><td>cardNo</td><td>String</td><td>32</td><td>C</td><td>广发/民生：零钱账户 的绑定卡号 如果不送，银行从零 钱账户扣款。如果 送，银行实现“绑定 卡充值+零钱还款”</td></tr><tr><td>type</td><td>String</td><td>1</td><td>M</td><td>微众：还款卡号 还款类型：</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>1：正常计划还款 2：剩余计划全部还 清 3：自定义金额还款</td></tr><tr><td>otpInfo</td><td>OtpInfo</td><td>NA</td><td>C</td><td>验证码 (兼容还款卡 补签约场景)</td></tr></table></body></html>  

# 6.3.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功， 非0：接口调用表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是 成功信息或失败原因</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>还款状态： SUCCESS:成功 PROCESS:进行中 FAILED:失败</td></tr><tr><td>billStatus</td><td>String</td><td>8</td><td>C</td><td>还款成功后账单状态 0：本期已还清 1：本期未还清 2:逾期</td></tr><tr><td>failedReason</td><td>String</td><td>64</td><td>C</td><td>失败原因（返回错误码 即可，如：1209) 1209：卡余额不足 1007：日限额 1006：单笔限额 xxxx:其他</td></tr><tr><td>capitalType</td><td>String</td><td>2</td><td>C</td><td>资方类型</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>1:自营 2：联合贷 3：助贷</td></tr><tr><td>repayOrderInfo</td><td>RepayOrderInfo</td><td>NA</td><td>C</td><td>返回还款记录 逾期金额，单位：分。</td></tr><tr><td>overdueAmount</td><td>String</td><td>20</td><td>C</td><td>还款成功后，还有逾期 时返回，包含本金，利 息，罚息，优惠</td></tr><tr><td>overdueOrderCn t</td><td>String</td><td>8</td><td>C</td><td>逾期订单数量，还款成 功后，还有逾期时返 回。</td></tr><tr><td>amountChangeln fo</td><td>AmountChangeln fo</td><td>NA</td><td>o</td><td>额度调整 （还款成功 后，返回)</td></tr><tr><td>rateChangelnfo</td><td>RateChangelnfo</td><td>NA</td><td>0</td><td>利率调整 (还款成功 后，返回)</td></tr></table></body></html>  

# 6.4 0304 - 查询还款结果（repay.status.query）  

# 6.4.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>C</td><td>零钱账户协议编号 (银行场景使 用，存在，必传））</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时，必 传)</td></tr><tr><td>repayOrderld</td><td>String</td><td>32</td><td>C</td><td>还款订单号(有订单号，必 传传)</td></tr></table></body></html>  

<html><body><table><tr><td>orgRepayOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧还款申请订单号</td></tr></table></body></html>  

# 6.4.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功， 非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是 成功信息或失败原因</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>还款状态: SUCCESS:成功 PROCESS:进行中 FAILED:失败</td></tr><tr><td>failedReason</td><td>String</td><td>64</td><td>C</td><td>失败原因（返回错误码 即可，如：1209) 1209：卡余额不足 1007：日限额 1006:单笔限额</td></tr><tr><td>capitalType</td><td>String</td><td>2</td><td>C</td><td>xxxx:其他 资方类型 1:自营 2：联合贷</td></tr><tr><td>repayOrderlnfo</td><td>RepayOrderlnfo</td><td>NA</td><td>C</td><td>3：助贷 还款成功或失败时返回 还款记录</td></tr><tr><td>overdueAmount</td><td>String</td><td>20</td><td>C</td><td>逾期金额，单位：分。 还款成功后，还有逾期 时返回，包含本金，利 息，罚息，优惠</td></tr><tr><td>overdueOrderCn t</td><td>String</td><td>8</td><td>C</td><td>逾期订单数量，还款成 功后，还有逾期时返</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>回。</td></tr><tr><td>amountChangeln fo</td><td>AmountChangeln fo</td><td>NA</td><td>0</td><td>额度调整 (还款成功 后，返回)</td></tr><tr><td>rateChangelnfo</td><td>RateChangelnfo</td><td>NA</td><td>0</td><td>利率调整 (还款成功 后，返回)</td></tr></table></body></html>  

# 6.5 0305 - 查询还款记录（repay.order.query）  

# 6.5.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>c</td><td>零钱账户协议编号 (银行场 景使用，存在，必传））</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时， 必传)</td></tr><tr><td>queryDate</td><td>String</td><td>16</td><td>0</td><td>传该字段支持按年或月查 询，格式yyyy-mm，查询 指定月份；格式yyyy,查询 指定年所有记录，不传查询 所有记录，建议分页返回</td></tr><tr><td>beginIndex</td><td>String</td><td>36</td><td>o</td><td>开始记录数，首次查询无</td></tr><tr><td>type</td><td>String</td><td>1</td><td>0</td><td>查询类型，不传查询所有 0：进行中（暂未使用） 1:成功 2:失败</td></tr><tr><td>paging</td><td>String</td><td>1</td><td>0</td><td>0：不分页</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>1、不传：分页</td></tr></table></body></html>  

# 6.5.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示 成功，非0：表示 失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述， 可以是成功信息或 失败原因</td></tr><tr><td>nextBeginlnde ×</td><td>String</td><td>36</td><td>0</td><td>下次查询的起始记 录</td></tr><tr><td>repayOrderlnf 0S</td><td>List<RepayOrderInf 0></td><td>100</td><td></td><td>还款记录列表</td></tr></table></body></html>  

# 6.6 0306 - 查询还款记录详情（repay.order.detail.query）  

# 6.6.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>coinAccountNo</td><td>String</td><td>128</td><td>c</td><td>零钱账户协议编号（银行场 景使用，存在，必传)</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>C</td><td>签约单号（有签约单号时，</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>必传)</td></tr><tr><td>repayOrderld</td><td>String</td><td>32</td><td>M</td><td>还款订单号</td></tr></table></body></html>  

# 6.6.2 响应消息  

6.7 0307 – 分期购退款（repay.confirm.purchase）  


<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0： 表示成功，非 0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描 述，可以是成 功信息或失败 原因</td></tr><tr><td>repayOrderDetai Is</td><td>List<RepayOrderDeta il></td><td>100</td><td>M</td><td>还款详情</td></tr></table></body></html>  

# 6.7.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号(存在， 必传)</td></tr><tr><td>mallOrderId</td><td>String</td><td>64</td><td>M</td><td>商城订单号</td></tr><tr><td>orgRefundOrderId</td><td> String</td><td>64</td><td>M</td><td>华为退款订单号</td></tr></table></body></html>  

<html><body><table><tr><td>subMallOrderInfos</td><td>List <SubMallOrderInfo></td><td>NA</td><td>C</td><td>子订单信息列表</td></tr><tr><td>refundAmount</td><td>String</td><td>20</td><td>M</td><td>退款总金额</td></tr></table></body></html>  

# 6.7.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示 成功，非0：接 □调用表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述， 可以是成功信息 或失败原因</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>退款状态: SUCCESS：成 功 PROCESS:进 行中 FAILED：失败</td></tr><tr><td>orgRefundOrderId</td><td> String</td><td>64</td><td>M</td><td>华为退款订单号</td></tr><tr><td>refundOrderId</td><td>String</td><td>64</td><td>M</td><td>乐信退款订单号</td></tr></table></body></html>

6.8 0308 查 询 分 期 购 退 款 结 果（repay.status.query.purchase）  

# 6.8.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr></table></body></html>

华为保密信息,未经授权禁止扩散  

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号</td></tr><tr><td>orgRefundOrderId</td><td>String</td><td>64</td><td>M</td><td>华为退款订单号</td></tr><tr><td>refundOrderId</td><td>String</td><td>64</td><td>c</td><td>乐信退款订单号</td></tr><tr><td>mallOrderId</td><td>String</td><td>64</td><td>M</td><td>商城订单号</td></tr></table></body></html>  

# 6.8.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示 成功，非0：表 示失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述， 可以是成功信息 或失败原因</td></tr><tr><td>status</td><td>String</td><td>10</td><td>C</td><td>还款状态: SUCCESS:成 功 PROCESS:进 行中 FAILED：失败</td></tr></table></body></html>

6.9 0309 查 询 还 款 卡 是 否 余 额 充 足（repay.balance.enough.query）  

# 6.9.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>M</td><td>借贷平台侧绑定卡Id</td></tr><tr><td>amount</td><td>String</td><td>20</td><td>M</td><td>查询金额，单位分</td></tr></table></body></html>  

# 6.9.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示 成功，非０：表 示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述， 可以是成功信息 或失败原因</td></tr><tr><td>balanceEnough</td><td>String</td><td>1</td><td>M</td><td>还款卡余额是否 充足:充足1，不 足:0</td></tr></table></body></html>  

# 7 绑卡接口(银行类使用零钱服务)  

7.1 0401 - 查询支持的银行列表（bank.support.list）[公开]  

# 7.1.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderIds</td><td>List<String V</td><td>NA</td><td>C</td><td>借款订单号列表</td></tr></table></body></html>  

# 7.1.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>bankInfos</td><td>List<BankInfo></td><td>NA</td><td>o</td><td>支持的银行信息列 表（与下面url二</td></tr><tr><td>url</td><td>String</td><td>4096</td><td>o</td><td>选一） 外部跳转URL</td></tr><tr><td>DATA样例</td><td colspan="4">{</td></tr></table></body></html>  

<html><body><table><tr><td>}</td><td colspan="2">"resultCode": "0", "resultDesc":"success"</td></tr><tr><td rowspan="4">返回码定义：</td><td colspan="2"></td></tr><tr><td>resultcod e</td><td>resultdesc</td></tr><tr><td>0</td><td>success(成功)</td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td></td><td colspan="2"></td></tr></table></body></html>  

# 7.2 0402 - 请求绑卡（bindcard.apply）  

# 7.2.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>cardNo</td><td>String</td><td>64</td><td>M</td><td>卡号</td></tr><tr><td>phoneNo</td><td>String</td><td>32</td><td>M</td><td>预留手机号</td></tr><tr><td>DATA样例 {</td><td colspan="4">"accountNo": "6225789456187951288", "cardNo": “62252665123010010000021", "phoneNo":"***********" }</td></tr></table></body></html>  

# 7.2.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr></table></body></html>  

<html><body><table><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>authld</td><td> String</td><td>128</td><td>M</td><td>校验短信的ld</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode":"0", "resultDesc":"success", "authld":"123456"</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="5">resultcod resultdesc</td></tr><tr><td>e</td><td colspan="5"></td></tr><tr><td>0</td><td colspan="5">success(成功)</td></tr><tr><td>E999999</td><td colspan="5">服务器内部错误</td></tr><tr><td>E040201</td><td colspan="5">卡bin不支持，请查询支持的卡列表</td></tr><tr><td>E040202</td><td colspan="5">卡号和实名信息不一致</td></tr><tr><td>E040203</td><td colspan="5">银行卡预留手机号不一致</td></tr><tr><td colspan="5"></td></tr></table></body></html>  

# 7.3 0403 - 绑卡确认（bindcard.confirm）  

# 7.3.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>authId</td><td>String</td><td>128</td><td>M</td><td>取值为card.apply接口返 回</td></tr><tr><td>code</td><td>String</td><td>16</td><td>M</td><td>验证码</td></tr><tr><td>riskInfo</td><td>RiskInfo</td><td>/</td><td>0</td><td>风控信息</td></tr><tr><td>cardNo</td><td>String</td><td>64</td><td>M</td><td>卡号</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo": "6225789456187951288",</td></tr></table></body></html>  

<html><body><table><tr><td>}</td><td>"authld":“123456", "code":"666666"</td></tr></table></body></html>  

# 7.3.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以 是成功信息或失败原 因</td></tr><tr><td>cardInfo DATA样例</td><td>CardInfo {</td><td>NA</td><td>M</td><td>卡片信息</td></tr><tr><td></td><td colspan="4">"resultCode":"0", "resultDesc":"success", "cardlnfo": { "cardNo":"6225789456187951288", "bindCardld":"*********" } 广</td></tr><tr><td rowspan="7">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod</td><td colspan="3">resultdesc</td></tr><tr><td>e</td><td colspan="3">success(成功)</td></tr><tr><td>0</td><td colspan="3">服务器内部错误</td></tr><tr><td>E999999 E040301</td><td colspan="3">卡bin不支持，请查询支持的卡列表</td></tr><tr><td>E040302</td><td colspan="3">卡号和实名信息不一致</td></tr><tr><td>E040303</td><td colspan="4">银行卡预留手机号不一致</td></tr></table></body></html>  

# 7.4 0404 - 用户自动还款设置（account.repay.set）  

# 7.4.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号 如果仅传此参数，则相当于 查询</td></tr><tr><td>autoPaySwitch</td><td>String</td><td>8</td><td>0</td><td>自动还款开关 0：自动还款启用 1：自动还款关闭</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>0</td><td>借贷平台侧绑定卡Id</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo": "6225789456187951288", " autoPaySwitch ":“1", "bankCardld":"*********" }</td></tr></table></body></html>  

# 7.4.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>autoRepay</td><td>String</td><td>1</td><td>c</td><td>自动还款0开启1</td></tr></table></body></html>  

<html><body><table><tr><td></td><td colspan="2"></td><td></td><td></td><td>未开启，已签约状 态返回</td></tr><tr><td>DATA样例</td><td colspan="5">{ "resultCode":"0", "resultDesc":"success"</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td>E040401</td><td colspan="3">银行卡不支持设置自动还款</td></tr><tr><td></td><td colspan="5"></td></tr></table></body></html>  

# 7.5 0405 - 绑卡查询（bindcard.query）  

# 7.5.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderIds</td><td>List<String ></td><td>NA</td><td>C</td><td>借款订单号列表</td></tr><tr><td>scene</td><td>String</td><td>32</td><td>0</td><td>不传为默认查所有； borrow：查借款账户 repay:查还款账户 all：查询借款和还款账 户 transfer:查允许转账</td></tr><tr><td>DATA样例</td><td colspan="4">账户 {</td></tr></table></body></html>  

<html><body><table><tr><td></td><td>}</td></tr></table></body></html>  

# 7.5.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultcode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultdesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>cardInfos</td><td>List<CardInfo></td><td>/</td><td>M</td><td>卡片列表，含银 行额度信息（微 众只返回收款账 户列表)</td></tr><tr><td>repayCardlnfos</td><td>List<CardInfo></td><td></td><td>0</td><td>还款账户列表信息 (微众银行使用)</td></tr><tr><td>transferCardInfos</td><td>List<CardInfo></td><td>NA</td><td>0</td><td>可转账至还款账号 的账号列表（微业 贷使用)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success" }</td></tr><tr><td rowspan="5">返回码定义：</td><td colspan="5">resultcod resultdesc</td></tr><tr><td>e</td><td colspan="4"></td></tr><tr><td>0</td><td colspan="4">success(成功)</td></tr><tr><td>E999999</td><td colspan="4">服务器内部错误</td></tr><tr><td></td><td colspan="4"></td></tr></table></body></html>  

# 7.6 0406 – 删除绑定卡（bindcard.delete）  

# 7.6.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>bindCardld</td><td>String</td><td>32</td><td>M</td><td>借贷平台侧绑定卡Id</td></tr><tr><td>autoRepayBindCar dld</td><td>String</td><td>32</td><td>C</td><td>如果开通了自动还款并 且删除的 bindCardld 是 自动还款绑定卡，则在默 认传一个已有的绑定卡给 借贷平台；如果不传代表 无可用的默认还款卡，自 动还款能力取消</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo": "6225789456187951288", "bindCardld":“*********" }</td></tr></table></body></html>  

# 7.6.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resuItDesc</td><td> String</td><td>256</td><td>o</td><td>返回结果描述，可 以是成功信息或失</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>败原因</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success" 广</td></tr><tr><td rowspan="4">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod e</td><td colspan="3">resultdesc</td></tr><tr><td>0</td><td colspan="3">success(成功)</td></tr><tr><td>E999999</td><td colspan="3">服务器内部错误</td></tr><tr><td></td><td colspan="5"></td></tr></table></body></html>  

# 7.7 0407 - 卡 Bin 校验（card.bin.check）  

# 7.7.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td> accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>cardNo</td><td>String</td><td>64</td><td>M</td><td>卡号</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo":"6225789456187951288, "cardNo":"62252665123010010000021" }</td></tr></table></body></html>  

# 7.7.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr></table></body></html>  

<html><body><table><tr><td>resultDesc</td><td colspan="2">String</td><td>256</td><td>0</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>bankInfo</td><td colspan="2">BankInfo</td><td>NA</td><td>C</td><td>卡bin校验通过返 回对应的银行信息</td></tr><tr><td>DATA样例</td><td colspan="5">{ "resultCode": "0", "resultDesc":"success" }</td></tr><tr><td rowspan="6">返回码定义：</td><td colspan="5"></td></tr><tr><td>resultcod</td><td colspan="5">resultdesc</td></tr><tr><td>e 0</td><td colspan="5">success(成功)</td></tr><tr><td>E999999</td><td colspan="5">服务器内部错误</td></tr><tr><td>E040701</td><td colspan="5">卡bin不支持，请查询支持的卡列表</td></tr><tr><td colspan="5"></td></tr></table></body></html>  

# 7.8 0408 – 查询协议列表（protocol.list）  

# 7.8.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td> accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>type</td><td>String</td><td>1</td><td>M</td><td>协议类型: 1：绑卡协议 2：自动扣款协议 3：用户协议与授权</td></tr></table></body></html>  

# 7.8.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr><tr><td>protocolinfos</td><td>List<Protocolinfo></td><td>NA</td><td>M</td><td>协议列表</td></tr></table></body></html>  

# 7.9 0409 – 添加对公账户（corporate.account.add）  

# 7.8.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>enterpriseName</td><td>String</td><td>64</td><td>M</td><td>企业名称</td></tr><tr><td>bankName</td><td>String</td><td>32</td><td>M</td><td>银行名称</td></tr><tr><td>bankCode</td><td>String</td><td>32</td><td>M</td><td>银行编码</td></tr><tr><td>bankBranch</td><td>String</td><td>64</td><td>M</td><td>网点</td></tr><tr><td>bankBranchCode</td><td>String</td><td>32</td><td>M</td><td>网点编码</td></tr><tr><td>cardNo</td><td>String</td><td>64</td><td>M</td><td>卡号</td></tr></table></body></html>  

# 7.8.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr></table></body></html>  

<html><body><table><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成 功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可 以是成功信息或失 败原因</td></tr></table></body></html>  

# 8 回调接口 （华为提供）  

# 8.1 0501 - 通知借贷结果（order.notify）  

# 8.1.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>notifyType</td><td>String</td><td>8</td><td>M</td><td>通知类型 01：用信成功 02：用信失败 03：放款成功 04：放款失败 05：放款二类户成功、提 现一类户失败</td></tr><tr><td>accountNo</td><td>String String</td><td>128 32</td><td>M 0</td><td>06：放款成功后冲销 借贷平台的用户账号 enterpriseLoan：小微</td></tr><tr><td>loanProductType</td><td></td><td></td><td></td><td>企业贷 creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>orderld</td><td>String</td><td>32</td><td>M</td><td>借贷平台订单号</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>orgBorrowOrderId</td><td>String</td><td>32</td><td>M</td><td>华为侧借款订单号</td></tr><tr><td>borrowOrderlnfo</td><td>BorrowOrderlnf</td><td>NA</td><td>M</td><td>借据单详情</td></tr><tr><td>orderCloseRes</td><td>String</td><td>8</td><td>C</td><td>订单关闭原因 1：其他原因 2：资金匹配原因 本字段仅在订单关闭消息 中返回</td></tr><tr><td>desc</td><td>String</td><td>128</td><td>o</td><td>订单关闭原因描述</td></tr></table></body></html>  

# 8.1.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr></table></body></html>  

# 8.2 0602 - 通知还款结果（repay.notify）  

# 8.2.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td> accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td></td><td>enterpriseLoan:小微 企业贷 creditLoan:信用贷</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>不传：默认为信用贷</td></tr><tr><td>orgRepayOrderId</td><td>String</td><td>32</td><td>C</td><td>华为侧还款申请订单号</td></tr><tr><td>repayOrderld</td><td>String</td><td>32</td><td>C</td><td>借贷平台还款订单号</td></tr><tr><td>repayOrderlnfo</td><td>RepayOrderIn fo</td><td>NA</td><td>M</td><td>返回还款记录和还款详 情</td></tr><tr><td>desc</td><td>String</td><td>128</td><td>0</td><td>描述，还款失败时告 知失败原因。 E060050：卡余额不 足 E060051：卡限额 E060052：卡单次限 额 E060053：卡单日限 额</td></tr></table></body></html>  

# 8.2.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultcode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td> String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功信息或失败原因</td></tr></table></body></html>  

# 8.3 0603 - 通知授信结果（credit.status.notify）  

# 8.3.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr></table></body></html>  

<html><body><table><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>newAccountNo</td><td>String</td><td>128</td><td>0</td><td>新用户账号（微业贷 涉及，企业核额失败回 捞变个体户核额成功场 景)</td></tr><tr><td>creditMsgld</td><td>String</td><td>20</td><td>C</td><td>0103接口请求msgld (小微贷场景不传)</td></tr><tr><td>status</td><td>String</td><td>16</td><td>M</td><td>授信状态: SUCCESS：成功，成 功时返回如下信息 FAILED：失败 NEED_SUPPLEMENT :</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>审批中待补录/待激活 enterpriseLoan :小 微企业贷 creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>creditSignlnfo</td><td>CreditSignInf</td><td>NA</td><td>C</td><td>签约信息 成功时：返回 失败时：返回 signStatu s、nextSignDate</td></tr></table></body></html>  

# 8.3.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失 败</td></tr></table></body></html>  

<html><body><table><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失</td></tr><tr><td></td><td></td><td></td><td></td><td>败原因</td></tr></table></body></html>  

# 8.4 0604 – 推送优惠券（coupon.send）  

# 8.4.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>couponInfos</td><td>List<CouponInf 0V</td><td>100</td><td>C</td><td>优惠券信息</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan : 小微企业贷 creditLoan:信用 贷 不传：默认为信用贷</td></tr></table></body></html>  

# 8.4.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功信息或失 败原因</td></tr></table></body></html>  

# 8.5 0605 – 调额通知（credit.change.notify）  

# 8.5.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0 C</td><td>enterpriseLoan:小微 企业贷 creditLoan:信用贷 不传：默认为信用贷 通知类型 1001：提升额度 1002：降低额度</td></tr><tr><td>rateNotifyType</td><td>String</td><td>8</td><td>C</td><td>1004：临时降低额度 通知类型 2001:提升利率 2002:降低利率 2003：临时提升利率</td></tr><tr><td>oldTotalAmount</td><td>String</td><td>20</td><td>0</td><td>2004:临时降低利率 原总额度，单位是分</td></tr><tr><td>oldDailyRate</td><td>String</td><td>20</td><td>o</td><td>原日利率,单位是%</td></tr><tr><td>oldAnnualRate</td><td>String</td><td>20</td><td>0</td><td>原年利率,单位是%</td></tr><tr><td>totalAmount</td><td>String</td><td>20</td><td>C</td><td>总额度，单位是分（固定额 度+临时额度)</td></tr><tr><td>tempAmount</td><td>String</td><td>20</td><td>C</td><td>临时额度，单位是分</td></tr><tr><td>tempRemainAmo unt</td><td>String</td><td>20</td><td>C</td><td>剩余临时额度，单位是分</td></tr></table></body></html>  

<html><body><table><tr><td>tempAmtExpireD ate</td><td>String</td><td>16</td><td>0</td><td>临额有效截止日期： 不传、为空：无有效期, 格式为yyyy-MM-dd</td></tr><tr><td>remainAmount</td><td>String</td><td>20</td><td>C</td><td>剩余额度，单位是分 (包含临 额)</td></tr><tr><td>dailyRate</td><td>String</td><td>20</td><td>C</td><td>日利率,单位是%</td></tr><tr><td>annualRate</td><td>String</td><td>20</td><td>C</td><td>年利率,单位是%</td></tr><tr><td>tempExpireDate</td><td>String</td><td>16</td><td>0</td><td>临价有效截止日期: 格式为yyyy-MM-dd</td></tr><tr><td>entrepreneurTag</td><td>String</td><td>1</td><td>0</td><td>企业用户标识：1企业用 户0普通用户</td></tr><tr><td>source</td><td>String</td><td>32</td><td>0</td><td>来源： negotiate_price:议价中 心 credit_change:提额降息 中心</td></tr></table></body></html>  

# 8.5.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失 败原因</td></tr></table></body></html>  

# 8.6 0606 – 营销通知（promotion.notify）  

# 8.6.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan:小微 企业贷 creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>notifyInfos</td><td>List<NotifyInfo></td><td>100</td><td>M</td><td>通知类型和消息模板</td></tr></table></body></html>  

# 8.6.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失 败原因</td></tr></table></body></html>  

# 8.7 0607 – 手机号修改通知（phoneno.change.notify）  

# 8.7.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr></table></body></html>  

<html><body><table><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan：小微 企业贷 creditLoan:信用贷 不传：默认为信用贷</td></tr><tr><td>phoneNo</td><td>String</td><td>11</td><td>M</td><td>新手机号码</td></tr></table></body></html>  

# 8.7.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非0：表示失 败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功信息或失 败原因</td></tr></table></body></html>  

# 8.8 0608 – 销户回调通知（account.cancel.notify）  

# 8.8.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>notifyType</td><td>String</td><td>8</td><td>M</td><td>通知类型 01：销户成功 (销户失败的场景，不需要 通知华为侧)</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan:小微企 业贷</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>creditLoan:信用贷</td></tr><tr><td></td><td></td><td></td><td></td><td>不传：默认为信用贷</td></tr></table></body></html>  

# 8.8.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr></table></body></html>  

# 8.9 0609 – 身份证过期回调通知（eid.expired.notify）  

# 8.9.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>notifyType</td><td>String</td><td>8</td><td>M</td><td>通知类型 01:已过期</td></tr><tr><td>expirationDate</td><td>String</td><td>16</td><td>c</td><td>过期日，格式yyyy-mm-dd</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>loanProductType</td><td>String</td><td>32</td><td>0</td><td>enterpriseLoan：小微企业</td></tr></table></body></html>  

# 8.9.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr></table></body></html>  

# 8.10 0610 - 通知分期购退款结果（refund.notify.purchase）  

# 8.10.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>borrowOrderld</td><td>String</td><td>32</td><td>M</td><td>借款订单号</td></tr><tr><td>orgRefundOrderId</td><td>String</td><td>64</td><td>M</td><td>华为退款订单号</td></tr><tr><td>refundOrderId</td><td>String</td><td>64</td><td>M</td><td>乐信退款订单号</td></tr><tr><td>status</td><td>String</td><td>10</td><td>M</td><td>SUCCESS:成功 FAILED:失败</td></tr><tr><td>desc</td><td>String</td><td>128</td><td>0</td><td>描述：失败原因等</td></tr></table></body></html>  

# 8.10.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultcode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td></td><td>返回结果描述，可以是成功信息或失败原因</td></tr></table></body></html>  

# 9 优惠券  

# 9.1 0701 – 查询优惠券（coupon.query）  

# 9.1.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/</td><td>说明</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td>0</td><td></td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>o</td><td>签约单号(存在，必传)</td></tr><tr><td>pageSize</td><td> String</td><td>36</td><td>o</td><td>分页大小 (不传默认查全部)</td></tr><tr><td>beginIndex</td><td>String</td><td>36</td><td>0</td><td>查询开始记录数（分页场景 需要，不传默认0)</td></tr></table></body></html>  

# 9.1.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0: 表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功 信息或失败原因</td></tr><tr><td>couponInfos</td><td>List<CouponInf 0></td><td>100</td><td>C</td><td>优惠券信息列表</td></tr></table></body></html>  

# 10 其他  

# 10.1 0801 – 客服凭证查询（sessionid.query）  

# 10.1.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr></table></body></html>  

# 10.1.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0: 表示失败</td></tr><tr><td>resultDesc</td><td> String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功 信息或失败原因</td></tr><tr><td>sessionId</td><td>String</td><td>48</td><td>M</td><td>48位随机数</td></tr></table></body></html>  

# 10.2 0802 – 查询结算账单（settle.bill.query）  

# 10.2.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>settleDate</td><td>String</td><td>16</td><td>M</td><td>账单的结算日期。 格式：yyyy-MM-dd 备注： 结算日期一般为结算周期的 后一个自然天。 传入非结算日期的时间，返 回信息为空。 如结算周期为每自然月 2019-06-01日0点至 2019-07-01日0点的订</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td>空。</td><td>2019-06-01日0点至 2019-07-01日0点结算周 期内的结算单。 请求 2019-06-29会返回</td></tr></table></body></html>  

# 10.2.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0： 表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功 信息或失败原因</td></tr><tr><td>settleBillList</td><td>List<SettleBill></td><td>100</td><td>M</td><td>结算账单列表</td></tr></table></body></html>  

# 10.3 0803 – 外部操作地址查询（sp.operation.addr.query）  

# 【必要】  

# 10.3.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>operation</td><td>String</td><td>32</td><td>M</td><td>creditApply:授信申请 faceCheck：人脸验证 creditChange:提额降息 invoicing:开具发票</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>secondAccountAdd:二类户 添加 (微业贷) negotiatePrice：议价 activityQuery:活动 borrowUsageUp：贷款用途 上传 (微业贷) bankBranchQuery：查询银 行网点 (微业贷) balanceQuery：查询账户余</td></tr><tr><td></td><td></td><td></td><td>C</td><td>额 (微业贷)</td></tr><tr><td>creditOrderId callbackUrl</td><td>String String</td><td>36 4096</td><td>C</td><td>签约单号 回调地址</td></tr><tr><td>operationId</td><td>String</td><td>48</td><td>C</td><td>操作编号， (随机数有效时间5</td></tr><tr><td>dynamicParam</td><td>List<DynamicParam></td><td>NA</td><td>C</td><td>分钟，一次性有效) 动态参数列表 bankBranchQuery 携带</td></tr></table></body></html>  

# 10.3.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0： 表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功 信息或失败原因</td></tr><tr><td>url</td><td>String</td><td>4096</td><td>M</td><td>外部h5 链接</td></tr><tr><td>token</td><td> String</td><td>256</td><td>C</td><td>非动态链接时返回</td></tr><tr><td>validTime</td><td>String</td><td>2</td><td>M</td><td>有效期，单位分钟</td></tr></table></body></html>  

10.4 0804 查 询 转 账 还 款 银 行 信 息（sp.repay.card.info.query）  

# 10.4.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>creditOrderId</td><td>String</td><td>36</td><td>M</td><td>签约单号</td></tr><tr><td>curReplayPlans</td><td>List<RepayPlanInfo></td><td>1000</td><td>M</td><td>本期账单的还款计划 列表，从还款计划或 还款试算中返回</td></tr><tr><td>curRepayAmt</td><td>String</td><td>20</td><td>M</td><td>还款总金额</td></tr><tr><td>type</td><td>String</td><td>1</td><td>M</td><td>还款类型: 1：正常计划还款 2：剩余计划全部还 清 3：自定义金额还款</td></tr><tr><td>capitalType</td><td>String</td><td>2</td><td>M</td><td>资方类型 1:自营 2：联合贷 3：助贷</td></tr></table></body></html>  

# 10.4.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td colspan="5"></td></tr><tr><td colspan="5">2025-7-21 华为保密信息,未经授权禁止扩散</td></tr></table></body></html>  

<html><body><table><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：成功，非0：失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述</td></tr><tr><td>spCardNo</td><td>String</td><td>64</td><td>M</td><td>转账还款卡号</td></tr><tr><td>loanCompany</td><td> String</td><td>128</td><td>M</td><td>资方名称，如：重庆度小满 小额贷款有限公司</td></tr><tr><td>bankName</td><td>String</td><td>128</td><td>M</td><td>开户行名称，如: 中国建设银行</td></tr><tr><td>bankAddr</td><td>String</td><td>256</td><td>M</td><td>开户行地址，如：重庆市/重 庆两江分行营业部</td></tr></table></body></html>  

# 10.5 0805 –借款意图信息提交（borrow.intention.submit）  

# 10.5.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 。</td><td>说明</td></tr><tr><td>infoDate</td><td>String</td><td>16</td><td>C</td><td>数据日期，格式：yyyy-MM- dd</td></tr><tr><td>intentionInfos</td><td>List<String></td><td>NA</td><td>M</td><td>内容</td></tr><tr><td>index</td><td>String</td><td>8</td><td>M</td><td>序号</td></tr></table></body></html>

intentionInfos 中单个数据举例：{accountNo}|2023-08-26|10|0.123456|0.123456|0.123456|0.123456|0.123456|********  

# 10.5.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8號</td><td>M</td><td>操作码0：成功，非0：失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述</td></tr></table></body></html>  

# 10.6 0806 –用户身份信息收集（user.type.collect）  

# 10.6.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台用户账号</td></tr><tr><td>userType</td><td>String</td><td>8</td><td>M</td><td>用户身份类型 (1：生意人0：消费者)</td></tr><tr><td>DATA样例</td><td colspan="4">{ "accountNo":"6225789456187951288", "userType":"1" }</td></tr></table></body></html>  

# 10.6.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示 失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>o</td><td>返回结果描述，可以是成功信息 或失败原因</td></tr></table></body></html>  

# 10.7 0807 –协议信息查询（protocol.info.query）  

# 10.7.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>userInfo</td><td>UserInfo</td><td>NA</td><td>0</td><td>在授信阶段还无accountNo时，查询携带三要素 (姓名、身份证号、手机号)</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>0</td><td>如果已有accountNo，则使用accountNo进行查 询</td></tr><tr><td>protocolName S</td><td>List<String V</td><td>NA</td><td>M</td><td>需查询的协议名称列表</td></tr></table></body></html>  

# 10.7.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示 失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息 或失败原因</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA</td><td>M</td><td>协议信息</td></tr></table></body></html>  

# 10.8 0808 –线下提额申请（offline.raise.amount.apply）  

# 10.8.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台用户账号</td></tr><tr><td>cityCode</td><td>String</td><td>32</td><td>M</td><td>预约提额城市编码 (比如440300等)</td></tr></table></body></html>  

# 10.8.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示 失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息 或失败原因</td></tr></table></body></html>  

# 10.9 0809 –补录项查询（supplement.operations.query）  

# 10.9.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台用户账号</td></tr><tr><td>scene</td><td>String</td><td>32</td><td>M</td><td>查询场景：credit.second.supplement一授信回 捞补录 borrow.first.supplement一用信前补录</td></tr><tr><td>callbackUrl</td><td>String</td><td>4096</td><td>C</td><td>active.supplement一激活补录 回调地址</td></tr></table></body></html>  

# 10.9.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表 示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息 或失败原因</td></tr><tr><td>operations</td><td>String</td><td>20</td><td>C</td><td>如需补充材料需返回对应类型， 格式为1|2|3 1：身份证影像 2：活体 3：用户三要素信息 4：联系人信息 5：须同意的协议列表 6：是否查征信 7：职业和收入 8：工作单位 9：地址 10：学历 11:仅职业 12：银行短信验证码 13：华为短信验证码 14：营业执照影像 (预留) 15：税务信息 (预留)</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>16：电话核实 (用信阶段) 17：须跳转外部补录(多种外部 补录项) 18：行业投向</td></tr><tr><td>protocolInfos</td><td>List<ProtocolInfo></td><td>NA</td><td>C</td><td>如果operation为5或6，需 返回协议信息</td></tr><tr><td>externalOptInfo</td><td>ExternalOptInfo</td><td>NA</td><td>C</td><td>跳转外部信息(operations返 回17时，返回)</td></tr></table></body></html>  

# 10.10 0810 – 发送文件材料接口(account.material.send)  

# 10.10.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/0</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>M</td><td>账户号</td></tr><tr><td>email</td><td>String</td><td>12 8</td><td>M</td><td>邮箱地址</td></tr><tr><td>materialType</td><td>String</td><td>2</td><td>M</td><td>文件材料类型 01-贷款凭证 02－结清证明</td></tr><tr><td>borrowOrderI d</td><td>String</td><td>36</td><td>C</td><td>03-还款明细 借款订单号 (微众必传)</td></tr><tr><td>DATA样例</td><td>{ "accountNo": "622578945618795128 8"</td><td></td><td></td><td></td></tr><tr><td></td><td colspan="4">}</td></tr></table></body></html>  

# 10.10.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr></table></body></html>  

<html><body><table><tr><td>DATA样例 { }</td><td colspan="2">"resultCode":"0", "resultDesc":"success"</td></tr><tr><td rowspan="2">返回码定义：</td><td colspan="2"></td></tr><tr><td>resultcod e</td><td>resultdesc</td></tr><tr><td rowspan="2"></td><td>0 success(成功)</td><td></td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td></td><td colspan="2"></td></tr></table></body></html>  

# 10.11 0811 – 通用资格查询（qualification.query）  

# 10.11.1 请求 data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>accountNo</td><td>String</td><td>128</td><td>M</td><td>借贷平台的用户账号</td></tr><tr><td>scene</td><td>String</td><td>32</td><td>M</td><td>场景：negotiatePrice—议价 borrowUsageUp一贷款用 途上传</td></tr></table></body></html>  

# 10.11.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/O</td><td>说明</td></tr><tr><td>resultCode</td><td> String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0: 表示失败</td></tr><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功 信息或失败原因</td></tr><tr><td>isSupport</td><td>String</td><td>1</td><td>0</td><td>不传：无资格 1:有资格</td></tr></table></body></html>  

<html><body><table><tr><td></td><td></td><td></td><td></td><td>0:无资格</td></tr></table></body></html>  

# 10.12 0812 – 活动数据上报（activity.data.report）  

# 10.12.1 请求data 数据结构  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/ 0</td><td>说明</td></tr><tr><td>infoDate</td><td>String</td><td>16</td><td>C</td><td>数据日期，格式：yyyy- MM-dd</td></tr><tr><td>intentionInfos</td><td>List<String></td><td>NA</td><td>M</td><td>内容</td></tr><tr><td>index</td><td>String</td><td>8</td><td>M</td><td>序号</td></tr></table></body></html>  

intentionInfos 中单个数据举例：  

{account_no}|{activity_code}|{is_activity_reach}|{attend_success_times}|{last_receive_time}| {has_interrupt_order}|{pt_d}  

字段解释：  

account_no：签约账户号  
activity_code：活动 id  
is_activity_reach：活动是否触达用户 (1--是 0--否)  
attend_success_times：成功参加活动次数  
last_receive_time：最后一次领取时间(yyyy-MM-dd HH:mm:ss)  
has_interrupt_order：是否有中断订单(1--有 0--无)  
pt_d: 数据日期(yyyy-MM-dd)  

# 10.12.2 响应消息  

<html><body><table><tr><td>参数名</td><td>类型</td><td>长度</td><td>M/0</td><td>说明</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0: 表示失败</td></tr></table></body></html>  

<html><body><table><tr><td>resultDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功 信息或失败原因</td></tr></table></body></html>  

# 10.13 0813 –文件材料开具查询接口(account.material.query)  

# 10.13.1 请求data 数据结构  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长 度</td><td>M/O</td><td>字段描述</td></tr><tr><td>accountNo</td><td>String</td><td>12 8</td><td>M</td><td>账户号</td></tr><tr><td>materialType</td><td>String</td><td>2</td><td>M</td><td>文件材料类型 01－贷款凭证 02－结清证明</td></tr><tr><td>borrowOrderI d</td><td>String</td><td>36</td><td>C</td><td>03-还款明细 借款订单号 (微众必传)</td></tr><tr><td>DATA样例</td><td>{ "accountNo": "622578945618795128 8" }</td><td></td><td></td><td></td></tr><tr><td></td><td colspan="4"></td></tr></table></body></html>  

# 10.13.2 响应消息  

<html><body><table><tr><td>参数列表</td><td>类型</td><td>长度</td><td>M/O</td><td>字段描述</td></tr><tr><td>resultCode</td><td>String</td><td>8</td><td>M</td><td>操作码0：表示成功，非0：表示失败</td></tr><tr><td>resuItDesc</td><td>String</td><td>256</td><td>0</td><td>返回结果描述，可以是成功信息或失败原因</td></tr><tr><td>url</td><td>String</td><td>2048</td><td>C</td><td>预览图片链接</td></tr><tr><td>remainTimes</td><td>String</td><td>10</td><td>C</td><td>邮件可发送次数：5|3 每日/月可发送5次，剩余3次 为空不限制</td></tr><tr><td>DATA样例</td><td colspan="4">{ "resultCode": "0", "resultDesc":"success"</td></tr><tr><td>返回码定义：</td><td colspan="4">}</td></tr></table></body></html>  

<html><body><table><tr><td rowspan="5"></td><td>resultcod resultdesc</td><td></td></tr><tr><td>e 0</td><td>success(成功)</td></tr><tr><td>E999999</td><td>服务器内部错误</td></tr><tr><td colspan="2"></td></tr></table></body></html>  
'''

ningyinjl_md = '''

'''