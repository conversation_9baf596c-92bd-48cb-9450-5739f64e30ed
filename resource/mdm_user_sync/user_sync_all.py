import hashlib
import sys
import os
import time
from datetime import datetime,timedelta
import requests
from domain.mdm_user import MdmUser
import json
from utils.MysqlConfig import MysqlConfig

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

def generate_signature(params, secret_key):
    # 对参数键进行排序
    sorted_keys = sorted(params.keys())
    # 拼接键值对
    concatenated_string = ''.join(f"{key}{params[key]}" for key in sorted_keys)
    # 拼接密钥
    concatenated_string += secret_key
    # 生成 MD5 签名
    md5_hash = hashlib.md5(concatenated_string.encode()).hexdigest().upper()
    return md5_hash


def query(pageNum):
     # 初始化参数
    params = {
        "timestamp": str(int(time.time() * 1000)),  # 当前时间戳（毫秒）
        "path": "/openapi/mdm/user/getMdmUserList",
        "version": "1.0.0"
    }
    secret_key = "5C092A7C8D3E4720B1580CE480317E14"  # 密钥
    # 生成签名
    sign = generate_signature(params, secret_key)

    # 构建请求头
    headers = {
        "timestamp": params["timestamp"],
        "appKey": "372D8689890B4EE5AC752F0255952F28",
        "sign": sign,
        "version": params["version"]
    }
    params['pageSize'] = 500
    params['pageNum'] = pageNum
    # 获取当前时间
    current_time = datetime.now()
    # 定义小时的时间差
    hour_sf = timedelta(minutes=65)
    # 计算半小时前的时间
    hour_ago = current_time - hour_sf
    # 格式化时间
    formatted_current_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
    params['beginTime']= '2014-01-01 00:00:00'
    params['endTime']= formatted_current_time
    result = send_get_request("https://openapi.sk.360shuke.com/openapi/mdm/user/getMdmUserList",params,headers)
    print(result)
    code = result['code']
    if code != 0:
        return []
    print("查询到符合条件用户数：" + str(result['total']))
    return result['data']

def main():
    pageNum = 1
    list = query(pageNum)
    mysqlUtil = MysqlConfig.jtlabelMysql()
    while (len(list) > 0) :
        mdmCodeList = []
        user_list = []
        exist_mdm_code_list = []
        for item in list:
            user_str = json.dumps(item, indent=4, ensure_ascii=False)
            mdm_user = MdmUser.parse_raw(user_str)
            user_list.append(mdm_user)
            mdmCodeList.append(mdm_user.mdmCode)
        print(user_list)
        if len(mdmCodeList) == 0:
            return
        sql = '''select mdm_code from mdm_user where is_delete = 0 and mdm_code in ('%s') '''  % (str("','".join(mdmCodeList)))
        print(sql)
        mdm_list = mysqlUtil.selectValues(sql)
        #mdm_list = []
        if mdm_list and len(mdm_list) > 0:
            for item in mdm_list:
                print(item[0])
                exist_mdm_code_list.append(item[0])
        insertSql = '''insert into mdm_user(mdm_code, dept_code, mdm_status, employee_id, un_list_employee_id, direct_superior_code, name, company_email, em_code, domain_account, org_path, company_name, workplace_name, dept_name, direct_superior, direct_superior_da, direct_superior_mdm_code, employ_status, dept_head1_mdm_code, bp_name, bp_mdm_code) 
        values(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)'''
        update_sql = '''update mdm_user set dept_code = '%s',mdm_status='%s',  employee_id='%s',  un_list_employee_id='%s',  direct_superior_code='%s',  name='%s',  company_email='%s',  em_code='%s',  domain_account='%s',  org_path='%s',  company_name='%s',  workplace_name='%s',  dept_name='%s',  direct_superior='%s',  direct_superior_da='%s',  direct_superior_mdm_code='%s',  employ_status='%s',  dept_head1_mdm_code='%s',  bp_name='%s',  bp_mdm_code='%s'
                where mdm_code = '%s'
            '''
        md_list = []
        for user in user_list:
            if user.mdmCode in exist_mdm_code_list:
                update = update_sql % (user.deptCode,user.mdmStatus,user.employeeId,user.unlistEmployeeId,user.directSuperiorCode,user.name,user.companyEmail,user.emCode,user.domainAccount,user.orgPath,user.companyName,user.workplaceName,user.deptName,user.directSuperior,user.directSuperiorDomainAccount,user.directSuperiorMdmCode,user.employStatus,user.depthead1MdmCode,user.bpName,user.bpMdmCode,user.mdmCode)
                print(update)
                mysqlUtil.executeSql(update)
            else:
                md_list.append((user.mdmCode,user.deptCode,user.mdmStatus,user.employeeId,user.unlistEmployeeId,user.directSuperiorCode,user.name,user.companyEmail,user.emCode,user.domainAccount,user.orgPath,user.companyName,user.workplaceName,user.deptName,user.directSuperior,user.directSuperiorDomainAccount,user.directSuperiorMdmCode,user.employStatus,user.depthead1MdmCode,user.bpName,user.bpMdmCode))
        if len(md_list) > 0: 
            mysqlUtil.insertValues(insertSql,md_list)
            print(md_list)
        pageNum += 1
        list = query(pageNum)
    mysqlUtil.closeDb()
          
        
    
def send_get_request(url, params, headers):
    """
    发送带有 JSON 参数和自定义头的 GET 请求
    :param url: 请求的 URL
    :param params: 请求参数（字典形式）
    :param headers: 请求头（字典形式）
    :return: 响应对象
    """
    try:
        print(params)
        # 发送 GET 请求
        response = requests.get(url, params=params, headers=headers)
        # 检查响应状态码
        response.raise_for_status()
        # 返回响应对象
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")
        return None

    

if __name__ == "__main__":
    try :
        main()
    except Exception as e:
        print(e)