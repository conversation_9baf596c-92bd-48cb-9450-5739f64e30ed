import json
from pydantic import BaseModel
from typing import List
from typing import Union, Optional

class MdmUser(BaseModel):
    mdmCode: Optional[str]
    deptCode: Optional[str]
    mdmStatus: Optional[str]
    employeeId: Optional[str]
    unlistEmployeeId: Optional[str]
    directSuperiorCode:Optional[str]
    name: Optional[str]
    companyEmail: Optional[str]
    emCode: Optional[str]
    domainAccount: Optional[str]
    orgPath: Optional[str]
    companyName: Optional[str]
    workplaceName: Optional[str]
    deptName: Optional[str]
    directSuperior: Optional[str]
    directSuperiorDomainAccount: Optional[str]
    directSuperiorMdmCode: Optional[str]
    employStatus: Optional[str]
    depthead1MdmCode: Optional[str]
    bpName: Optional[str]
    bpMdmCode: Optional[str]
    usingCategory: Optional[str]

    class Config:
        extra = "ignore"