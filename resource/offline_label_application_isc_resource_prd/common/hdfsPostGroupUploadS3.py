#!/usr/bin/python2.6
# -*- coding: utf-8 -*-
import json
import os
import sys

import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.s3ClientUtil import S3Util


class postHdfs2s3(object):
    '''
    获取hdfs上数据并且merge到本地数据
    '''

    def __init__(self):
        # self.postUrl="http://10.216.2.81:10081/v2api/combine/result"
        # 修改于 2021-10-14 19:19 原始值 self.postUrl="http://10.216.2.81:10081/v2api/combine/result"
        # self.postUrl = "http://stg1-gws-bps-web.daikuan.360.cn/api/gws/bps/gateway/iops/combine/result"
        # self.postUrl="http://10.216.2.78:10081/v2api/combine/result"
        self.postUrl="https://gws-bps.daikuan.360.cn/api/gws/bps/gateway/iops/combine/result"

    def post_labelsysHdfs_(self, localPathFile, crowdNo, template_days, label_type, crowdnum, fileName):
        headers = {'Content-Type': 'application/json'}
        data = {'crowdNo': '%s' % (crowdNo), 'status': 2, 'count': '%s' % (crowdnum), "filename": fileName}
        S3Util(localPathFile).uploadFile(fileName)

        r = requests.post(self.postUrl, data=json.dumps(data), headers=headers, verify=False).json()
        return r.get("msg")

    def reponseErro(self, crowdNo, status):
        headers = {'Content-Type': 'application/json'}
        data = {'status': int(status), 'crowdNo': '%s' % (crowdNo), 'count': '', 'filename': ''}
        print(data)
        r = requests.post(self.postUrl, data=json.dumps(data), headers=headers, verify=False).json()
        print(r)
        return r.get("msg")

    def returnReponseErroError(self, configFilePath):
        f = open(configFilePath)
        for line in f:
            crowdNo = line.split("@@@")[0]
            status = 3
            msg = self.reponseErro(crowdNo, status)
            print(msg)
            # data = {'status':status,'crowdNo':'%s'%(crowdNo),'count':'','filename':''}
            # r = requests.post(self.postUrl,data=data).json()
            # return  r.get("msg")


if __name__ == '__main__':
    configFilePath = sys.argv[1]
    f = postHdfs2s3().returnReponseErroError(configFilePath)
