#!/usr/bin/python
# -*- coding: utf-8 -*-
'''
-- file name     : ConfigMapping.py
-- author        : ca<PERSON>n<PERSON>
-- version       : v1.0
-- date          : 2020-12-31
-- copyright     : @qctx
-- function list : python读取数据生产配置文件.
-- history       : 第一版脚本，无历史
'''


class ConfigMapping(object):
      def __init__(self):
          pass

      def mappingNum(self,businessKey):
          businessJson={
               "jietiao":"'1','3','5'",
               "nicaifu":"4",
               "baoxian":"2",
               "xiaoweidai":"5"
          }
          return businessJson[businessKey]

