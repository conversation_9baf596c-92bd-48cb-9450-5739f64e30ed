#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'zhangyong'

'''
-- file name     : produceConfig.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-05-24
-- description   : 查询一体化资源位文件
'''
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from utils.MysqlConfig import MysqlConfig


class ProduceConfig(object):
    def __init__(self, local_dir,  config_file):
        self.localDir = local_dir
        self.configFile = config_file

    def produce(self):
        sql = '''
            select itc.task_no, itc.crowd_no,itc.crowd_file_ids  from  isc_task_crowd itc,isc_crowd ic   where itc.status  in(2,4) 
            and itc.callback_time >= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 12 HOUR),'%Y-%m-%d %H:%i:00')   and  itc.crowd_no  = ic.crowd_no 
            -- and itc.crowd_count >0
                 and ic.res = 1 and ic.is_delete = 0 and itc.is_delete=0
                 and not exists (
                  select 1 from  isc_resource_bit_record irbr where irbr.crowd_no = itc.crowd_no and irbr.task_no  = itc.task_no  and irbr.status  in(0,1) and is_delete =0
                 )

            UNION ALL

            SELECT
                b.crowd_task_no AS task_no,
                b.crowd_no,
                b.crowd_file_ids
            FROM
                isc_expt_task_crowd a
                JOIN isc_expt_task_crowd_file b ON a.task_no = b.task_no
                JOIN isc_expt_crowd c on c.crowd_no = b.crowd_no
            WHERE
                a.STATUS = 2
                AND a.callback_time >= DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 DAY ), '%Y-%m-%d %H:%i:00' )
                AND a.is_deleted = 0
                AND b.is_deleted = 0
                AND c.is_deleted = 0
                AND c.group_type in (11, 14)
                AND NOT EXISTS (
                    SELECT 1 FROM isc_resource_bit_record c
                    WHERE c.crowd_no = b.crowd_no AND c.task_no = b.crowd_task_no AND c.status IN ( 0, 1 ) AND c.is_delete = 0 )
        '''
        print(sql)
        mysql = MysqlConfig().integrationMysql()
        data = mysql.selectValues(sql)
        mysql.closeDb()
        print (data)
        if not data or len(data) == 0:
            return

        path = "%s/data/" % self.localDir
        self.openFilePath(path)
        with open("%s/%s" % (path, self.configFile), 'wt') as source_file:
            for task_no, crowd_no, file_id in data:
                    source_file.write(crowd_no + "@@@" + file_id + "@@@" + task_no + "\n")

    def openFilePath(self, path):
        if not os.path.exists(path):
            os.makedirs(path)


if __name__ == '__main__':
    local_path = sys.argv[1]
    config_file = sys.argv[2]
    # print datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # print datetime.datetime.strptime("2023-05-24 11:11:11", "%Y-%m-%d %H:%M:%S")
    # ProduceConfig("d:/data/log","config.file").produce()
    ProduceConfig(local_path, config_file).produce()
    # file_id = "GFS20bc72fc5a46415bad9ee3150dff8303,GFS24fcaee82cee4014b6197c5c585cb776,GFSf39d922d343d4a49b33b5be5e8611add"
    # files = file_id.split(",")

    # print len(files)
