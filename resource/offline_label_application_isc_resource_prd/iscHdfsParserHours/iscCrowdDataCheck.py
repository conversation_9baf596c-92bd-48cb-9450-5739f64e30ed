#!/usr/bin/python
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from common.RedisPoolManager import RedisPoolManager
from utils.sendSmsMail import SendMailSms
import multiprocessing
from utils.SinkLocalData2redis import SinkLocalData2Redis
from datetime import datetime, time, timedelta
from utils.MysqlConfig import MysqlConfig

CUR_CHECK_DATE_KEY = "resource_crowd_check_date_isc"

class CrowdDataCheck(object):

    def __init__(self, hdfs_file_path):
        self.hdfs_file_path = hdfs_file_path

    def check_data(self):
        print(self.hdfs_file_path)
        exec_start_time = datetime.now()
        # 查询待校验的人群包列表并多线程进行比对
        check_date_start, check_date_end = get_data_check_date()
        print("校验人群包开始, check_date_start=", check_date_start, "check_date_end=", check_date_end)
        # 一体化人群包
        isc_crowd_list = read_isc_crowd_check(check_date_start, check_date_end)
        self.multi_thread_check(isc_crowd_list)
        # 保存check_date_end
        set_data_check_date(check_date_end)
        diff = datetime.now() - exec_start_time
        print(f"check_data method execution time: {diff.total_seconds()/60:.0f} minutes")

    def multi_thread_check(self, crowd_list):
        if crowd_list is None or len(crowd_list) == 0:
            print("待校验的人群包数量为0, 任务结束")
            return
        print("待校验的人群包数量=", len(crowd_list))
        pool = multiprocessing.Pool(processes=3)
        for crowd_no, bag_version in crowd_list:
            pool.apply_async(self.run_check, args=(crowd_no, bag_version))
        pool.close()
        pool.join()

    def run_check(self, crowd_no, bag_version):
        exec_start_time = datetime.now()
        crowd_file_dir = self.hdfs_file_path + "/rule_no=" + crowd_no
        print("校验人群包开始, 人群包=", crowd_no, "版本号=", bag_version, "人群包文件目录=", crowd_file_dir)
        command_ls = ["hadoop", "fs", "-ls", crowd_file_dir]
        count = 0
        try:
            process_ls = subprocess.Popen(command_ls, stdout=subprocess.PIPE)
            while True:
                # 获取目录下所有文件
                line_ls = process_ls.stdout.readline()
                if not line_ls:
                    break
                line_ls = line_ls.decode().strip()
                crowd_file_path = line_ls.split()[-1]
                if not crowd_file_path.startswith("hdfs"):
                    continue
                print("人群包=", crowd_no, "文件路径=", crowd_file_path)
                command_cat = ["hadoop", "fs", "-cat", crowd_file_path]
                process_cat = subprocess.Popen(command_cat, stdout=subprocess.PIPE)
                keys_and_fields = []
                # 获取文件里所有内容(用户号),拼装成redis的key
                while True:
                    line_cat = process_cat.stdout.readline()
                    if not line_cat:
                        break
                    user_no = line_cat.decode().strip()
                    # print("序号=", count, "人群包=", crowd_no, "用户号=", user_no)
                    keys_and_fields.append(("ISC_" + user_no, crowd_no))
                    count += 1
                # 根据拼装的key获取redis中的值进行比对
                with RedisPoolManager.redis_pool_instance.connect().pipeline(transaction=False) as p:
                    for key, field in keys_and_fields:
                        p.hget(key, field)
                    results = p.execute()
                    for (key, field), value in zip(keys_and_fields, results):
                        redis_version = value.decode() if value is not None else 0
                        if str(bag_version) == str(redis_version):
                            continue
                        # 存在max版本号大于当前版本号 continue
                        max_bag_version = read_isc_crowd_max_bag_version(crowd_no)
                        if int(max_bag_version) > int(bag_version) and int(max_bag_version) == int(redis_version):
                            print("版本号有变更, 人群包=", crowd_no, "bag_version=", bag_version,
                                  "redis_version=",redis_version, "max_bag_version=", max_bag_version)
                            break
                        msg = f"资源位-普通人群包, 监控异常, key_user_no={key}, crowd_no={field}, bag_version={bag_version}, redis_version={redis_version}"
                        print(msg)
                        SendMailSms(msg).notice_teams()
                        break
            msg = f"校验人群包结束, 人群包={crowd_no}, 版本号={bag_version}, 人群包文件目录={crowd_file_dir}, 校验条数={count}"
            print(msg)
        except Exception as e:
            str_e = str(e)
            msg = f"资源位-普通人群包, 监控异常(请求失败异常)={str_e}"
            print(msg)
            SendMailSms(msg).notice_teams()
        diff = datetime.now() - exec_start_time
        print(f"run_check method execution time: {diff.total_seconds():.0f} seconds")

def get_data_check_date():
    current_time = datetime.now()
    start_of_day = datetime.combine(current_time.date(), time.min).strftime("%Y%m%d%H%M%S")
    check_date_start = None
    redis_value = SinkLocalData2Redis.get(CUR_CHECK_DATE_KEY)
    if redis_value is None or int(redis_value.decode()) < int(start_of_day):
        # redis中为空,或者redis值小于当天开始时间,设置redis中的值为当天开始时间
        SinkLocalData2Redis.set(CUR_CHECK_DATE_KEY, start_of_day)
        check_date_start = start_of_day
    else:
        # redis值正常,设置开始时间为redis中的值
        check_date_start = redis_value.decode()
    two_hours_ago = current_time - timedelta(hours=2)
    check_date_end = two_hours_ago.replace(minute=0, second=0).strftime("%Y%m%d%H%M%S")
    return check_date_start, check_date_end

def set_data_check_date(check_date_end):
    SinkLocalData2Redis.set(CUR_CHECK_DATE_KEY, check_date_end)

def read_isc_crowd_check(check_date_start, check_date_end):
    query_sql = """
                SELECT
                  t1.crowd_no,
                  t1.version as bag_version
                FROM
                  ipss.isc_resource_bit_record t1
                  INNER JOIN (
                    SELECT
                      crowd_no,
                      MAX(id) AS id
                    FROM
                      ipss.isc_resource_bit_record
                    where
                      status = 1
                      AND DATE(date_created) = DATE(NOW())
                    GROUP BY
                      crowd_no
                  ) t2 ON t1.crowd_no = t2.crowd_no
                  AND t1.id = t2.id
                  AND t1.date_created >= {start_date}
                  AND t1.date_created < {end_date}
                """.format(start_date=check_date_start, end_date=check_date_end)
    print("read_isc_crowd_check =", query_sql)
    mysql = MysqlConfig.integrationMysql()
    results = mysql.selectValues(query_sql)
    mysql.closeDb()
    print(results)
    if len(results) == 0:
        return []
    return results

def read_isc_crowd_max_bag_version(crowd_no):
    query_sql = """
                SELECT
                  max(version)
                FROM
                  ipss.isc_resource_bit_record t1
                WHERE date_created >= current_date
                AND crowd_no = '{crowd_no}'
                """.format(crowd_no=crowd_no)
    print("read_isc_crowd_max_bag_version =", query_sql)
    mysql = MysqlConfig.integrationMysql()
    results = mysql.selectValues(query_sql)
    mysql.closeDb()
    print(results)
    if len(results) == 0:
        return 0
    return results[0][0]

if __name__ == '__main__':
    hdfs_file_path = sys.argv[1]
    CrowdDataCheck(hdfs_file_path).check_data()
