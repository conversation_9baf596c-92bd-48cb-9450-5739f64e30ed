#!/usr/bin/python
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from common.RedisPoolManager import RedisPoolManager
from utils.sendSmsMail import SendMailSms
import multiprocessing
from utils.SinkLocalData2redis import SinkLocalData2Redis
from datetime import datetime, time, timedelta
from utils.MysqlConfig import MysqlConfig

class ExpCrowdResourceDel(object):

    def __init__(self, hdfs_file_path):
        self.hdfs_file_path = hdfs_file_path

    def delete_resource(self):
        print("delete_resource start, day_file_dir=", self.hdfs_file_path)
        exec_start_time = datetime.now()
        path_crowd_no = ""
        try:
            # 清理资源位信息
            # hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/strategy_expt_person_data_clean/pday=20240609
            day_file_dir = self.hdfs_file_path
            command_ls_day = ["hadoop", "fs", "-ls", day_file_dir]
            process_ls = subprocess.Popen(command_ls_day, stdout=subprocess.PIPE)
            while True:
                # 循环获取目录下子目录(人群包目录)
                line_ls_crowd_no = process_ls.stdout.readline()
                if not line_ls_crowd_no:
                    break
                line_ls_crowd_no = line_ls_crowd_no.decode().strip()
                crowd_no_path = line_ls_crowd_no.split()[-1]
                if not crowd_no_path.startswith("hdfs"):
                    continue
                path_crowd_no = crowd_no_path.split("=")[-1]
                # hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/strategy_expt_person_data_clean/pday=20240609/crowd_no=C036744
                print("清理人群包开始, crowd_no_path=", crowd_no_path, " 人群包=", path_crowd_no)
                command_ls_file = ["hadoop", "fs", "-ls", crowd_no_path]
                process_ls_file = subprocess.Popen(command_ls_file, stdout=subprocess.PIPE)
                # 循环获取子目录(人群包目录)下文件
                while True:
                    line_ls_file = process_ls_file.stdout.readline()
                    if not line_ls_file:
                        break
                    line_ls_file = line_ls_file.decode().strip()
                    crowd_file_path = line_ls_file.split()[-1]
                    if not crowd_file_path.startswith("hdfs"):
                        continue
                    # hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/strategy_expt_person_data_clean/pday=20240609/crowd_no=C036744/part-00000-2fddcf96-51e4-4efd-b9b6-534e2508b746.c000
                    print("清理人群包下文件开始, crowd_file_path=", crowd_file_path)
                    command_cat = ["hadoop", "fs", "-cat", crowd_file_path]
                    process_cat = subprocess.Popen(command_cat, stdout=subprocess.PIPE)
                    # 获取文件中的内容并删除资源位数据(user_no、crowd_no)
                    batch_size = 100000
                    count = 0
                    with RedisPoolManager.redis_pool_instance.connect().pipeline(transaction=False) as p:
                        while True:
                            line_cat = process_cat.stdout.readline()
                            if not line_cat:
                                break
                            line_content = line_cat.decode().strip()
                            user_no = line_content
                            count = count + 1
                            # print("key=", "ISC_" + user_no, " c=", path_crowd_no)
                            p.hdel("ISC_" + user_no, path_crowd_no)
                            if not count % batch_size:
                                p.execute()
                                print("执行删除操作,count=", count)
                                count = 0
                        p.execute()
                        print("执行最后一次删除操作,count=", count)
                # 删除人群包版本信息
                print("删除人群包版本信息,crowd_no=", path_crowd_no)
                redis = RedisPoolManager.redis_pool_instance.connect()
                result = redis.delete(path_crowd_no)
                print("删除人群包版本信息,result=", result)
        except Exception as e:
            str_e = str(e)
            msg = f"一体化实验,资源位数据清理(请求失败异常)={str_e},crowd_no={path_crowd_no}"
            print(msg)
            SendMailSms(msg).notice_teams()
            sys.exit(1)
        diff = datetime.now() - exec_start_time
        print(f"delete_resource method execution time: {diff.total_seconds()/60:.0f} minutes")

if __name__ == '__main__':
    hdfs_file_path = sys.argv[1]
    ExpCrowdResourceDel(hdfs_file_path).delete_resource()
