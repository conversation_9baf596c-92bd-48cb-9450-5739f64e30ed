#!/usr/bin/python
# -*- coding: utf-8 -*-
'''
-- file name     : MainGroupCrowd.py
-- author        : caoyanwei
-- version       : v1.0
-- date          : 2020-12-31
-- copyright     : @qctx
-- function list : python读取数据生产配置文件.
-- history       : 第一版脚本，无历史
'''
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
import json
import subprocess
from hdfsCrowdGroupLabel.model.combSqlQuery import MysqlQuery
from hdfsCrowdGroupLabel.model.combConfigMapping import ConfigMapping
from hdfsCrowdGroupLabel.model.combCrowdParser import CombParser
from hdfsCrowdGroupLabel.model.filePathChange import WriteDataFile
from utils.s3ClientUtil import S3Util
import os


class GroupCrowdTypeData(object):
    def __init__(self, business_type, read_env, config_path, config_file, group_config_file, parent_hdfs_dir,
                 parent_script_dir, local_file_dir):
        self.business_type = business_type
        self.readEvn = read_env
        self.configPath = config_path
        self.configFile = config_file
        self.groupConfigFile = group_config_file
        self.parentHdfsDir = parent_hdfs_dir
        self.parentScriptDir = parent_script_dir
        self.localFileDir = local_file_dir

    def mainGroupCrowd(self):
        """
         根据业务线获取参数
        :return:
        """
        businessValue = ConfigMapping().mappingNum(self.business_type)
        select = MysqlQuery().groupSqlSelect(businessValue)
        dataJsonList = []
        ##查询出组合人群包
        if len(select) > 0:
            resultNumber = []
            for number, bag_json, bag_class, bag_type in select:
                ##组合人群包迭代有两种，需要兼容之前的数据需求
                cobJson = json.loads(json.loads(json.dumps(bag_json)))
                dataNum = self.groupParser(cobJson)
                resultNumber += dataNum
                dataJsonList.append(number + "@@@" + bag_json)

            ruleDataList = []
            if len(resultNumber) > 0:
                for num in list(set(resultNumber)):
                    select = MysqlQuery().crowdPerson(num)
                    if len(select) > 0:
                        for number, bag_json, bag_type, upload_file_name in select:
                            if bag_type == 2:
                                self.downLoadFileUploadHdfs(select)
                            elif bag_type == 1:
                                ruleDataList.append("%s@@@%s" % (number, bag_json))
                if len(ruleDataList) > 0:
                    self.writeConfigFile(ruleDataList, self.configFile)

                self.writeConfigFile(dataJsonList, self.groupConfigFile)





        else:
            print ("未查询到需要组合的人群包数据,程序正常退出")

    def groupParser(self, cobJson):
        '''
        根据查询的结果进行解析json:
         1、如果是新的人群组合就走新的支持10个人群包的人群解析方法
         2、如果是旧的按照旧的处理方式进行
        :return:
        '''
        if any("new_crowd" in key for key in cobJson.keys()):
            crowdList = CombParser(cobJson).ParserNewRule()
        else:
            crowdList = [CombParser(cobJson).ParserOldRule()]

        dataNumList = []
        for number in crowdList:
            for num in number:
                dataNumList.append(num)

        return dataNumList

    def downLoadFileUploadHdfs(self, selectData):
        '''
          类型等于2的下载数据进行上传至hdfs
        :param selectData:
        :return:
        '''
        print("downLoadFileUploadHdfs")
        for number, bag_json, bag_class, upload_file_name in selectData:
            downFilePath = self.localFileDir + "/" + "s3DowloadData" + "/" + number + "/"
            WriteDataFile().mkdir_crowd_dir(downFilePath)
            downMergeFilePath = self.localFileDir + "/" + "mergeCrowdData" + "/" + number + "/"
            WriteDataFile().mkdir_crowd_dir(downMergeFilePath)
            rmlocalfileCmd = '''rm -rf %s; rm -rf %s''' % (downFilePath, downMergeFilePath)

            ##下载s3文件
            S3Util(upload_file_name).downloadFilePath(downFilePath)
            WriteDataFile().dos_to_unix(downFilePath + upload_file_name)

            fileCommand = '''cd %s;sed -i '1d' %s;sed -e 's/^M/\n/g' %s;cat %s|awk '{sub("^ *","");sub(" *$","");print}'|awk '$0=$0"\t%s"'>%s''' % (
                downFilePath, upload_file_name, downFilePath + upload_file_name, downFilePath + upload_file_name,
                number,
                downMergeFilePath + upload_file_name)
            print("fileCommand=" + fileCommand)
            status, output = subprocess.getstatusoutput(fileCommand)
            if status == 0:
                hdfsParentFilePath = self.parentHdfsDir

                rmfile = '''hadoop fs -rm -r -skipTrash %s*/%s ''' % (hdfsParentFilePath, upload_file_name)
                print(rmfile)
                subprocess.getstatusoutput(rmfile)

                hdfsFilePath =hdfsParentFilePath+"/"+number

                status, output = subprocess.getstatusoutput('''hadoop fs -test -e  %s''' % hdfsFilePath)
                if status != 0:
                    mkdirParentDirCommand = "hadoop fs -mkdir -p %s" % hdfsFilePath
                    subprocess.getstatusoutput(mkdirParentDirCommand)

                hadoopPutCommand = '''cd %s;hadoop fs -put -f %s  %s/%s''' % (
                    downMergeFilePath, upload_file_name, hdfsFilePath, upload_file_name)
                print(hadoopPutCommand)
                status, output = subprocess.getstatusoutput(hadoopPutCommand)
                print(output)
                print(rmlocalfileCmd)
                status,output = subprocess.getstatusoutput(rmlocalfileCmd)


    def writeConfigFile(self, select, configfile):
        '''
         将对应的文件写入配置中
        :param select:
        :param configfile:
        :return:
        '''
        if len(select) > 0:
            fileAddPathName = self.configPath + configfile
            if not os.path.exists(self.configPath):
                os.makedirs(self.configPath)
            fwrite = open(fileAddPathName, "w+", encoding="utf-8")
            for mysqlData in select:
                fwrite.write(mysqlData + "\n")
            fwrite.close()


if __name__ == '__main__':
    print("mainCombCrowd start ")
    business_type = sys.argv[1]
    readEvn = sys.argv[2]
    configPath = sys.argv[3]
    configFile = sys.argv[4]
    groupConfigFile = sys.argv[5]
    parentHdfsDir = sys.argv[6]
    parentScriptDir = sys.argv[7]
    localFileDir = sys.argv[8]
    GroupCrowdTypeData(business_type, readEvn, configPath, configFile, groupConfigFile, parentHdfsDir,
                       parentScriptDir, localFileDir).mainGroupCrowd()
