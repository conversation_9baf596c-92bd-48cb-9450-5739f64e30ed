#!/usr/bin/python
# -*- coding: utf-8 -*-
'''
-- file name     : sqlQuery.py
-- author        : caoyanwei
-- version       : v1.0
-- date          : 2020-12-31
-- copyright     : @qctx
-- function list : sql查询方式
-- history       : 第一版脚本，无历史
'''
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from utils.MysqlConfig import MysqlConfig

class MysqlQuery(object):
      def __init__(self):
          pass

      def selectPathConfig(self,business_type,dispose_type,evn_type):
          sql='''
          select  business_type
                 ,dispose_type
                 ,dispose_name
                 ,input_hdfs_path
                 ,input_hdfs_success_tag
                 ,output_hdfs_path
                 ,evn_type
                 ,flag_usage
              from fa_label_job_conf_info where business_type='%s'
              and dispose_type='%s' and evn_type='%s' and flag_usage=1
          ''' %(business_type,dispose_type,evn_type)
          mysql=MysqlConfig().jtlabelMysql()
          select= mysql.selectValues(sql)
          mysql.closeDb()
          return select


      def groupSqlSelect(self,bag_class_list):
          '''
          查询出哪些需要分组
          :return:
          '''
          sql='''
          select
              number,bag_json,bag_class,bag_type
           from fa_bag_info a
             where a.update_time>=DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 5 MINUTE),'%%Y-%%m-%%d %%H:%%i:00')
                     and a.update_time<=DATE_FORMAT(DATE_SUB(NOW(),INTERVAL  1 MINUTE),'%%Y-%%m-%%d %%H:%%i:59')
                      and status=1
                     and  combine_status=0 and bag_class in (%s) and btype=6 and data_mechanism=1
             ''' %(str(bag_class_list))
          # sql='''
          #     select
          #       number,bag_json,bag_class,bag_type
          #       from fa_bag_info a
          #      where DATE_FORMAT(a.update_time,'%%Y-%%m-%%d') =CURDATE()
          #               and status=1
          #               and  combine_status=0 and bag_class in (%s)  and btype=6 and data_mechanism=1
          # ''' %(str(bag_class_list))
          print (sql)
          mysql=MysqlConfig().jtlabelMysql()
          select= mysql.selectValues(sql)
          mysql.closeDb()
          return select

      def crowdPerson(self,number):
          '''
           查询对应的类型进行数据处理
          :param number:
          :return:
          '''
          sql='''
                 select  number,bag_json,bag_type,upload_file_name  from fa_bag_info a
                               left join fa_bag_upload b 
                               on a.id=b.bag_id
                 where a.number ='%s' and a.status=1 and a.valid =1 and data_mechanism=1
               ''' %(number)
          print (sql)
          mysql=MysqlConfig().jtlabelMysql()
          select= mysql.selectValues(sql)
          mysql.closeDb()
          return select







