#!/usr/bin/python
#coding:utf-8
"""
-- file name     : sendSmsMail.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2019-02-25
-- copyright     : @qctx
-- description   : 短信发送邮件发送
-- usage         : 告警处理
-- function list : 告警发送
-- history       : 第一版脚本，无历史
"""
import json
import sys
import traceback

import requests
import configparser
import os

class  SendMailSms(object):
    def __init__(self,content,):
        self.content=content
        self.cf=configparser.ConfigParser()
        self.confPath= os.path.realpath(__file__)
        self.confDir= os.path.dirname(self.confPath)
        self.read=self.cf.read(self.confDir + "/config/db.conf")
        self.sendMainSwitch = self.cf.getboolean("emailConfig","switch")
        self.appKey = self.cf.get("emailConfig","appkey")
        self.appSecret = self.cf.get("emailConfig","appsecret")
        self.url = self.cf.get("emailConfig","url")


    def sendMail(self,appdata):
        if not self.sendMainSwitch :
            print ("发送邮件开关已关闭")
            return
        appkey= self.appKey
        appsecret= self.appSecret
        url=self.url
        response = requests.post(url, data=appdata, auth=(appkey, appsecret))

    def getAppData(self):
        teams="digital_intelligence_email"
        title="360标签系统服务告警"
        appdata={"teams":teams,"title":title,"content":self.content,"app_content":self.content}
        self.sendMail(appdata)

    def notice_teams(self):
        try:
            appdata={"msgtype": "text", "text": {"content": self.content},"at": {"isAtAll": "true", "userIds": [] }}
            alarm_url = "https://im.360teams.com/api/qfin-api/rce-app/robot/send?access_token=0928611ca7964f91b622ce1fb805ec593be249de99834d9eaa372990241ad265"
            result = requests.post(alarm_url,json=appdata)
            print( result)
        except Exception as e:
            print(e)
            traceback.print_exc()
        try:
            self.voice_notice()
        except Exception as e:
            print(e)
            traceback.print_exc()
            sys.exit(1)

    def voice_notice(self):
        mobile_list = "13764753037,18916512909,15221280416,18217412510"
        headers = {'Content-Type': 'application/json'}
        data = {"content": self.content, "alarm_list": mobile_list, "invalidSecond": 10}
        safeLyccPostUrl = "http://10.160.132.158:5000/tomq_by_voice"
        r = requests.post(safeLyccPostUrl, data=json.dumps(data), headers=headers, verify=False).json()
        print(json.dumps(r, ensure_ascii=False))

if __name__ == '__main__':
    SendMailSms("测试数据").notice_teams()