#!/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import os
import sys
import time

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)
import requests


def requestCrowdRuleParse(dataJson):
    headers = {'Content-Type': 'application/json'}
    # url = "http://gws-bps.daikuan.360.cn/ucs/getCrowdRuleSql"
    url = "http://stg3-gws-bps-web.daikuan.360.cn/ucs/getCrowdRuleSql"
    for i in range(4):
        try:
            res = requests.post(url, data=json.dumps(dataJson), headers=headers, verify=False).json()
            # print(res)
            if res["status"] == '200':
                return res["data"]
            else:
                raise Exception("请求人群包规则解析服务异常")
        except Exception as e:
            print(e)
            time.sleep(10)
            if i == 3:
                sys.exit(1)


def requestDelayCrowdRule(dataJson):
    headers = {'Content-Type': 'application/json'}
    # url = "http://gws-bps.daikuan.360.cn/ucs/getCrowdRuleSql"
    url = "http://stg3-gws-bps-web.daikuan.360.cn/ucs/getSqlResourceBit"
    for i in range(4):
        try:
            res = requests.post(url, data=json.dumps(dataJson), headers=headers, verify=False).json()
            # print(res)
            if res["status"] == '200':
                return res["data"]
            else:
                raise Exception("请求人群包规则解析服务异常")
        except Exception as e:
            print(e)
            time.sleep(10)
            if i == 3:
                sys.exit(1)
