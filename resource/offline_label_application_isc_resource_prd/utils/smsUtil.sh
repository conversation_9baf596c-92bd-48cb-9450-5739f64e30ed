#!/bin/bash
##  file name     : smsUtil.sh
##  author        : caoyanwei
##  version       : v1.0
##  date          : 2018-10-18
##  copyright     : @qctx
##  description   : 短信发送工具
##  usage         : 短信报警所用
##  function list :
##  history       : 第一版脚本，无历史
# set -e

send_sms(){

      url="http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"

      appkey="creditdcplat"
      appsecret="95CDEAF62D4BFBFF0A0BB20F12A36A71"

      teams="digital_intelligence_sms" #接受报警的组,多个逗号分隔
      title="智能运营平台数据服务报警" #标题
      content="[标签系统]$1"
      app_content="[标签系统告警]$1"

      echo `curl -X POST ${url} -u ${appkey}:${appsecret} -d "teams=${teams}&title=${title}&content=${content}&app_content=${app_content}"`
}






send_sms_01(){

      url="http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"

      appkey="creditdcplat"
      appsecret="95CDEAF62D4BFBFF0A0BB20F12A36A71"

      teams="digital_intelligence_sms" #接受报警的组,多个逗号分隔
      title="奇富科技数据任务监控报警" #标题
      content="[数据处理]$1"
      app_content="[数据处理]$1"

      echo `curl -X POST ${url} -u ${appkey}:${appsecret} -d "teams=${teams}&title=${title}&content=${content}&app_content=${app_content}"`
}


send_sms_02(){

      url="http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"

      appkey="creditdcplat"
      appsecret="95CDEAF62D4BFBFF0A0BB20F12A36A71"

      teams="digital_intelligence_sms" #接受报警的组,多个逗号分隔
      title="奇富科技数据任务监控报警" #标题
      content="[保险标签数据]$1"
      app_content="[保险标签数据]$1"

      echo `curl -X POST ${url} -u ${appkey}:${appsecret} -d "teams=${teams}&title=${title}&content=${content}&app_content=${app_content}"`
}


send_sms_03(){

    url="http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"

    appkey="creditdcplat"
    appsecret="95CDEAF62D4BFBFF0A0BB20F12A36A71"

    teams="digital_intelligence_sms" #接受报警的组,多个逗号分隔
    title="奇富科技数据任务监控报警" #标题
    content="[你财富标签数据]$1"
    app_content="[你财富标签数据]$1"

    echo `curl -X POST ${url} -u ${appkey}:${appsecret} -d "teams=${teams}&title=${title}&content=${content}&app_content=${app_content}"`
}