#!/usr/bin/python3
# -*- coding: utf-8 -*-
__author__ = 'z<PERSON><PERSON><PERSON><PERSON>'

import traceback
from importlib import reload

'''
-- file name     : dorisUtil.py
-- author        : zhou<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2023-03-28
-- copyright     : @qctx
-- function list : doris使用
-- history       : 第一版脚本，无历史
'''
import os
import sys
import subprocess
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)
from utils.sendSmsMail import SendMailSms

reload(sys)


def mainDorisUtil(localFilePath, localFileName, inputHdfs, businessType, number):
    hdfsDataPath = inputHdfs + "/" + businessType + "/"
    count = len(open(localFilePath + localFileName, 'r').readlines())
    if count > 0:
        putLocalHdfsData(localFilePath, localFileName, hdfsDataPath, number)


def putLocalHdfsData(localFilePath, localFileName, hdfsDataPath, number):
    try:
        print("put local data to hdfs =" + hdfsDataPath)
        # parentfilePath = "/".join(hdfsDataPath.split('/')[:len(hdfsDataPath.split('/')) - 2])
        rmfile = '''hadoop fs -rm -r -skipTrash %s%s ''' % (hdfsDataPath, localFileName)
        print("rmfile=" + rmfile)
        status, output = subprocess.getstatusoutput(rmfile)
        print("rmfile=" + rmfile + " status=" + str(status) + " output=" + str(output))
        ##按照人群包前缀删除
        # rmfile='''hadoop fs -rm -r -skipTrash %s/*/%s* ''' %(parentfilePath,number)
        # commands.getstatusoutput(rmfile)
        checkDirectoryCommand = "hadoop fs -test -e %s" % hdfsDataPath
        print("checkDirectoryCommand=" + checkDirectoryCommand)
        status, output = subprocess.getstatusoutput(checkDirectoryCommand)
        print("checkDirectoryCommand=" + checkDirectoryCommand + " status=" + str(status) + " output=" + str(
            output))
        if status != 0:
            mkdirParentDirCommand = "hadoop fs -mkdir -p %s" % hdfsDataPath
            status, output = subprocess.getstatusoutput(mkdirParentDirCommand)
            print("mkdirParentDirCommand=" + mkdirParentDirCommand + " status=" + str(status) + " output=" + str(
                output))

        hadoopPutCommand = '''cd %s;hadoop fs -put -f %s %s%s''' % (
            localFilePath, localFileName, hdfsDataPath, localFileName)

        print("hadoopPutCommand=" + hadoopPutCommand)
        status, output = subprocess.getstatusoutput(hadoopPutCommand)
        if status != 0:
            content = "资源位-Doris下载数据删除HDFS失败,失败原因%s" % (output)
            print(content)
            SendMailSms(content).notice_teams()
        rmlocalfile = "rm -rf %s%s" % (localFilePath, localFileName)
        print("rmlocalfile=" + rmlocalfile)
        status, output = subprocess.getstatusoutput(rmlocalfile)
    except Exception as e:
        traceback.print_exc()
        content = "资源位-人群包%s,Doris下载数据删除HDFS失败,失败原因%s" % (number, e)
        SendMailSms(content).notice_teams()
        print("Doris下载数据删除HDFS失败，重试执行~")
        print(e)
