#!/bin/python

# -*- utf8 -*-
import os
import subprocess
import traceback

from utils.sendSmsMail import SendMailSms


def mkdir(file_path):
    if os.path.exists(file_path):
        return file_path
    os.makedirs(file_path, exist_ok=True)
    return file_path


def command_rmr(filepath):
    rmr = "rm -rf %s" % filepath
    print("rmr=%s" % rmr)
    status, output = subprocess.getstatusoutput(rmr)
    print("rmr=%s status=%s  output=%s" % (rmr, status, output))


def rmr_file(filepath, filename):
    rmr_file_command = '''rm -f %s/%s''' % (filepath, filename)
    print(rmr_file_command)
    status, output = subprocess.getstatusoutput(rmr_file_command)
    print(status, output)
    file = filepath + filename
    if os.path.exists(file) and os.path.isfile(file):
        os.remove(file)

    # 判断文件夹中是否有文件，不存在文件时，删除父类文件夹
    if os.path.exists(filepath) and len(os.listdir(filepath)) == 0:
        try:
            os.rmdir(filepath)
        except Exception as e:
            traceback.print_stack()


def check_hdfs_path(hdfs_path):
    check_directory_command = "hadoop fs -test -e %s" % hdfs_path
    print("checkDirectoryCommand=" + check_directory_command)
    status, output = subprocess.getstatusoutput(check_directory_command)
    print("checkDirectoryCommand=" + check_directory_command + " status=" + str(status) + " output=" + str(
        output))
    if status != 0:
        mkdir_parent_dir_command = "hadoop fs -mkdir -p %s" % hdfs_path
        status, output = subprocess.getstatusoutput(mkdir_parent_dir_command)
        print("mkdir_parent_dir_command=" + mkdir_parent_dir_command + " status=" + str(status) + " output=" + str(
            output))
    return hdfs_path


def put_to_hdfs(hdfs_path, local_path, file_id, crowd_no, if_del_local=False):
    """
      上送非s3下载的文件到hdfs
      :param if_del_local:是否删除本地文件
      :param hdfs_path:  hdfsPath
      :param local_path: 本地文件路径
      :param file_id:    文件名称
      :param crowd_no:   人群包编号
      :return:
      """
    count = len(open(local_path + file_id, 'r').readlines())
    if count == 0:
        return

    try:
        print("put local data to hdfs =" + hdfs_path)
        # rm_file = '''hadoop fs -rm -r -skipTrash %s%s ''' % (hdfs_path, file_id)
        # print("rm_file=" + rm_file)
        # status, output = subprocess.getstatusoutput(rm_file)
        # print("rm_file=" + rm_file + " status=" + str(status) + " output=" + str(output))
        # 按照人群包前缀删除,这里删除所有defined子文件夹下的所有符合人群包的文件
        rm_file = '''hadoop fs -rm -r -skipTrash %s%s* ''' % (hdfs_path, crowd_no)
        print("rm_file=" + rm_file)
        status, output = subprocess.getstatusoutput(rm_file)
        print("rm_file=" + rm_file + " status=" + str(status) + " output=" + str(output))

        hadoop_put_command = '''cd %s;hadoop fs -put -f %s %s%s''' % (
            local_path, file_id, hdfs_path, file_id)

        print("hadoopPutCommand=" + hadoop_put_command)

        count = 0
        while count < 3:
            status, output = subprocess.getstatusoutput(hadoop_put_command)
            print("count=" + str(count) + " hadoopPutCommand= " + hadoop_put_command + " status=" + str(
                status) + " output=" + output)

            if status == 0:
                break

            count += 1

        if count >= 3:
            SendMailSms("资源位-Doris下载数据上送HDFS,重试3次失败,template_file=" + local_path + "/" + file_id).notice_teams()

        if if_del_local:
            rmr_file(local_path, file_id)
    except Exception as e:
        traceback.print_exc()
        content = "资源位-人群包%s,Doris下载数据删除HDFS失败,失败原因%s" % (crowd_no, e)
        SendMailSms(content).notice_teams()
        print("Doris下载数据删除HDFS失败，重试执行~")
        print(e)


def puts3_to_hdfs(hdfs_path, local_path, file_id, crowd_no, if_del_local=False):
    """
    上送s3下载的文件到hdfs
    :param if_del_local:是否删除本地文件
    :param hdfs_path:  hdfsPath
    :param local_path: 本地文件路径
    :param file_id:    文件名称
    :param crowd_no:   人群包编号
    :return:
    """
    file_path = "/".join(hdfs_path.split('/')[:len(hdfs_path.split('/')) - 2])
    # 按照文件名前缀删除,这里删除所有defined子文件夹下的所有符合文件名的文件
    rm_file = '''hadoop fs -rm -r %s/*/%s ''' % (file_path, file_id)
    print("rm_file= " + rm_file)
    subprocess.getstatusoutput(rm_file)
    # 按照人群包前缀删除,这里删除所有defined子文件夹下的所有符合人群包的文件
    rm_file = '''hadoop fs -rm -r  %s/*/%s* ''' % (file_path, crowd_no)
    print("rm_file= " + rm_file)
    subprocess.getstatusoutput(rm_file)
    status, output = subprocess.getstatusoutput('''hadoop fs -test -e  %s''' % hdfs_path)
    if status != 0:
        mkdirParentDirCommand = "hadoop fs -mkdir -p %s" % hdfs_path
        subprocess.getstatusoutput(mkdirParentDirCommand)

    hadoopPutCommand = '''cd %s;hadoop fs -put -f %s %s%s''' % (
        local_path, file_id, hdfs_path, crowd_no+"_"+file_id)
    status, output = subprocess.getstatusoutput(hadoopPutCommand)
    print("hadoopPutCommand= " + hadoopPutCommand + " status=" + str(status) + " ouput=" + output)
    count = 0
    while count < 4:
        status, output = subprocess.getstatusoutput(hadoopPutCommand)
        print("count=" + str(count) + " hadoopPutCommand= " + hadoopPutCommand + " status=" + str(
            status) + " output=" + output)
        count += 1
        if status == 0:
            break
    if count > 4:
        SendMailSms("资源位-S3文件上送hive失败,template_file=" + file_id).notice_teams()

    if if_del_local:
        rmr_file(local_path, file_id)
    return local_path + "/" + file_id


def put_isc_files_to_hdfs(hdfs_path, local_path, file_ids, crowd_no, if_del_local=False):
    file_path = "/".join(hdfs_path.split('/')[:len(hdfs_path.split('/')) - 2])
    # 按照人群包前缀删除
    rm_file = '''hadoop fs -rm -r  %s/%s/%s* ''' % (file_path, crowd_no, crowd_no)
    print("rm_file= " + rm_file)
    subprocess.getstatusoutput(rm_file)
    status, output = subprocess.getstatusoutput('''hadoop fs -test -e  %s''' % hdfs_path)
    if status != 0:
        mkdirParentDirCommand = "hadoop fs -mkdir -p %s" % hdfs_path
        subprocess.getstatusoutput(mkdirParentDirCommand)

    for file_id in file_ids:
        hadoopPutCommand = '''cd %s;hadoop fs -put -f %s %s%s''' % (
            local_path, file_id, hdfs_path, crowd_no+"_"+file_id)
        status, output = subprocess.getstatusoutput(hadoopPutCommand)
        print("hadoopPutCommand= " + hadoopPutCommand + " status=" + str(status) + " ouput=" + output)
        count = 0
        while count < 4:
            status, output = subprocess.getstatusoutput(hadoopPutCommand)
            print("count=" + str(count) + " hadoopPutCommand= " + hadoopPutCommand + " status=" + str(
                status) + " output=" + output)
            count += 1
            if status == 0:
                break
        if count > 4:
            SendMailSms("资源位-文件上送hive失败,template_file=" + file_id).notice_teams()

        if if_del_local:
            rmr_file(local_path, file_id)


if __name__ == '__main__':
    hdfspath = "hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data/defined/20230727/111"
    hdfspath = "hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test//defined_v2/20230801/"
    path = "/".join(hdfspath.split('/')[:len(hdfspath.split('/')) - 2])
    print(path)
    print(hdfspath.split("/")[-1])
    localPath = "D:/DATA/logs1/logs3/"
    filename = "1.txt"
    file = localPath + filename
    # os.removedirs("D:\data\logs1")
    # if os.path.exists(file) and os.path.isfile(file):
    #     os.remove(file)

    # 判断文件夹中是否有文件，不存在文件时，删除父类文件夹
    # if len(os.listdir(localPath)) == 0:
    #     os.rmdir(localPath)

    # ll = os.scandir(path1)
    # for l1 in ll:
    #     print(l1.name)
    # print(os.scandir(path1))
    # for dir in dirs:
    #    if not os.listdir(dir):
    #        os.rmdir(dir)
