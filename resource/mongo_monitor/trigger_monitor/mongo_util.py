import threading
from urllib import quote_plus
from pymongo import MongoClient
import pipes


class MongoDBConnectionPool(object):

    def __init__(self, user, name, password):
        self.name = name
        self.user = user
        self.password = password

    _instance_lock = threading.Lock()
    _connection_pool = None

    def __new__(cls, user, password, host):
        if not hasattr(cls, "_instance"):
            with cls._instance_lock:
                if not hasattr(cls, "_instance"):
                    cls._instance = super(MongoDBConnectionPool, cls).__new__(cls)
                    cls._instance.initialize_connection_pool(user, password, host)
        return cls._instance

    def initialize_connection_pool(self, user, password, host):
        uri = "mongodb://%s:%s@%s" % (
            quote_plus(user), quote_plus(password), host)
        mongo_client = MongoClient(uri)
        self._connection_pool = mongo_client

    def get_connection(self):
        return self._connection_pool

    def close_connection(self):
        self._connection_pool.close()        


# Example usage
if __name__ == "__main__":
    # Get the singleton instance of MongoDBConnectionPool
    pool_instance = MongoDBConnectionPool("mongo", "7191c811654493d2", "mongodb.dev.daikuan.qihoo.net:2824")
    # Get a connection from the connection pool
    connection = pool_instance.get_connection()

    # Use the connection to interact with the database
    db = connection["360loan"]
    collection = db["cr_record_ref"]

    result = collection.find({"crNo": "CR202301050110200000013501"})
    for document in result:
        print(document)

    pool_instance.close_connection()
