#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : MysqlConfig.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2020-08-17
-- description   : 
'''
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from MysqlUtils import MysqlUtils

class MysqlConfig:

    @staticmethod
    def testLabelMysql():
        host = "*************"
        port = 2076
        user = "jt_label_wr"
        db = "jt_label"
        passwd = "40d73b9744186b33"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils


    @staticmethod
    def jtlabelMysql():
        host = "pro-4958-rw.mysql.daikuan.qihoo.net"
        port = 4958
        user = "jt_label"
        db = "jt_label"
        passwd = "520124ef2ddcfd69"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def ipssMysql():
        host = "mysql.dev.daikuan.qihoo.net"
        port = 2490
        user = "ipss_report"
        db = "ipss_report"
        passwd = "0cdc02e44b129408"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils
