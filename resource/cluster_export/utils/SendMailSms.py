#!/usr/bin/python
# coding:utf-8
"""
-- file name     : sendSmsMail.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2019-02-25
-- copyright     : @qctx
-- description   : 短信发送邮件发送
-- usage         : 告警处理
-- function list : 告警发送
-- history       : 第一版脚本，无历史
"""
import configparser
import json
import os
import traceback
import requests
import sys
import time
import hmac
import hashlib
import urllib
import base64
basedir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(basedir)
from export_result_notice import ExportResultNotice
from RedisPoolManager import RedisPoolManager


class SendMailSms(object):
    def __init__(self, content, crowdNo):
        self.content = content
        self.cf = configparser.ConfigParser()
        self.confPath = os.path.realpath(__file__)
        self.confDir = os.path.dirname(self.confPath)
        self.read = self.cf.read(self.confDir + "/config/db.conf")
        self.sendMainSwitch = self.cf.getboolean("emailConfig", "switch")
        self.appKey = self.cf.get("emailConfig", "appkey")
        self.appSecret = self.cf.get("emailConfig", "appsecret")
        self.url = self.cf.get("emailConfig", "url")
        self.crowdNo = crowdNo

    def sendMail(self, appdata):
        if not self.sendMainSwitch:
            print("发送邮件开关已关闭")
            return
        appkey = self.appKey
        appsecret = self.appSecret
        url = self.url
        response = requests.post(url, data=appdata, auth=(appkey, appsecret))

    def getAppData(self):
        teams = "digital_intelligence_email"
        title = "360标签系统服务告警"
        appdata = {"teams": teams, "title": title, "content": self.content, "app_content": self.content}
        self.sendMail(appdata)

    def notice_teams(self):
        print(f"team告警通知开始：{self.content},人群编号为：{self.crowdNo}")
        try:
            timestamp = str(round(time.time() * 1000))
            redisKey=f"iops:cn:{self.crowdNo}"
            redisConnect = RedisPoolManager().redis_pool_instance.connect()
            if redisConnect.exists(redisKey):
                print("1小时内已通知")
                return
            else:
                redisConnect.setex(redisKey,3600,timestamp)
            secert = self.sign(timestamp)
            appdata = {"msgtype": "text", "text": {"content": "【告警】"+self.content}, "at": {"isAtAll": "false", "userIds": []}}
            alarm_url = "https://im.360teams.com/api/qfin-api/rce-app/robot/send?access_token=4cc32e6f3a2a43dd8b8cad866ed2048580f3cdd3be7b45cf8d2f7ddebb1aa5b7&timestamp=%s&sign=%s" % (timestamp,secert)
            response = requests.post(alarm_url, json=appdata)
            if response.status_code == 200:
                print("请求成功！")
                res = response.json()
                print(res)
            else :
                print("发送通知异常")
                exit(1)
            ExportResultNotice(self.crowdNo,self.content).send()
        except Exception as e:
            print(e)
            traceback.print_exc()
            mobile_list  = "13764753037,18916512909,15221280416,18217412510"
            self.voice_notice(mobile_list, self.content)

    def voice_notice(self, mobile_list, content):
        print(f"电话告警通知开始：{self.content}")
        headers = {'Content-Type': 'application/json'}
        data = {"content": content, "alarm_list": mobile_list, "invalidSecond": 10}
        safeLyccPostUrl = "http://10.160.132.158:5000/tomq_by_voice"
        try:
            response = requests.post(safeLyccPostUrl, data=json.dumps(data), headers=headers, verify=False).json()                
        except Exception as e:
            print(e)
            traceback.print_exc()
            exit(1)
    def sign(self,timestamp):
        secret = "f06da5568acf4f499b37e6b971e5c968da1a35816c2b42ab88e114b00b6e9bcf"
        secret_enc = secret.encode('utf-8')
        string_to_sign = '{}\n{}'.format(timestamp, secret)
        string_to_sign_enc = string_to_sign.encode('utf-8')
        hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.urlsafe_b64encode(hmac_code))
        return sign

if __name__ == '__main__':
    timestamp = str(round(time.time() * 1000))
    secert = SendMailSms("【报警】集群导入测试","C1").sign(timestamp)
    appdata = {"msgtype": "text", "text": {"content": "【报警】集群导入测试"}, "at": {"isAtAll": "true", "userIds": []}}
            ##alarm_url = "https://im.360teams.com/api/qfin-api/rce-app/robot/send?access_token=0928611ca7964f91b622ce1fb805ec593be249de99834d9eaa372990241ad265"
    alarm_url = "https://im.360teams.com/api/qfin-api/rce-app/robot/send?access_token=4cc32e6f3a2a43dd8b8cad866ed2048580f3cdd3be7b45cf8d2f7ddebb1aa5b7&timestamp=%s&sign=%s" % (timestamp,secert)
    response = requests.post(alarm_url, json=appdata)
    if response.status_code == 200:
        print("请求成功！")
        res = response.json()
        print(res)
    else :
        print("发送通知异常")
    