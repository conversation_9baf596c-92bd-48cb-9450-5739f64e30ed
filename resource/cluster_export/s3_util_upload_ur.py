#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'luxiaolong'

'''
-- file name     : s3_util_upload_ur.py
-- author        : luxiaolong
-- version       : v1.0
-- date          : 2022-11-17
-- copyright     : @qctx
-- description   : s3工具类
-- usage         : 获取s3数据
-- function list : 
-- history       : 第一版脚本，无历史
'''
import logging
import sys

import boto3
from botocore.client import Config

console_handler = logging.StreamHandler()
console_handler.setLevel('INFO')
fmt = '%(asctime)s - %(funcName)s - %(lineno)s - %(levelname)s - %(message)s'
formatter = logging.Formatter(fmt)
console_handler.setFormatter(formatter)
logger = logging.getLogger('s3_util_upload')
logger.setLevel('DEBUG')
logger.addHandler(console_handler)


def upload_file(endpoint_url, aws_secret_access_key, aws_access_key_id, service, bucket_name, file_name, local_path):
    try:
        count = len(open(local_path, 'r').readlines())
        valid_flag = invalid_user_no(local_path, count)
        if valid_flag is True:
            return "failed001"
        client = get_s3_conn(endpoint_url, aws_secret_access_key, aws_access_key_id, service)
        return upload(client, bucket_name, file_name, local_path)
    except Exception as e:
        logger.error("上传文件异常:" + str(e), exc_info=True)
        return "failed003"


def upload(s3_cli, bucket_name, file_name, local_path):
    try:
        with open(local_path, 'rb') as data:
            s3_cli.upload_fileobj(data, bucket_name, file_name)
        return 'success'
    except Exception as e:
        logger.error("上传文件异常:" + str(e), exc_info=True)
        return "failed003"


def get_s3_conn(endpoint_url, aws_secret_access_key, aws_access_key_id, service):
    s3_cli = boto3.client('s3', service,
                          config=Config(signature_version='s3v4'), use_ssl=False,
                          endpoint_url=endpoint_url,
                          aws_secret_access_key=aws_secret_access_key,
                          aws_access_key_id=aws_access_key_id)
    return s3_cli


def invalid_user_no(local_path, count):
    logger.info("用户号是否正常" + str(count))
    valid_num = 0
    num = 0
    for line in open(local_path):
        line = line.strip()
        if line.startswith("user_no") or line == r"\N":
            continue
        if (
            (line.startswith("UR") or line.startswith("CQ"))
            and 25 >= len(line) >= 20
        ) or line.startswith("wx_"):
            valid_num += 1
            if valid_num % 10000 == 0:
                print(f"已校验：{valid_num}")
        else:
            logger.error("存在用户号非法：" + line)
            return True
    logger.info("用户号是否正常处理完成" + str(num))
    return False


result = upload_file(sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4], sys.argv[5], sys.argv[6], sys.argv[7])
print(result)