#!/usr/bin/python3.1
# -*- coding: utf-8 -*-
"""
-- file name     : user_analysis.py
-- author        : luxiaolong
-- version       : v1.9
-- date          : 2024-09-03
-- copyright     : @qctx
-- function list : 用户分析
"""

import os
import sys
import subprocess

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class UserAnalysisHdfs(object):
    def __init__(self, task_no, file_key, start_time, end_time):
        self.task_no = task_no
        self.file_key = file_key
        self.start_time = start_time
        self.end_time = end_time
        self.parent_path = os.path.dirname(os.path.abspath(__file__))
        self.hdfs_path = "/user/hive/warehouse/dp_data_db.db/ods_ipss_task_user_analysis_stg/task_no="
        self.download_path = self.parent_path + '/download/'
        self.tmp_path = self.parent_path + '/download/'

    def put_file_hdfs(self, user_analysis):
        try:
            print(self.file_key)
            file_path = self.download_path + self.file_key
            temp_file_path = self.parent_path + "/tmp/"
            print(temp_file_path)
            temp_file = temp_file_path + self.file_key
            file_command = (
                f'cd {self.download_path}\n'
                f'sed -e "s/^M/\n/g" {file_path}\n'
                f'cat {file_path} | awk \'{{sub("^ *", ""); sub(" *$", ""); print}}\'\n'
                f'awk \'$0=$0"\\t{self.task_no}\\t{self.start_time}\\t{self.end_time}" > {temp_file}'
            )
            process = subprocess.Popen(file_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            output, error = process.communicate()
            status = process.returncode
            print(f'status: {status}')
            hive_path = self.hdfs_path + self.task_no
            if status == 0:
                hadoop_file_create = f'hdfs fs -rm -skipTrash {hive_path}'
                process = subprocess.Popen(hadoop_file_create, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE)
                if process.returncode != 0:
                    print(f'删除文件异常：{hive_path}')
                    return
                hadoop_file_create = f'hdfs fs -mkdir {hive_path}'
                process = subprocess.Popen(hadoop_file_create, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE)
                if process.returncode != 0:
                    print(f'创建文件异常：{hive_path}')
                    return
                hadoop_command = f'cd {self.tmp_path}; hadoop fs --put -f {self.file_key} {hive_path}'
                print("hadoop_command===" + hadoop_command)
                process = subprocess.Popen(hadoop_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if process.returncode == 0:
                    print(f'hadoop 上传文件成功: {self.task_no}')
                    return 1
                else:
                    print(f'Error hadoop 上传文件异常：{self.task_no},异常原因：{process.communicate()}')
                    return 0
            else:
                print(f"Error occurred. Status code: {status}. Error message: {error.decode('gbk')}")
        except Exception as e:
            print(repr(e))
            return 0


if __name__ == '__main__':
    uah = UserAnalysisHdfs('1', 'test.txt', '2024-09-03 12:00:00', '2024-09-04 13:00:00')
    result = uah.put_file_hdfs()
    print(result)
