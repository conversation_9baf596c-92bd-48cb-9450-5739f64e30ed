#!/usr/bin/python3.1
# -*- coding: utf-8 -*-
"""
-- file name     : user_analysis.py
-- author        : luxiaolong
-- version       : v1.0
-- date          : 2024-09-03
-- copyright     : @qctx
-- function list : 用户分析
"""

import os
import sys
import json

from user_analysis_hdfs import UserAnalysisHdfs
from user_analysis_ck import UserAnalysisCk

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from utils.s3ClientUtil import S3Util
from utils.filePathChange import WriteDataFile


from datetime import datetime

date_formatter = "%Y-%m-%d %H:%M:%S"

def is_today(date):
    today = datetime.today().date()
    return date.date() == today


def calc_path():
    return os.path.dirname(os.path.abspath(__file__)) + '/download/'


def download(download_path, file_key):
    if not os.path.exists(download_path):
        os.makedirs(download_path)
    if not os.path.exists(download_path + file_key):
        os.system(r"touch {download_path}{file_key}".format(download_path=download_path, file_key=file_key))
    print(f"下载路径:{download_path},key:{file_key}")

    S3Util(file_key).downloadFilePath(download_path)
    WriteDataFile().dos_to_unix(download_path + file_key)


if __name__ == '__main__':

    # json_str = '''
    #     {
    #         "userCount":0,
    #         "taskNo":"TAU00000A",
    #         "fileKey":"d73de7b5a17c043690e8b6b9f8452c51",
    #         "startTime":"2024-09-26 00:22:33",
    #         "endTime":"2024-09-26 23:55:11",
    #         "hiveDbName":"credit_data",
    #         "hiveHdfsDbName":"credit_data",
    #         "hiveHdfsTableName":"ods_ipss_task_user_analysis_stg"
    #     }
    # '''

    json_str = sys.argv[1]
    print(f"json_str:{json_str}")

    jsonParam = json.loads(json_str)

    # 文件路径
    download_path = calc_path()
    file_key = jsonParam['fileKey']
    full_file_path = download_path + file_key

    # 时间
    start_datetime = datetime.strptime(jsonParam['startTime'], date_formatter)
    end_datetime = datetime.strptime(jsonParam['endTime'], date_formatter)

    # 其他数据
    task_no = jsonParam['taskNo']
    crowd_count = jsonParam['userCount']

    hiveHdfsDbName = jsonParam['hiveHdfsDbName']
    hiveHdfsTableName = jsonParam['hiveHdfsTableName']

    download(download_path, file_key)
    print(f"download file:[{full_file_path}] success.")

    if is_today(start_datetime):
        print("is today,ready to process ck................")
        ua_ck = UserAnalysisCk(full_file_path,task_no,start_datetime,end_datetime,crowd_count)
        ua_ck.save_user()
    ua_hdfs = UserAnalysisHdfs(task_no, file_key, start_datetime, end_datetime,hiveHdfsDbName, hiveHdfsTableName)
    ua_hdfs.put_file_hdfs()
