#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : Alarm.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2020-08-27
-- description   : 
'''
import sys


class Alarm(object):
    def __init__(self, content, app_content = ''):
        self.title = "奇富科技数据任务监控报警"
        self.teams = "360creditDcplat"
        self.appkey = "creditdcplat"
        self.appsecret = "95CDEAF62D4BFBFF0A0BB20F12A36A71"
        self.url = "http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"
        self.content = content
        if app_content == '' :
            self.app_content = content
        else:
            self.app_content= app_content

    def sendTagMail(self):
        title = "智能运营平台"
        teams = "digital_intelligence_yushu_sms"
        appdata = {"teams": teams, "title": title, "content": self.content, "app_content": self.app_content}
        # response = requests.post(self.url, data=appdata, auth=(self.appkey, self.appsecret))
        print(appdata)

    def sendTagSMS(self):
        title = "智能运营平台"
        teams = "digital_intelligence_yushu_sms"
        appdata = {"teams": teams, "title": title, "content": self.content, "app_content": self.app_content}
        # response = requests.post(self.url, data=appdata, auth=(self.appkey, self.appsecret))
        print(appdata)


if __name__ == '__main__':
    type = sys.argv[1]
    message = sys.argv[2]
    alarm = Alarm(message)
    if type == "sms":
        alarm.sendTagSMS()
    else:
        alarm.sendTagMail()
