#!/usr/bin/python
# coding:utf-8

'''
-- file name     : smsContentStyleUtil.py
-- author        : wanghaoyu1-jk
-- version       : v1.0
-- date          : 2021-08-17
-- copyright     : @qctx
-- description   : 邮件内容样式
-- usage         : 邮件内容样式
-- function list : 邮件内容样式
-- history       : 第一版脚本，无历史
'''

import importlib
import sys
import time

importlib.reload(sys)


class SmsContentStyleUtil(object):
    def __init__(self, headContent, colnameList, contentDataList):
        self.headContent = headContent
        self.colnameList = colnameList
        self.contentDataList = contentDataList

    def styleContent(self):
        # if len(self.colnameList) <= 0:
        #     raise Exception("请给出表头列字段！")

        currenttime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())

        html = """\
          <html xmlns="http://www.w3.org/1999/xhtml">
          <head>
          <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
          <title>hulk数据告警</title>
          <style type="text/css">  
              .onecenter{text-align:center;white-space: nowrap;}
              .onecenter1{text-align:right;white-space: nowrap;}
              .onecenter2{text-align:left;white-space: nowrap;}
              .confCss2{background: #CCCCCC;}
              .confCss1{background: #FFFFFF;}
              .div{margin-left:0px}
               body{font-family:"新宋体";}
              .p1{margin:50px;}
               h1{font-size:14px;color:#B0C4DE;font-family:"新宋体";font-weight:100;}
               .style1 {text-align: center; white-space: nowrap; font-family: "新宋体"; }
          </style>

          </head>
          <body>
          <div id="container2"  style="height:100%%">
            <div >
               <p>
                <br>
                    <strong>
                        %s
                    </strong>
                </p> 
            """ % self.headContent
        # 【数据平台数据服务告警】数据智能平台数据服务告警-<span style='color:red'>非常重要</span>-数据推送异常,异常原因:

        ### 如果设置了列，才拼接表头和表内容
        if len(self.colnameList) > 0:
            # 拼接表头列
            tableHead = ""
            for colname in self.colnameList:
                tableHead += """
                    <td class="onecenter"  style="padding-left:20px;"><div align="center">%s</div></td>
                    """ % (colname)

            # 追加表头样式和表头列
            html += """
                    <div id="content" class="div">
                         <table  width="850" border="1" >
                          <tr bgcolor="#12529A" style="font-weight:bold;color:#FFFFFF"  class="onecenter"  >
                            <td colspan="%s"  >数据推送服务监控 截止时间:[%s]</td>
                          </tr>
                        <tr bgcolor="#12529A" style="font-weight:bold;color:#FFFFFF" >
                          %s
                        </tr>
                    """ % (len(self.colnameList), currenttime, tableHead)

            # 追加内容字段
            if len(self.contentDataList) > 0:
                for row in self.contentDataList:
                    contentRows = ""
                    for cell in row:
                        contentRows += """
                            <td class="onecenter"  style="padding-left:20px;"><div align="center">%s</div></td>
                            """ % (cell)

                    html += """
                        <tr bgcolor="#E6E6E6"  style='color:#070B19'>
                            %s
                        </tr>
                        """ % (contentRows)

            html += """
                </table>
                    </div>
                """

        # 补全html文件结尾
        html += """
              </div>
               <p class="p1">
                   <p>
                   </p>
               </p>
              </div><br/>
            """

        return html

    def styleContentRobot(self):
        '''
        teams告警内容
        :return:
        '''

        currenttime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())

        content = self.headContent
        if len(self.colnameList) > 0:
            # 拼接表头列
            tableHead = ""
            for colname in self.colnameList:
                tableHead += """ %s """ % (colname)

            # 追加表头样式和表头列
            content += """ 数据推送服务监控 截止时间:[%s] \n %s""" % ( currenttime, tableHead)
            content +="\n"
            # 追加内容字段
            if len(self.contentDataList) > 0:
                for row in self.contentDataList:
                    contentRows = ""
                    for cell in row:
                        contentRows += """ %s """ % (cell)

                    content += """ %s """ % (contentRows)
        return content

if __name__ == "__main__":
    headContent = "【数据平台数据服务告警】数据智能平台数据服务告警-<span style='color:red'>非常重要</span>-数据推送异常,异常原因:"
    columnList = ["姓名", "年龄", "性别"]
    contentDataList = [
        ["aa", "20", "女"],
        ["bb", "21", "男"],
        ["cc", "22", "女"],
        ["dd", "23", "男"]
    ]
    # html=SmsContentStyleUtil(headContent, columnList, contentDataList).styleContent()
    html = SmsContentStyleUtil(headContent, columnList, contentDataList).styleContentRobot()
    print(html)
