#!/usr/bin/python3.1
# -*- coding: utf-8 -*-
"""
-- file name     : user_analysis.py
-- author        : luxiaolong
-- version       : v1.9
-- date          : 2024-09-03
-- copyright     : @qctx
-- function list : 用户分析
"""

import os
import sys

from utils.MysqlUtils import MysqlUtils
from utils.MysqlDataSource import MysqlDataSource

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
from utils.ClickHouseUtil import <PERSON>lickHouseUtil
from datetime import datetime

date_formatter = "%Y-%m-%d %H:%M:%S"

class UserAnalysisCk(object):
    def __init__(self, file_full_path_name,task_no,start_datetime:datetime,end_datetime:datetime,crowd_count):
        self.file_full_path_name = file_full_path_name
        self.task_no = task_no

        self.start_datetime = start_datetime
        self.end_datetime = end_datetime

        self.crowd_count = crowd_count


    def save_user(self):
        ck = ClickHouseUtil()
        mysql = MysqlDataSource.getmysqlutil("ipss_report_prd")
        try:
            # 删除已经存在的task_no数据
            self.delete_existing_ck_data(ck,mysql)
            # s3下载本地的文件存储到ck
            self.s3_to_ck(ck)
            # 存储 user_detail
            self.save_analysis_detail(ck)
            # 存储 user_detail_result
            self.save_analysis_result(ck)
            # ck 同步到 mysql (user_detail,user_detail_result)
            self.sync_to_mysql_from_ck(ck,mysql)
            # 插入流程执行结束
            self.write_mysql_complement(mysql,self.task_no)
        finally:
            mysql.closeDb()

    def s3_to_ck(self, ck):
        current_datetime = datetime.now()
        file_path = self.file_full_path_name
        sql = "insert into ys_ods_real_local.prd_ipss_isc_task_user_analysis_real(`datetime`,task_no , user_no, start_datetime,end_datetime,crowd_count) values "
        data = []
        print(f"本地文件:{file_path}")
        with open(file_path, 'r') as file:
            i = 0
            for line in file:
                data.append((
                    current_datetime, self.task_no, line.strip(), self.start_datetime,
                    self.end_datetime, self.crowd_count
                ))
                i += 1
                if i == 100:
                    ck.client.execute(sql, data)
                    data = []
                    i = 0  # strip() 去除每行末尾的换行符
            if len(data) > 0:
                ck.client.execute(sql, data)

    def delete_existing_ck_data(self, ck,mysql:MysqlUtils):
        ck.client.execute(f"alter table ys_ods_real_local.prd_ipss_isc_task_user_analysis_real delete where task_no = '{self.task_no}'")
        ck.client.execute(f"alter table ys_ods_real_local.prd_isc_it_task_user_analysis_result_detail delete where task_no = '{self.task_no}'")
        ck.client.execute(f"alter table ys_ods_real_local.prd_isc_it_task_user_analysis_result delete where task_no = '{self.task_no}'")
        cursor = mysql.conn.cursor()
        cursor.execute(f"UPDATE ipss_report.isc_it_task_user_analysis_result_detail SET is_delete = 1 WHERE task_no = '{self.task_no}'")
        cursor.execute(f"UPDATE ipss_report.isc_it_task_user_analysis_result SET is_delete = 1 WHERE task_no = '{self.task_no}'")
        cursor.execute(f"UPDATE ipss_report.isc_it_task_user_analysis_complement SET is_delete = 1 WHERE task_no = '{self.task_no}'")
        mysql.conn.commit()
        cursor.close()

    def save_analysis_detail(self,ck:ClickHouseUtil):
        print("enter in save_analysis_detail......")
        analysis_result_sql = '''
            INSERT INTO 
                ys_ods_real_local.prd_isc_it_task_user_analysis_result_detail(task_no, batch_no, user_no)
            SELECT DISTINCT 
                a.task_no,
                b.batch_no,
                a.user_no 
            FROM 
                ys_ods_real_local.prd_ipss_isc_task_user_analysis_real a
            INNER JOIN 
                ys_ods_real.prd_strategy_touch_user_view_new b ON a.user_no = b.user_no
            WHERE a.task_no = '{task_no}' 
                AND b.reach_time BETWEEN toDateTime('{start_datetime}') AND toDateTime('{end_datetime}')
        '''
        print(f"save_analysis_detail_sql:{analysis_result_sql}")
        print(self.start_datetime.strftime(date_formatter))
        print(self.end_datetime.strftime(date_formatter))
        print(analysis_result_sql.format(task_no=self.task_no,start_datetime=self.start_datetime.strftime(date_formatter),end_datetime=self.end_datetime.strftime(date_formatter)))
        ck.client.execute(analysis_result_sql.format(task_no=self.task_no,start_datetime=self.start_datetime.strftime(date_formatter),end_datetime=self.end_datetime.strftime(date_formatter)))

    def save_analysis_result(self,ck:ClickHouseUtil):
        print("enter in save_analysis_result......")
        # (task_no, batch_no, user_count, plan_no, plan_name, strategy_no, strategy_name, strategy_creator, crowd_no, crowd_name, reach_time, channel_type_name, hit_count, hit_rate)
        analysis_result_sql = '''
            INSERT INTO ys_ods_real_local.prd_isc_it_task_user_analysis_result
            (task_no, batch_no, user_count, plan_no, plan_name, strategy_no, strategy_name, strategy_creator, crowd_no, crowd_name, reach_time, channel_type_name, hit_count, hit_rate)
            
            select task_no, batch_no, user_count, plan_no, plan_name, strategy_no, strategy_name, strategy_creator
            , crowd_no, crowd_name, reach_time, channel_type_name, hit_count
            , hit_count/user_count as hit_rate from (
            WITH a AS(
                SELECT * FROM ys_ods_real_local.prd_isc_it_task_user_analysis_result_detail WHERE task_no = '{task_no}'
            ),
            file_user_count as (
                        SELECT count() AS user_count from ys_ods_real_local.prd_ipss_isc_task_user_analysis_real where task_no = '{task_no}'
           )
             SELECT DISTINCT
                a.task_no as task_no,
                a.batch_no as batch_no,
                (select user_count from file_user_count)AS user_count,
                tmp.batch_hit_count as hit_count,
                b.plan_no                   AS plan_no,
                b.plan_name                 AS plan_name,
                b.strategy_no               AS strategy_no,
                b.strategy_name             AS strategy_name,
                b.strategy_creator          As strategy_creator,
                b.crowd_no                  AS crowd_no,
                b.crowd_name                AS crowd_name,
                b.task_exec_time            AS reach_time,
                toString(b.channel)    		AS channel_type_name
            FROM
                a
            INNER JOIN
                realtime_report.strategy_aggregation_info_realtime b ON a.batch_no = b.batch_number
               and stat_datetime =  
                    (
                        SELECT max(stat_datetime)
                        FROM realtime_report.strategy_aggregation_info_realtime
                    )
            INNER JOIN
                (SELECT count()AS batch_hit_count,batch_no FROM a GROUP BY batch_no) tmp ON a.batch_no = tmp.batch_no
          ) tt
        '''
        print(f"save_analysis_result_sql:{analysis_result_sql}")
        ck.client.execute(analysis_result_sql.format(task_no=self.task_no))


    def sync_to_mysql_from_ck(self, ck: ClickHouseUtil,mysql:MysqlUtils):

        self.sync_user_analysis_result_detail(ck, mysql)

        self.sync_user_analysis_result(ck, mysql)


    def sync_user_analysis_result_detail(self, ck, mysql):
        cur_index = 1
        page_size = 1000
        ck_base_sql = "SELECT task_no,batch_no,user_no FROM ys_ods_real_local.prd_isc_it_task_user_analysis_result_detail WHERE task_no = '{task_no}' AND is_delete = 0 LIMIT {page_size} OFFSET {offset}"
        mysql_data = []
        mysql_base_sql = "INSERT INTO ipss_report.isc_it_task_user_analysis_result_detail(task_no,real_task_no,batch_no,user_no) VALUES(%s,%s,%s,%s)"
        print("=======================")
        while True:
            offset = (cur_index - 1) * page_size
            ck_query_sql = ck_base_sql.format(task_no=self.task_no,page_size=page_size, offset=offset)
            print(ck_query_sql)
            ck_data = ck.client.execute(ck_query_sql)

            if len(ck_data) > 0:
                for each in ck_data:
                    task_no, batch_no, user_no = each
                    mysql_data.append((task_no,task_no, batch_no, user_no))
                mysql.insertValues(mysql_base_sql, mysql_data)
                mysql_data.clear()

            if len(ck_data) != page_size:
                break
            cur_index += 1

    def sync_user_analysis_result(self, ck, mysql):
        cur_index = 1
        page_size = 1000
        ck_base_sql = '''
            SELECT 
                result_no,task_no,batch_no,user_count,plan_no,plan_name,strategy_no,strategy_name,strategy_creator,
                crowd_no,crowd_name,reach_time,channel_type_name,hit_count,hit_rate
            FROM ys_ods_real_local.prd_isc_it_task_user_analysis_result WHERE task_no = '{task_no}' AND is_delete = 0
            LIMIT {page_size} OFFSET {offset}
        '''
        mysql_data = []
        mysql_base_sql = '''
            INSERT INTO 
                ipss_report.isc_it_task_user_analysis_result
                (result_no, task_no, real_task_no,batch_no, user_count, plan_no, plan_name, strategy_no, strategy_name, strategy_creator,crowd_no, crowd_name, reach_time, channel_type_name, hit_count, hit_rate)
            VALUES
                (%s, %s, %s,%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        '''

        print("######################")
        while True:
            offset = (cur_index - 1) * page_size
            ck_query_sql = ck_base_sql.format(task_no = self.task_no,page_size=page_size, offset=offset)
            ck_data = ck.client.execute(ck_query_sql)
            print(ck_query_sql)
            if len(ck_data) > 0:
                for each in ck_data:
                    result_no, task_no, batch_no, user_count, plan_no, plan_name, strategy_no, strategy_name, strategy_creator,crowd_no, crowd_name, reach_time, channel_type_name, hit_count, hit_rate = each
                    mysql_data.append((result_no, task_no, task_no, batch_no, user_count, plan_no, plan_name, strategy_no, strategy_name, strategy_creator,crowd_no, crowd_name, reach_time, channel_type_name, hit_count, hit_rate))
                mysql.insertValues(mysql_base_sql, mysql_data)
                mysql_data.clear()

            if len(ck_data) != page_size:
                break
            cur_index += 1

    @staticmethod
    def write_mysql_complement(mysql,task_no):
        mysql_base_sql = "INSERT INTO ipss_report.isc_it_task_user_analysis_complement(task_no) VALUES(%s)"
        mysql_data = []
        mysql_data.append((task_no))
        mysql.insertValues(mysql_base_sql, mysql_data)



if __name__ == '__main__':
    # //    def __init__(self, file_full_path_name,task_no,start_datetime:datetime,end_datetime:datetime,crowd_count):

   u = UserAnalysisCk("","task1111_no",datetime.now(),datetime.now(),1000)
   u.save_analysis_detail("null")