#!/bin/bash
# file name     : pull_contrast.sh
# author        : zhangyong
# version       : v1.0
# date          : 2023-06-29
# description   : 项目名称: strategy_crowd_recycle_batch 人群回收
source ~/.bashrc
unzip pull_white.zip

cwd=$(
  cd $(dirname "$0")
  pwd
)
scriptDir=${cwd}/pull_white
python=/bin/python3.6
hadoop=/usr/hdp/*******-315/hadoop/bin/hadoop

yesterday=${system.biz.date}
thedate=`date -d ${yesterday} +%Y%m%d`

if ! test $thedate; then
  thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi

inputpath=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/temp_strategy_white_person_data/pday=${thedate}

${hadoop} fs -rm -r -skipTrash $inputpath
${hadoop} fs -mkdir -p $inputpath
${python} ${scriptDir}/pull_data.py ${thedate}


if [ $? -eq 0 ]; then
  echo "执行sql"
else
   echo "执行文件上送失败"
   exit 1
fi

# hive -e "msck repair table credit_data.strategy_white_person_data"