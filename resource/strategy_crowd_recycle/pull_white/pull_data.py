#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'zhangyong'

'''
-- file name     : pull_data.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-04-22
-- description   : 组内留白文件上送hive
'''
import datetime
import os
import subprocess
import sys
import time
import traceback

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from utils.MysqlConfig import MysqlConfig
from utils.Alarm import Alarm
from warehouseUtil import DownLoadWarehouse


class PullData:
    def __init__(self, thedate, hadoop, safe_path):
        self.thedate = thedate
        self.format_thedate = datetime.datetime.strptime(thedate, "%Y%m%d").strftime("%Y-%m-%d")
        self.format_thedate_start = datetime.datetime.strptime(thedate, "%Y%m%d").strftime("%Y-%m-%d 00:00:00")
        self.format_thedate_end = datetime.datetime.strptime(thedate, "%Y%m%d").strftime("%Y-%m-%d 23:59:59")
        self.script_path = os.path.dirname(os.path.realpath(__file__))
        self.hadoop = hadoop
        self.safe_path = safe_path

    def alarm(self, content):
        alarm = Alarm(content)
        print(content)
        alarm.sendTagSMS()

    def remove_local_file(self):
        cmd = "rm -f %s/data/* %s/new_data/*" % (self.script_path, self.script_path)
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        if status != 0:
            raise Exception("删除本地数据失败")

    def query_task(self):
        # 普通策略 有组内留白
        sql = f"""
             select t3.person_num as crowd_no,file_ids,crowd_type,concat(t3.strategy_id,"_",t4.task_id) as batch_no
            from ( select strategy_id,person_num from jt_label.fa_plan_user) t3
            join (
			    select strategy_id, crowd_rate_fileids as file_ids,task_id,'1' as crowd_type
                from fa_plan_task
                where date(send_start_time) = '{self.format_thedate}' 
                     and send_start_time between  '{self.format_thedate_start}' and '{self.format_thedate_end}'
			         and crowd_rate_fileids != '' 
			         and crowd_rate_fileids not like 'Tri%'
     		    union all
			    select strategy_id, comp_rate_fileids as file_ids,task_id,'2' as crowd_type
                from fa_plan_task
                where date(send_start_time) = '{self.format_thedate}' 
                     and send_start_time between  '{self.format_thedate_start}' and '{self.format_thedate_end}'
			         and comp_rate_fileids != '' 
			         and comp_rate_fileids not like 'Tri%'
			    union all 
                select strategy_id, group_rw_file_ids as file_ids,t.task_id as task_id,'3' as crowd_type
                from 
                	fa_plan_task_ext t,
			        fa_plan_task fpt 
                where date(fpt.send_start_time) = '{self.format_thedate}'  
                     and fpt.send_start_time between  '{self.format_thedate_start}' and '{self.format_thedate_end}'
			         and fpt.is_delete = 0  
			         and t.is_delete = 0  
			         and t.task_id = fpt.task_id  
			         and group_rw_file_ids is not null 
			         and group_rw_file_ids != '' 
			         and group_rw_file_ids not like 'Tri%'
                union all
                select strategy_id, group_comp_rw_file_ids as file_ids,t.task_id as task_id,'4' as crowd_type
                from fa_plan_task_ext t,
			         fa_plan_task fpt 
                where date(fpt.send_start_time) = '{self.format_thedate}' 
                     and fpt.send_start_time between  '{self.format_thedate_start}' and '{self.format_thedate_end}'
			         and fpt.is_delete = 0  
			         and t.is_delete = 0  
			         and t.task_id = fpt.task_id
			         and group_comp_rw_file_ids is not null 
			         and group_comp_rw_file_ids != '' 
			         and group_comp_rw_file_ids not like 'Tri%'
            ) t4
            on t3.strategy_id = t4.strategy_id
			union all
		    select t3.person_num,file_ids,crowd_type,concat(t3.strategy_id,"_",t4.task_id) as batch_no
            from (
                select strategy_id,person_num from fa_plan_user 
			    where comp_person_type = 3 and sub_reserved_rule=0
            ) t3 join ( select strategy_id, crowd_fileids as file_ids,task_id,'1' as crowd_type
                  from jt_label.fa_plan_task
                  where date(send_start_time) = '{self.format_thedate}' 
                      and  send_start_time between  '{self.format_thedate_start}' and '{self.format_thedate_end}'
			          and crowd_fileids != '' 
			          and crowd_fileids is not null
			          and crowd_fileids not like 'Tri%' ) t4
            on t3.strategy_id = t4.strategy_id
        """
        print(sql)
        mysql = MysqlConfig.labelMysql()
        tasks = list(mysql.selectValues(sql))

        # 一体化策略数据   有组内留白
        sql = f"""
		  select y.crowd_no as crowd_no,y.file_ids,y.crowd_type as crowd_type,z.batch_no
           from (
                select crowd_no,task_no,strategy_no,rw_file_ids as file_ids,'1' as crowd_type
                       from ipss.isc_task_crowd
                       where  task_exec_time  >= date(DATE_SUB('{self.format_thedate}',INTERVAL 45 DAY)) 
					   and rw_file_ids is not null 
					   and trim(rw_file_ids) != '' 
					   and rw_file_ids not like 'Tri%'
			    union all
			    select crowd_no,task_no,strategy_no,group_rw_file_ids as file_ids,'2' as crowd_type
                       from ipss.isc_task_crowd
                       where  task_exec_time  >= date(DATE_SUB('{self.format_thedate}',INTERVAL 45 DAY)) 
					   and group_rw_file_ids is not null 
					   and trim(group_rw_file_ids) != '' 
					   and group_rw_file_ids not like 'Tri%'
			     union all 
			     select crowd_no,task_no,strategy_no,comp_rw_file_ids as file_ids,'3' as crowd_type
                       from ipss.isc_task_crowd
                       where  task_exec_time  >= date(DATE_SUB('{self.format_thedate}',INTERVAL 45 DAY)) 
					   and comp_rw_file_ids is not null 
					   and trim(comp_rw_file_ids) != '' 
					   and comp_rw_file_ids not like 'Tri%'
			     union all
			     select crowd_no,task_no,strategy_no,group_comp_rw_file_ids as file_ids,'4' as crowd_type
                       from ipss.isc_task_crowd
                       where  task_exec_time  >= date(DATE_SUB('{self.format_thedate}',INTERVAL 45 DAY)) 
					   and group_comp_rw_file_ids is not null 
					   and trim(group_comp_rw_file_ids) != '' 
					   and group_comp_rw_file_ids not like 'Tri%'
            ) y
            join (
                select crowd_task_no,batch_no
                from isc_task_reach
                where status = 2 and date(reach_finish_time) = '{self.format_thedate}'
                 and  reach_finish_time between  '{self.format_thedate_start}' and '{self.format_thedate_end}'
            ) z
            on y.task_no = z.crowd_task_no
            """
        print(sql)
        mysql = MysqlConfig.integrationMysql()
        integration_tasks = list(mysql.selectValues(sql))
        tasks.extend(integration_tasks)
        return tasks

    def download_file(self, tasks):
        self.remove_local_file()
        warehouse = DownLoadWarehouse()
        all_file = []
        all = len(tasks)
        cnt = 0
        for task in tasks:
            cnt = cnt + 1
            crowd_no, file_ids, crowd_type, batch_no = task
            child_crowd_fileids = file_ids.split(',')
            for file in child_crowd_fileids:
                print(file)
                result = 0
                for i in range(3):
                    result = warehouse.download_file(file)
                    if result == 200:
                        print("all:%s,current:%s,download:%s,succeed" % (str(all), str(cnt), file))
                        all_file.append((crowd_no, file, crowd_type, batch_no))
                        break
                    else:
                        print("all:%s,current:%s,download:%s,failed" % (str(all), str(cnt), file))
                if result != 200:
                    raise Exception("下载文件重试三次失败:"+file)
        return all_file

    def write_person_num(self, tasks):
        data = []
        rows = 0
        file_number = 1
        for task in tasks:
            person_num, crowd_fileids, crowd_type, batch_no = task
            child_crowd_fileids = crowd_fileids.split(',')
            for file_name in child_crowd_fileids:
                cur_file_len = 0
                with open("%s/data/%s" % (self.script_path, file_name), 'r') as source_file:
                    for line in source_file:
                        line = line.strip()
                        cur_file_len = cur_file_len + 1
                        temp_line = line + "\t" + crowd_type + "\t" + person_num + "\t" + batch_no + "\n"
                        data.append(temp_line)
                        rows = rows + 1
                        if rows == 4500000:
                            dst_file = "%s/new_data/%s" % (self.script_path, str(file_number))
                            with open(dst_file, 'w') as file:
                                file.write("".join(data))
                            print("文件%s写入%s行数据" % (str(file_number), str(rows)))
                            rows = 0
                            data.clear()
                            file_number = file_number + 1
                    print("file_name=%s,total_line=%s" % (file_name, cur_file_len))
        dst_file = "%s/new_data/%s" % (self.script_path, str(file_number))
        if data and len(data) > 0:
            with open(dst_file, 'w') as file:
                file.write("".join(data))
        print("最终文件%s写入%s行数据" % (str(file_number), str(rows)))

    def upload_file(self):
        cmd = "%s fs -put -f %s/new_data/* %s" % (self.hadoop, self.script_path, self.safe_path)
        print(cmd)
        for i in range(4):
            status, output = subprocess.getstatusoutput(cmd)
            if status == 0:
                break
            if i < 3:
                time.sleep(30)
                print("put失败，sleep 30s")
                print(output)
                subprocess.getstatusoutput("%s fs -rm %s/*" % (self.hadoop, self.safe_path))
            else:
                print(output)
                raise Exception("上传文件至毓数集群失败")

    def runTask(self):
        try:
            tasks = self.query_task()
            all_file = self.download_file(tasks)
            self.write_person_num(all_file)
            self.upload_file()
        except Exception as e:
            traceback.print_exc()
            message = "【数智平台数据处理】标签对照组数据拉回毓数:client04处理异常,请及时排查问题,Reason:%s" % (e)
            self.alarm(message)
            sys.exit(1)


if __name__ == '__main__':
    thedate = sys.argv[1]
    hadoop = "/usr/hdp/3.1.4.0-315/hadoop/bin/hadoop"
    safe_path = f"hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/temp_strategy_white_person_data/pday={thedate}"
    pull = PullData(thedate, hadoop, safe_path)
    pull.runTask()
