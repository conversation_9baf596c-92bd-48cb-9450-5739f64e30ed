#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'

import json

'''
-- file name     : Alarm.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2020-08-27
-- description   : 
'''
import sys

import requests


class Alarm(object):
    def __init__(self, content):
        self.title = "奇富科技数据任务监控报警"
        self.teams = "360creditDcplat"
        self.appkey = "creditdcplat"
        self.appsecret = "95CDEAF62D4BFBFF0A0BB20F12A36A71"
        self.url = "http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"
        self.content = content
        self.app_content = content

    def sendTagMail(self):
        title = "奇富科技数智平台"
        teams = "digital_intelligence_email"
        appdata = {"teams": teams, "title": title, "content": self.content, "app_content": self.app_content}
        response = requests.post(self.url, data=appdata, auth=(self.appkey, self.appsecret))

    def sendTagSMS(self):
        # title = "奇富科技数智平台"
        # teams = "digital_intelligence_sms"
        # appdata = {"teams": teams, "title": title, "content": self.content, "app_content": self.app_content}
        # response = requests.post(self.url, data=appdata, auth=(self.appkey, self.appsecret))
        try:
            self.notice_teams()
            self.voice_notice(self.content)
        except Exception as e:
            print(e)
            sys.exit(1)

    def notice_teams(self):
        appdata = {"msgtype": "text", "text": {"content": "资源位:" + self.content},
                   "at": {"isAtAll": "true", "userIds": []}}
        alarm_url = "https://im.360teams.com/api/qfin-api/rce-app/robot/send?access_token=0928611ca7964f91b622ce1fb805ec593be249de99834d9eaa372990241ad265"
        result = requests.post(alarm_url, json=appdata)
        print(result)

    def voice_notice(self, content):
        mobile_list = "13764753037,18916512909,15221280416,18217412510"
        headers = {'Content-Type': 'application/json'}
        data = {"content": content, "alarm_list": mobile_list, "invalidSecond": 10}
        print(data)
        safeLyccPostUrl = "http://10.160.132.158:5000/tomq_by_voice"
        r = requests.post(safeLyccPostUrl, data=json.dumps(data), headers=headers, verify=False).json()
        print(json.dumps(r, ensure_ascii=False))


if __name__ == '__main__':
    type = sys.argv[1]
    message = sys.argv[2]
    alarm = Alarm(message)
    if type == "sms":
        alarm.sendTagSMS()
    else:
        alarm.sendTagMail()
