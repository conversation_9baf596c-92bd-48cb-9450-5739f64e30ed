#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : MysqlConfig.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2020-08-17
-- description   : 
'''
import sys
sys.path.append("/home/<USER>/workdir/lijiacheng")
from utils.MysqlUtils import MysqlUtils

class MysqlConfig:

    @staticmethod
    def dataCenterMysql():
        host = "************"
        port = 3171
        user = "jt_dw"
        db = "jt_dw"
        passwd = "0f0aa2de4ce3d8b3"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def labelMysql():
        host = "pro-4958-rw.mysql.daikuan.qihoo.net"
        port = 4958
        user = "jt_label"
        db = "jt_label"
        passwd = "520124ef2ddcfd69"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def testLabelMysql():
        host = "************"
        port = 2481
        user = "jt_label"
        db = "jt_label"
        passwd = "520124ef2ddcfd69"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def integrationMysql():
        host = "*************"
        port = 20417
        user = "ipss_r1"
        db = "ipss"
        passwd = "05e1b4fb23727217"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def clickhouseMysql():
        host = "*************"
        port = 20854
        user = "jt_dw_dev"
        db = "jt_dw_dev"
        passwd = "331dae205006c3cf"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def testintegrationMysql():
        host = "************"
        port = 2052
        user = "ipss_stg3"
        db = "ipss_stg3"
        passwd = "68a548ed3298a6cb"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils
