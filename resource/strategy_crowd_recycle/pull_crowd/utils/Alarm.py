#!/usr/bin/python
#coding:utf-8

import sys
import requests

class Alarm():
    def __init__(self,content,app_content):

        self.title = "奇富科技数据任务监控报警"
        self.teams = "360creditDcplat"
        self.appkey = "creditdcplat"
        self.appsecret = "95CDEAF62D4BFBFF0A0BB20F12A36A71"
        self.url="http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"
        self.content = content
        self.app_content = app_content

    def sendMail(self):

        appdata={"teams":self.teams,"title":self.title,"content":self.content,"app_content":self.app_content}
        response = requests.post(self.url,data=appdata,auth=(self.appkey,self.appsecret))

    def sendTagMail(self):
        title = "360标签系统"
        teams = "digital_intelligence_email"
        appdata={"teams":teams,"title":title,"content":self.content,"app_content":self.app_content}
        response = requests.post(self.url, data=appdata, auth=(self.appkey, self.appsecret))

    def sendTagSMS(self):
        title = "360标签系统"
        teams = "digital_intelligence_sms"
        appdata = {"teams": teams, "title": title, "content": self.content, "app_content": self.app_content}
        response = requests.post(self.url, data=appdata, auth=(self.appkey, self.appsecret))


def main(content,appcontent,type):
    alram = Alarm(content,appcontent)
    if type == "label_sms":
        alram.sendTagSMS()
    elif type == "label_email":
        alram.sendTagMail()
    else:
        alram.sendMail()

if  __name__ == "__main__":
    content = sys.argv[1]
    type = sys.argv[2]
    main(content,content,type)
