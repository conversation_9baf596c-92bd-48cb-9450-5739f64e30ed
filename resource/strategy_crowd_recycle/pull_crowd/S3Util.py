#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : S3Util.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2020-10-10
-- description   : 
'''
import sys
import os
import configparser
import boto3
import subprocess
sys.path.append("/home/<USER>/workdir/lijiacheng")
from utils.MysqlConfig import MysqlConfig
from botocore.client import Config

class S3Util:
    def __init__(self):
        self.cf = configparser.ConfigParser()
        self.cf.read(os.path.dirname(os.path.realpath(__file__)) + "/../config/s3.conf")
        self.service = self.cf.get("s3Config", "service")
        self.endpoint_url = self.cf.get("s3Config", "endpoint_url")
        self.aws_secret_access_key = self.cf.get("s3Config", "aws_secret_access_key")
        self.aws_access_key_id = self.cf.get("s3Config", "aws_access_key_id")
        self.bucket_name = self.cf.get("s3Config", "bucket_name")
        self.client = self.getS3conn()

    def getS3conn(self):
        client = boto3.client('s3', self.service,
                              config=Config(signature_version='s3v4'), use_ssl=False,
                              endpoint_url=self.endpoint_url,
                              aws_secret_access_key=self.aws_secret_access_key,
                              aws_access_key_id=self.aws_access_key_id)

        return client



    def downloadFlie(self,fileName):
        file = os.path.dirname(os.path.realpath(__file__)) + "/data/" + fileName
        try:
            with open(file,"wb") as data:
                self.client.download_fileobj(self.bucket_name, fileName, data)
            data.close()
        except Exception as e:
            print(fileName)
            subprocess.getstatusoutput("rm -f %s" % file)
            return e

