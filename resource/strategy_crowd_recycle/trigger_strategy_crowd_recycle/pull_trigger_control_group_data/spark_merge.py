#!/usr/bin/python2.6
# -*- coding: utf-8 -*-

'''
-- file name     : spark_merge.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2018-10-30
-- copyright     : @qctx
-- function list : join人群包两层目录
-- history       : 第一版脚本，无历史
'''

import json
import sys
import os
reload(sys)
sys.setdefaultencoding("utf-8")
os.environ['PYSPARK_PYTHON'] = 'python27/python27/bin/python2.7'
from pyspark.sql import *
from pyspark import SparkConf,SparkContext,Row,SQLContext

from pyspark.sql.functions import *
conf = SparkConf().setMaster("yarn-client").setAppName("lijiacheng_pyspark_merge_contrast_group_data" )
sc = SparkContext(conf=conf)
sqlContext = HiveContext(sc)

def _get_path_dataframe(onepath,outputPath):
    one_data=sc.textFile(onepath,use_unicode=False).map(lambda line:line.split("\t"))
    onedata001=sqlContext.createDataFrame(one_data.map(lambda p:Row(userno=p[0],ruleno=p[1],batch_number=p[2])))
    onedata001.registerTempTable("tableA")

    sql="""
     select userno,concat_ws(',',collect_set(ruleno)) as ruleno,concat_ws(',',collect_set(batch_number)) as batch_number from tableA where userno !='' and length(userno)>1 and userno is not null and ruleno !='' and ruleno is not null and batch_number !='' and batch_number is not null group by userno
    """

    dataFrame=sqlContext.sql(sql)

    dataFrame.rdd.repartition(10).map(lambda p:p.userno+"\t"+p.ruleno+"\t"+p.batch_number).saveAsTextFile(outputPath)

if __name__ == '__main__':
        onepath=sys.argv[1]
        outputPath=sys.argv[2]
        f=_get_path_dataframe(onepath,outputPath)
        print f



