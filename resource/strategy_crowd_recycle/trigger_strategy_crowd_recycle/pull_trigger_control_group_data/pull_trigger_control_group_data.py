#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : pull_trigger_control_group_data.py
-- version       : v1.0
-- date          : 2021/9/3 17:05
-- description   : 
'''
import sys
import datetime
import redis
import subprocess
import os
import time
sys.path.append("/hdpData1/hdp-jinrong-loan/workdir/j-wangshaowu-jk")
from utils.MysqlConfig import MysqlConfig
from utils.Alarm import Alarm
from os import path

class PullControlGroupData:
    def __init__(self):
        self.thedate = sys.argv[1]
        self.format_thedate = datetime.datetime.strptime(sys.argv[1], "%Y%m%d").strftime("%Y-%m-%d")
        self.script_path = path.dirname(path.realpath(__file__))
        self.hadoop = "/usr/hdp/*******-315/hadoop/bin/hadoop"
        self.safe_path = f"hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/label_pull_trigger_control_group_data_temp/pday={sys.argv[1]}"

    def alarm(self, content):
        alarm = Alarm(content)
        print(content)
        alarm.sendTagSMS()

    def pikaInit(self):
        # host = "*************"
        host = "pro-5961-rw.redis.daikuan.qihoo.net"
        port = 5961
        psw = "050548524bdff5bb"
        pool = redis.ConnectionPool(host=host, port=port, db=0, password=psw)
        connect = redis.StrictRedis(connection_pool=pool)
        return connect

    def delete_data(self):
        os.system(f"rm -f {self.script_path}/data/*")
        os.system(f"{self.hadoop} fs -rmr {self.safe_path}")
        os.system(f"{self.hadoop} fs -mkdir {self.safe_path}")

    def query_task(self):
        sql = f"""
            select 
                concat(strategy_id,'_',task_id) as batch_number,strategy_id,crowd_fileids
            from fa_plan_task
            where date(send_start_time) = '{self.format_thedate}' and crowd_fileids like 'Tri%'
        """
        print(sql)
        mysql = MysqlConfig.labelMysql()
        tasks = mysql.selectValues(sql)
        return tasks

    def download_file(self, tasks):
        conn = self.pikaInit()
        user_list = []
        rows = 0
        file_number = 1
        for task in tasks:
            batch_number, strategy_id, crowd_fileids = task
            for fileid in crowd_fileids.split(","):
                print(f"下载 {fileid}")
                user_data = conn.lrange(fileid, 0 ,-1)
                if user_data is None:
                    continue
                for user_no in user_data:
                    user_list.append(f"{user_no.decode()}\t{strategy_id}\t{batch_number}\n")
                    rows += 1
                    if rows == 4500000:
                        with open(f"{self.script_path}/data/{str(file_number)}", "w") as file:
                            file.write("".join(user_list))
                            print(f"文件{str(file_number)}写入{str(rows)}行数据")
                            file_number += 1
                            file.close()
                            user_list.clear()
                            rows = 0

        with open(f"{self.script_path}/data/{str(file_number)}", "w") as file:
            file.write("".join(user_list))
            print(f"最终文件{str(file_number)}写入{str(rows)}行数据")
            file.close()

    def upload_file(self):
        cmd = f"{self.hadoop} fs -put -f {self.script_path}/data/* {self.safe_path}"
        print(cmd)
        for i in range(4):
            os.system(f"{self.hadoop} fs -rm {self.safe_path}/*")
            status, output = subprocess.getstatusoutput(cmd)
            if status == 0:
                print("put成功")
                break
            elif i < 3:
                time.sleep(30)
                print("put失败，sleep 30s")
                print(output)
            else:
                print(output)
                raise Exception("上传文件至毓数集群失败")

    def run_task(self):
        tasks = self.query_task()
        self.delete_data()
        self.download_file(tasks)
        self.upload_file()

if __name__ == '__main__':
    pull = PullControlGroupData()
    pull.run_task()
