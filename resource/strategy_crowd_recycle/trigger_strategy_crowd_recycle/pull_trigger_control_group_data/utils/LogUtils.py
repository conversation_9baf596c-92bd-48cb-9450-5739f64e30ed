#!/usr/bin/python
#coding:utf-8
__author__ = 'lijiacheng'
'''
-- file name     : LogUtils.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2019-12-11
-- copyright     : @qctx
-- description   : 程序日志输出
-- function list : 程序日志输出
-- history       : 第一版，无历史
'''

import logging
from logging.handlers import TimedRotatingFileHandler

class logUtils:
    def __init__(self,logdir):
        self.logdir = logdir
        info_handle = logging.handlers.TimedRotatingFileHandler(filename=self.logdir + "Info.log",  when='midnight', encoding="utf-8")
        info_format = logging.Formatter(fmt="%(asctime)s - %(filename)s:%(lineno)s  - %(message)s")
        info_handle.setFormatter(info_format)
        self.infoLog = logging.Logger(name="info", level=logging.INFO)
        self.infoLog.addHandler(info_handle)

