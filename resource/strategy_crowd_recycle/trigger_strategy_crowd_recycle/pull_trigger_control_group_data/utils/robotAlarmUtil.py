#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : robotAlarmUtil.py
-- version       : v1.0
-- date          : 2022/4/8 17:56
-- description   : 
'''
import pika
import json
import socket


class RobotAlarmUtil:
    def __init__(self):
        self.queue = "robot_alarm"
        self.connect()

    def connect(self):
        user = "j-zhoujiuan-jk"
        password = "zhoujiuan0"
        hostname = "**************"
        port = 5672
        credentials = pika.PlainCredentials(user, password)
        self.connect = pika.BlockingConnection(pika.ConnectionParameters(hostname, port, credentials=credentials))
        self.channel = self.connect.channel()
        self.channel.queue_declare(queue=self.queue, durable=True)

    def formatData(self, robot_id, content):
        message = {}
        message["hostname"] = socket.gethostname()
        message["robot_id"] = robot_id
        message["content"] = content
        return json.dumps(message)

    def push(self, robot_id, content):
        message = self.formatData(robot_id, content)
        self.channel.basic_publish(exchange='', routing_key=self.queue, body=message)
        self.channel.close()

if __name__ == '__main__':
    RobotAlarmUtil().push(1,"测试")
