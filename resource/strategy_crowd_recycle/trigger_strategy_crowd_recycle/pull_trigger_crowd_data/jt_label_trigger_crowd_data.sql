insert overwrite table credit_data.jt_label_trigger_crowd_data partition(pday = '${yesterday_p}')
select
    user_no,
    concat_ws(',',collect_set(new_strategy_no)) as strategy_no,
    concat_ws(',',collect_set(new_batch_number)) as batch_number
from (
    select
        user_no,new_strategy_no,new_batch_number
    from (
        select user_no,strategy_no,batch_number
        from credit_data.jt_label_trigger_crowd_data
        where pday = '${yesterday_p}'
    )lateral view explode(split(strategy_no,',')) strategy_no as new_strategy_no
    lateral view explode(split(batch_number,',')) batch_number as new_batch_number

    union all

    select
        user_no,
        strategyNo,
        batchNumber
    from (
        select
            body,
            split(get_json_object(body, '$.batchNumber'),'_')[0] as strategyNo,
            concat_ws('_',split(get_json_object(body, '$.batchNumber'),'_')[0],split(get_json_object(body, '$.batchNumber'),'_')[1]) as batchNumber,
            get_json_object(body, '$.conditionType') as conditionType,
            case when userType_0 = 0 then userNoList_0
                 when userType_1 = 0 then userNoList_1
                 else null end as userlist
        from (
            select
                body,
                get_json_object(body, '$.userList[0].userNoList') as userNoList_0,
                get_json_object(body, '$.userList[1].userNoList') as userNoList_1,
                get_json_object(body, '$.userList[0].userType') as userType_0,
                get_json_object(body, '$.userList[1].userType') as userType_1
            from (
                select
                    get_json_object(replace(replace(replace(content,'\\"','"'),'"{','{'),'}"','}'),'$.body') as body
                from ods.prd_kafka_sync_digital_strategy_touch_result_hourly
                where pday = '${yesterday_p}' and content like '%UR%'
            )
        ) where case when userType_0 = 0 then userNoList_0 when userType_1 = 0 then userNoList_1 else null end is not null
                and get_json_object(body, '$.conditionType') = 3
    ) lateral view explode(split(replace(substring(userlist,2,length(userlist)-2),'"',''),',')) userlist as user_no
) group by user_no
