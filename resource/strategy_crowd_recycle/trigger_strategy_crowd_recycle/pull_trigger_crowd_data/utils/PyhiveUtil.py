#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : PyhiveUtil.py
-- version       : v1.0
-- date          : 2022/5/23 12:33
-- description   : 
'''
import sys
from pyhive import hive

class PyhiveUtil:
    def __init__(self, host, port):
        self.conn = hive.connect(host=host, port=int(port))

    def selectValues(self, sql):
        cur = self.conn.cursor()
        cur.execute(sql)
        info = cur.fetchall()
        cur.close()
        return info

    def executeSql(self, sql):
        try:
            cur = self.conn.cursor()
            cur.execute(sql)
        except Exception as e:
            print(e)
            cur.close()

    def closeDb(self):
        try:
            if self.conn:
                self.conn.close()
        except Exception as e:
            print(e)
