#!/bin/bash
# file name     : jt_label_trigger_crowd_data.sh
# author        : lijiacheng
# version       : v1.0
# date          : 2020-09-08
# description   :将一体化触发式数据填充到回收表中
#source /home/<USER>/workdir/lijiacheng/utils/labelCheckSource.sh
scriptDir=./pull_trigger_crowd_data/
python=/bin/python3.6
hadoop=/usr/hdp/*******-315/hadoop/bin/hadoop

checkTime(){
    if [ $# -ne 3 ]; then
        echo -e "invalid arguments, usage: \n\tcheckdone donefile checktimes sleepseconds mailto"
        return 1;
    fi

    donefiles=$1
    checktimes=$2
    sleepseconds=$3
    counttimes=0

    for donefile in $donefiles; do
        while ! $hadoop fs -ls "$donefile" 2> /dev/null | grep ${today} > /dev/null 2>&1 && [ $counttimes -lt $checktimes ]; do
            echo "`date '+%Y%m%d %H:%M:%S'` - donefile[$donefile] not exists, sleep $sleepseconds($counttimes/$checktimes)..."
            sleep $sleepseconds
            let counttimes++
        done
        if [ $counttimes -ge $checktimes ]; then
            location=""
            subject="subject: donefile not exists"
            message="message:$0: donefile[$donefile] not exists for $checktimes times checks"
            send_sms "$message"
            echo -e "  $location \n  $subject \n  $message"
            return 1
        else
            echo "`date '+%Y%m%d %H:%M:%S'` - donefile[$donefile] exists..."
        fi
    done
}

send_sms(){

      url="http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send"

      appkey="creditdcplat"
      appsecret="95CDEAF62D4BFBFF0A0BB20F12A36A71"

      teams="digital_intelligence_sms" #接受报警的组,多个逗号分隔
      title="数智平台数据依赖监控报警" #标题
      content="$1"
      app_content="$1"

      echo `curl -X POST ${url} -u ${appkey}:${appsecret} -d "teams=${teams}&title=${title}&content=${content}&app_content=${app_content}"`
}

thedate=${globalYesterday}

if ! test $thedate;then
     thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi

outputpath=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/jt_label_trigger_crowd_data/pday=${thedate}
#file=${outputpath}/_TEMPSUCCESS

#checkTime "$file" 720 60 || exit

#spark  jt_label_trigger_crowd_data.sql

if [ $? -eq 0 ];then
    #${hadoop} fs -touchz /home/<USER>/hive/warehouse/credit_data.db/jt_label_trigger_crowd_data/pday=${thedate}/_DONE
	${hadoop} fs -mkdir -p ${outputpath}
	${hadoop} fs -touchz ${outputpath}/_DONE
fi

delthedate=$(/bin/date -d "-5day" +"%Y%m%d")
rm -f ${scriptDir}/logs/${delthedate}_jt_label_trigger_crowd_data.log

exit $?



