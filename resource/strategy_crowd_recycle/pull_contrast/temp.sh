#!/bin/bash
# file name     : pull_data.sh
# author        : lijiacheng
# version       : v1.0
# date          : 2020-09-08
# description   :
#set -x
scriptDir=/home/<USER>/workdir/lijiacheng/_360_jinrong_realtime_label/pull_contrast_group_safe3
python=/home/<USER>/workdir/lijiacheng/python3/bin/python3.6



/bin/bash ${scriptDir}/pull_data.sh 20211001
/bin/bash ${scriptDir}/pull_data.sh 20211002
/bin/bash ${scriptDir}/pull_data.sh 20211003
/bin/bash ${scriptDir}/pull_data.sh 20211004
/bin/bash ${scriptDir}/pull_data.sh 20211005
/bin/bash ${scriptDir}/pull_data.sh 20211006
/bin/bash ${scriptDir}/pull_data.sh 20211007
/bin/bash ${scriptDir}/pull_data.sh 20211008
/bin/bash ${scriptDir}/pull_data.sh 20211009
/bin/bash ${scriptDir}/pull_data.sh 20211010
/bin/bash ${scriptDir}/pull_data.sh 20211011
/bin/bash ${scriptDir}/pull_data.sh 20211012
/bin/bash ${scriptDir}/pull_data.sh 20211013
/bin/bash ${scriptDir}/pull_data.sh 20211014
/bin/bash ${scriptDir}/pull_data.sh 20211015
/bin/bash ${scriptDir}/pull_data.sh 20211016
/bin/bash ${scriptDir}/pull_data.sh 20211017
/bin/bash ${scriptDir}/pull_data.sh 20211018
/bin/bash ${scriptDir}/pull_data.sh 20211019
/bin/bash ${scriptDir}/pull_data.sh 20211020
/bin/bash ${scriptDir}/pull_data.sh 20211021
/bin/bash ${scriptDir}/pull_data.sh 20211022
/bin/bash ${scriptDir}/pull_data.sh 20211023
/bin/bash ${scriptDir}/pull_data.sh 20211024
/bin/bash ${scriptDir}/pull_data.sh 20211025
/bin/bash ${scriptDir}/pull_data.sh 20211026
/bin/bash ${scriptDir}/pull_data.sh 20211027
/bin/bash ${scriptDir}/pull_data.sh 20211028
/bin/bash ${scriptDir}/pull_data.sh 20211029
/bin/bash ${scriptDir}/pull_data.sh 20211030
/bin/bash ${scriptDir}/pull_data.sh 20211031

