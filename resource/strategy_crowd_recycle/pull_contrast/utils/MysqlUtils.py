#!/usr/bin/python
# -*- coding: utf-8 -*-

__author__ = 'jinjing'
'''
-- file name     : MysqlUtils.py
-- author        : jinjing
-- version       : v1.0
-- date          : 2019-12-05
-- copyright     : @qctx
-- description   : mysql工具类
-- history       : 第一版，无历史
'''

import pymysql
import sys
import os

class MysqlUtils:
    def __init__(self,host,port,user,passwd,db):
        self.conn = pymysql.connect(
            host = host,
            port = int(port),
            user = user,
            passwd = passwd,
            db = db,
            charset="utf8",
            local_infile=1
        )

    def selectValues(self,sql):
        cur = self.conn.cursor()
        info = cur.fetchmany(cur.execute(sql))
        cur.close()
        self.conn.commit()
        return info

    def insertValues(self,sql,values):

        try:
            cur = self.conn.cursor()
            cur.executemany(sql,values)
            self.conn.commit()
        except Exception as e:
            print(e)
            self.conn.rollback()
            cur.close()

    def executeSql  (self,sql):

        try:
            cur = self.conn.cursor()
            cur.execute(sql)
            self.conn.commit()
        except Exception as e:
            print (e)
            self.conn.rollback()
            cur.close()

    def updateValus(self,sql,values):
        try:
            cur = self.conn.cursor()
            cur.execute(sql % values)
            self.conn.commit()
            cur.close()
        except Exception as e:
            print (e)
            self.conn.rollback()
            cur.close()

    def closeDb(self):
        try:
            if self.conn:
                self.conn.close()
        except Exception as e:
            print (e)


if __name__ == "__main__":
    op = MysqlUtils("10.202.5.136",3171,"jt_dw","0f0aa2de4ce3d8b3","jt_dw")
    sql = "SELECT * FROM fin_meta_transportfile_job_list"
    line=op.selectValues(sql)
    print (line)
