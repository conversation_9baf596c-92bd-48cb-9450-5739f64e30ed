from hashlib import md5
from typing import List

from pymeter.api.samplers import BaseSampler


class JdbcSimpler(BaseSampler):
    def __init__(self, name: str, url: str, *children) -> None:
        """

        Args:

            name (str): name to be displayed in reports

            url (str): Full http\\s url (e.g - https://postman-echo.com/get)
        """
        self._http_sampler_instance = BaseSampler.jmeter_class.jdbcSample(name, url)

        super().__init__(*children)


class Solution:
    def compress(self, chars: List[str]) -> int:
        n = len(chars)
        j = 0
        cnt = 1
        for i in range(n):
            if i == n - 1 or chars[i] != chars[i + 1]:
                chars[j] = chars[i]
                j += 1
                if cnt > 1:
                    for k in str(cnt):
                        chars[j] = k
                        j += 1
                cnt = 1
            else:
                cnt += 1
        return j


if __name__ == '__main__':
    s = Solution()
    strings = ["a", "b", "b", "b", "b", "b", "b", "b", "b", "b", "b", "b", "b"]
    print(s.compress(strings))
    print(strings)
    md_5 = md5()
    md_5.update(b"eyJ0eXAiOiJKV1QiLCJhbGciOiJTSEEtMjU2In0.eyJVc2VyIjoiYWRtaW4iLCJjbGlvTmFtZSI6ImFkbWluIiwiSWQiOjEsIlN1cGVyQWRtaW4iOnRydWV9.BqFsV8yojXF0I3xsqPJbSfaof6l3YqJwqDLKVfJQgZQ")


    print( md_5.hexdigest())