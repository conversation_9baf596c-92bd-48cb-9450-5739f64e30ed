#  1.splitCrowdRule：人群包拆分项目
### 1.1 splitCrowdDataHourlyDoris.sh 当天新建的拆分包任务
### 1.2 splitCrowdDataDailyDoris.sh  按天例行的拆分包
### 1.3 splitCrowdDefinedDataDaily.sh 按天自定义人群包拆分
### 1.4 splitCrowdDefinedDataHourly.sh 当天自定义人群包拆分


# -----------------------------------------

# 2.hdfsCrowdGroupLabelDoris 人群包组合
### 2.1 jietiaoCombCrowdDoris.sh 人群包组合

# -----------------------------------------

# 3. hdfsCrowdAutoHours 

### 3.1 hdfsCrowdAutoHoursDoris.sh  单天新建人群包手动刷新上送s3

# -----------------------------------------

# 4. hdfsCrowdAutoDaily

### 4.1 1_hdfsCrowdAutoDailyDoris_download.sh 、 2_autoDaily_MergeJieTiaoDone.sh、 3_autoDaily_sparkMerge 例行人群包到期上送s3


--- 
# 调整写法 参照  jietiaoCombCrowdDoris.sh
`
python运行环境初始化：

source /home/<USER>/anaconda_yushu_jds/bin/activate py36
pythonPath=python3

------
spark3环境初始化
py_env=py37_base
spark_env=spark3
source /home/<USER>/anaconda_data_ai/ocenv ${py_env} ${spark_env}

`
