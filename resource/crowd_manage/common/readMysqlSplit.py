#!/usr/bin/python2.6
# -*- coding: utf-8 -*-

"""
-- file name     : readMysqlSplit.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2019-03-25
-- copyright     : @qctx
-- function list : 读取mysql进行分割文件
-- history       : 第一版脚本，无历史
"""
import os
import subprocess
import sys
import time
from importlib import reload

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.sendSmsMail import SendMailSms

reload(sys)
from utils.MysqlConfig import MysqlConfig
from hdfsPostLabelUploadS3 import postHdfs2s3


class ReadMysqlCrowdData(object):
    '''
    step1:readHdfsPathData 解析配置文件获取父Id人群编号
    step2:readMysql 根据配置文件循环获取子ID编号和比例
    '''

    def __init__(self, configPath, configFile, hourdate, inputPath, outputPath, localpath, parentScriptDir, splitCycle):
        self.configPath = configPath
        self.configFile = configFile
        self.hourdate = hourdate
        self.inputPath = inputPath
        self.outputPath = outputPath
        self.localpath = localpath
        self.parentScriptDir = parentScriptDir
        self.splitCycle = splitCycle

    def readHdfsPathData(self):

        configFileName = self.configPath + self.configFile
        f = open(configFileName)
        for line in f:
            crowdNo = line.split("@@@")[0]
            print(crowdNo)
            self.readMysql(crowdNo)

    def readMysql(self, parent_crowdNo):
        resultList = []
        print("parent_crowdNo=", parent_crowdNo)
        if self.splitCycle == "daily":
            sql = '''
            select distinct scale,number from fa_bag_info where  parent='%s' and status=1  and split_status not in (2,3) and sync_end_time>=CURDATE() and flush_rate='day' and  version='v3'
              ''' % (parent_crowdNo)
            print(sql)
        else:
            sql = '''
            select distinct scale,number from fa_bag_info where  parent='%s' and status=1  and split_status not in (1,2,3) and  version='v3'
            ''' % (parent_crowdNo)
            print(sql)
        mysql = MysqlConfig().jtlabelMysql()
        select = mysql.selectValues(sql)
        mysql.closeDb()
        ##每循环一次把规则带入spark并按照比例拆分数据
        for scale, child_crowdNo in select:
            updateSql = '''update fa_bag_info set split_status='3' where status=1  and number='%s' and  version='v3' ''' % (
                child_crowdNo)
            mysql = MysqlConfig().jtlabelMysql()
            mysql.executeSql(updateSql)
            mysql.closeDb()
            resultList.append(parent_crowdNo + "@@@" + str(scale) + "@@@" + str(child_crowdNo))
        if len(select) > 0:
            self.executeSparkCommand(",".join(resultList))
        else:
            cowdList = resultList
            for x in cowdList:
                child_crowdNo = x.split("@@@")[2]
                postHdfs2s3().reponseErro(child_crowdNo, "3")
            content = "标签人群包拆分异常%s时间段:有配置文件,无mysql数据，请检查标签系统配置库中的数据生成情况!" % (
                self.hourdate)
            SendMailSms(content).getAppData()

    '''
    执行spark程序并后台执行
    '''

    def executeSparkCommand(self, resultList):
        num = 0
        for x in range(3):
            cowdList = resultList.split(",")
            for x in cowdList:
                child_crowdNo = x.split("@@@")[2]
                hdpcmd = "hadoop fs -rm -r %s%s" % (self.outputPath, child_crowdNo)
                status, output = subprocess.getstatusoutput(hdpcmd)
                print(hdpcmd, output)
            PY_ZIP = "hdfs:///user/hive/warehouse/fin_dm_data_ai.db/dm_eco_hdfs_common/pyenv/base37_venvs.tar.gz#pyenv"

            sparkCommand = '''py_env=py37_base;spark_env=spark3; source /home/<USER>/anaconda_data_ai/ocenv ${py_env} ${spark_env}; /usr/hdp/*******-315/spark3.3.2/bin/spark-submit --num-executors 400 --executor-memory 4g   --driver-memory 1g --queue yushu_offline_high --conf spark.yarn.dist.archives=%s %s/splitCrowdRule/spark_splitCrowd_rate.py %s %s %s %s''' \
                           % (PY_ZIP, self.parentScriptDir, self.inputPath, self.hourdate, resultList, self.outputPath)
            # sparkCommand = '''/usr/hdp/*******-315/spark3.3.2/bin/spark-submit --num-executors 400 --executor-memory 4g   --driver-memory 1g --queue yushu_offline_high  %s/splitCrowdRule/spark_splitCrowd_rate.py %s %s %s %s''' \
            #                    % ( self.parentScriptDir, self.inputPath, self.hourdate, resultList, self.outputPath)
            status, output = subprocess.getstatusoutput(sparkCommand)
            print(sparkCommand)
            print("spark-submit result", status, output)
            if status != 0:
                time.sleep(60)
                content = "资源位-标签人群包拆分异常%s时间段:spark执行失败，请检查资源使用情况,程序目前处于重试状态中!" % (
                    self.hourdate)
                SendMailSms(content).notice_teams()
                num += 1
                if num == 2:
                    cowdList = resultList.split(",")
                    for x in cowdList:
                        child_crowdNo = x.split("@@@")[2]
                        postHdfs2s3().reponseErro(child_crowdNo, "2")
                    raise Exception("标签人群包拆分异常")
            else:
                self.getMergeData(resultList)
                break

    '''
    获取数据并请求接口
    '''

    def getMergeData(self, resultList):
        import uuid
        cowdList = resultList.split(",")
        for x in cowdList:
            chidName = x.split("@@@")[2]
            fileName = chidName + "_" + str(uuid.uuid1()).replace("-", "")
            outpath = self.outputPath + chidName
            localchildPath = self.localpath + chidName + "/" + fileName
            getMergeCmd = "rm -f %s; hadoop fs -getmerge %s %s;sed -i '1i\\user_no' %s" % (
                localchildPath, outpath, localchildPath, localchildPath)
            print(getMergeCmd)
            status, output = subprocess.getstatusoutput(getMergeCmd)
            print(getMergeCmd, output)
            if status == 0:
                countNumCmd = "cat %s|wc -l" % (localchildPath)
                status, output = subprocess.getstatusoutput(countNumCmd)
                if int(output) > 0:
                    num = int(output) - 1
                else:
                    num = 0

                exestatus = postHdfs2s3().post_labelsysHdfs_(localchildPath, chidName, "30", "2", num, fileName)
                print(localchildPath, chidName, "30", "2", num, fileName)
                print(exestatus)
                if exestatus != 'success':
                    content = "资源位-标签人群包拆分异常%s时间段:请求人群包接口异常,请检查文件上传情况并且通知标签系统前端开发人员!" % (
                        self.hourdate)
                    SendMailSms(content).notice_teams()

            else:
                postHdfs2s3().reponseErro(chidName, "3")
                content = "资源位-标签人群包拆分异常%s时间段:get文件下载到本地失败，程序正在重试!" % (self.hourdate)
                SendMailSms(content).notice_teams()


if __name__ == '__main__':
    configPath = sys.argv[1]
    configFile = sys.argv[2]
    hourdate = sys.argv[3]
    inputPath = sys.argv[4]
    outputPath = sys.argv[5]
    localpath = sys.argv[6]
    parentScriptDir = sys.argv[7]
    splitCycle = sys.argv[8]
    ReadMysqlCrowdData(configPath, configFile, hourdate, inputPath, outputPath, localpath, parentScriptDir,
                       splitCycle).readHdfsPathData()
