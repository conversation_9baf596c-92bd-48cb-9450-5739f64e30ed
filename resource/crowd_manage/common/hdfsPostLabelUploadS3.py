#!/usr/bin/python2.6
# -*- coding: utf-8 -*-
import json
import os
import sys
from importlib import reload

import requests

basedir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(basedir)
from utils.s3ClientUtil import S3Util

reload(sys)


class postHdfs2s3(object):
    '''
    获取hdfs上数据并且merge到本地数据
    '''

    def __init__(self, combine=False):
        self.combine = combine
        # self.postUrl="http://10.216.2.81:10081/v2api/split/result"
        # 修改于 2021-10-14 19:08 原始值 self.postUrl="http://10.216.2.81:10081/v2api/split/result"
        # self.postUrl = "https://gws-bps.daikuan.360.cn/api/gws/bps/gateway/iops/split/result"
        # self.postUrl = "https://gws-bps.daikuan.360.cn/api/gws/bps/gateway/iops/split/result"
        self.combine = combine
        self.postUrl = "https://gws-bps.daikuan.360.cn/api/gws/bps/gateway/iops/split/result"
        if combine:
            self.postUrl = "https://gws-bps.daikuan.360.cn/api/gws/bps/gateway/iops/combine/result"

        # self.postUrl = "http://stg2-gws-bps-web.daikuan.360.cn/api/gws/bps/gateway/iops/split/result"
        # if combine:
        #     self.postUrl = "https://stg2-gws-bps-web.daikuan.360.cn/api/gws/bps/gateway/iops/combine/result"

    def post_labelsysHdfs_(self, localPathFile, crowdNo, template_days, label_type, crowdnum, fileName):
        headers = {'Content-Type': 'application/json'}
        status = 1
        if self.combine:
            status = 2
        data = {'crowdNo': '%s' % (crowdNo), 'status': status, 'count': '%s' % (crowdnum), "filename": fileName}
        print(data)
        print(localPathFile, fileName)
        S3Util(localPathFile).uploadFile(fileName)
        for num in range(4):
            r = requests.post(self.postUrl, data=json.dumps(data), headers=headers, verify=False).json()
            if "success" == r.get("msg"):
                return "success"
            elif num == 3 and "success" != r.get("msg"):
                return r.get("msg")

    def reponseErro(self, crowdNo, status):
        data = {'status': status, 'crowdNo': '%s' % (crowdNo), 'count': '', 'filename': ''}
        r = requests.post(self.postUrl, data=data).json()
        return r.get("msg")

    def returnReponseErroError(self, configFilePath):
        f = open(configFilePath)
        for line in f:
            crowdNo = line.split("@@@")[0]
            status = 2
            data = {'status': status, 'crowdNo': '%s' % (crowdNo), 'count': '', 'filename': ''}
            r = requests.post(self.postUrl, data=data)

            return r

    def postOneceReturn(self, crowdNo, count, filename):
        '''
        一次性人群包数据接口
        :param crowdNo:
        :param count:
        :param filename:
        :return:
        '''
        # testurl=" http://10.216.2.78:10081/v2api/cbapi/labelresult"
        # url="http://10.216.2.81:10081/v2api/cbapi/labelresult"
        # 修改与 2021-10-14 19:11 原始值 url="http://10.216.2.81:10081/v2api/cbapi/labelresult"
        url = "https://gws-bps.daikuan.360.cn/api/gws/bps/gateway/iops/cbapi/labelresult"
        # url = "http://stg2-gws-bps-web.daikuan.360.cn/api/gws/bps/gateway/iops/cbapi/labelresult"
        headers = {'Content-Type': 'application/json'}
        data = {'crowdNo': '%s' % (crowdNo), 'status': 2, 'count': '%s' % (count), "filename": filename}
        print(data)
        for num in range(4):
            r = requests.post(url, data=json.dumps(data), headers=headers, verify=False).json()
            if "success" == r.get("msg"):
                return "success"
            elif num == 3 and "success" != r.get("msg"):
                return r.get("msg")


if __name__ == '__main__':
    configFilePath = sys.argv[1]
    f = postHdfs2s3().returnReponseErroError(configFilePath)
