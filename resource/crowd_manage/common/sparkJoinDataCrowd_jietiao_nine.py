#!/usr/bin/python2.6
# -*- coding: utf-8 -*-

'''
-- file name     : sparkJoinDataCrowd_daily.py
-- author        : ca<PERSON>nwei
-- version       : v1.0
-- date          : 2018-10-30
-- copyright     : @qctx
-- function list : join人群包两层目录
-- history       : 第一版脚本，无历史
'''

import sys
from importlib import reload

reload(sys)
from pyspark.sql import *
from pyspark import SparkConf, Row

from pyspark.sql.functions import *

conf = SparkConf().setMaster("yarn-client").setAppName("yushu_pyspark_merge_jietiao_nine").set(
    "spark.hive.mapred.supports.subdirectories", "true").set(
    "spark.hadoop.mapreduce.input.fileinputformat.input.dir.recursive", "true")
sc = SparkContext(conf=conf)
sqlContext = HiveContext(sc)


def get_path_dataframe(cguiActivityDailyOutput, dailyOutput, definedCrowdData, resultOutputData):
    print("get_path_dataframe four args ")
    cguiActivityDailyOutputDataRDD = sc.textFile(cguiActivityDailyOutput, use_unicode=False).map(
        lambda line: line.split("\t"))
    dailyOutputDataRDD = sc.textFile(dailyOutput, use_unicode=False).map(lambda line: line.split("\t"))
    definedCrowdDataRDD = sc.textFile(definedCrowdData, use_unicode=False).map(lambda line: line.split("\t"))
    sc.getOrCreate()
    cguiActivityDailyOutputDataFrame = sqlContext.createDataFrame(
        cguiActivityDailyOutputDataRDD.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    dailyOutputDataFrame = sqlContext.createDataFrame(dailyOutputDataRDD.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    definedCrowdDataFrame = sqlContext.createDataFrame(definedCrowdDataRDD.map(lambda p: Row(userno=p[0], ruleno=p[1])))

    allDataFrame = dailyOutputDataFrame.unionAll(definedCrowdDataFrame)

    cguiActivityDailyOutputDataFrame.registerTempTable("cguiActivityDailyOutputDataTable")
    allDataFrame.registerTempTable("allDataTable")

    sql = """
            select 
              a.userno as userno
              ,case when coalesce(b.ruleno,'')='' then a.ruleno
                else concat(a.ruleno,',',coalesce(b.ruleno,''))
              end
              as ruleno  
           from 
            (select userno,concat_ws(',',collect_set(ruleno)) as ruleno 
               from cguiActivityDailyOutputDataTable where userno !='' and length(userno)>1 
                and userno is not null and ruleno !='' and ruleno is not null group by userno)a
             left join 
             (select userno,concat_ws(',',collect_set(ruleno)) as ruleno 
                from (select a.userno as userno,a.ruleno as ruleno  from allDataTable a  
              left join 
                    (select distinct  ruleno  from cguiActivityDailyOutputDataTable) b 
                       on a.ruleno=b.ruleno 
                    where b.ruleno is null)x 
              where userno !='' and length(userno)>1 and userno is not null and ruleno !='' and ruleno is not null group by userno)b 
             on a.userno=b.userno    
    """

    dataFrame = sqlContext.sql(sql)
    # dataFrame.show()

    dataFrame.rdd.repartition(10).map(lambda p: p.userno + "\t" + p.ruleno).saveAsTextFile(resultOutputData)


def get_path_dataframe_five(cguiActivityDailyOutput, dailyOutput, hoursCrowdData, definedCrowdData, resultOutputData):
    print("get_path_dataframe_five  args ")

    cguiActivityDailyOutputRdd = sc.textFile(cguiActivityDailyOutput, use_unicode=False).map(
        lambda line: line.split("\t"))
    dailyOutputRdd = sc.textFile(dailyOutput, use_unicode=False).map(lambda line: line.split("\t"))
    hoursCrowdDataRdd = sc.textFile(hoursCrowdData, use_unicode=False).map(lambda line: line.split("\t"))
    definedCrowdDataRdd = sc.textFile(definedCrowdData, use_unicode=False).map(lambda line: line.split("\t"))

    cguiActivityDailyOutputDataFrame = sqlContext.createDataFrame(
        cguiActivityDailyOutputRdd.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    dailyOutputDataFrame = sqlContext.createDataFrame(dailyOutputRdd.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    hoursCrowdDataFrame = sqlContext.createDataFrame(hoursCrowdDataRdd.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    definedCrowdDataFrame = sqlContext.createDataFrame(definedCrowdDataRdd.map(lambda p: Row(userno=p[0], ruleno=p[1])))

    allDataFrame = dailyOutputDataFrame.unionAll(hoursCrowdDataFrame).unionAll(definedCrowdDataFrame)

    cguiActivityDailyOutputDataFrame.registerTempTable("cguiActivityDailyOutputDataTable")

    allDataFrame.registerTempTable("allDataTable")

    sql = """
            select 
              a.userno as userno
             ,case when coalesce(b.ruleno,'')='' then a.ruleno
                else concat(a.ruleno,',',coalesce(b.ruleno,''))
              end
              as ruleno 
           from 
            (select userno,concat_ws(',',collect_set(ruleno)) as ruleno from cguiActivityDailyOutputDataTable where userno !='' and length(userno)>1 and userno is not null and ruleno !='' and ruleno is not null group by userno)a
             left join 
             (select userno,concat_ws(',',collect_set(ruleno)) as ruleno 
               from (select a.userno as userno,a.ruleno as ruleno  from allDataTable a  
              left join 
                    (select distinct  ruleno  from cguiActivityDailyOutputDataTable) b 
                       on a.ruleno=b.ruleno 
                    where b.ruleno is null)x 
              where userno !='' and length(userno)>1 and userno is not null and ruleno !='' 
                and ruleno is not null group by userno)b 
             on a.userno=b.userno    
   """

    dataFrame = sqlContext.sql(sql)
    # dataFrame.show()
    dataFrame.rdd.repartition(10).map(lambda p: p.userno + "\t" + p.ruleno).saveAsTextFile(resultOutputData)


if __name__ == '__main__':
    if len(sys.argv) == 5:
        cguiActivityDailyOutput = sys.argv[1]
        dailyOutput = sys.argv[2]
        definedCrowdData = sys.argv[3]
        resultOutputData = sys.argv[4]
        f = get_path_dataframe(cguiActivityDailyOutput, dailyOutput, definedCrowdData, resultOutputData)
        print(f)
    elif len(sys.argv) == 6:
        cguiActivityDailyOutput = sys.argv[1]
        dailyOutput = sys.argv[2]
        hoursCrowdData = sys.argv[3]
        definedCrowdData = sys.argv[4]
        resultOutputData = sys.argv[5]

        f = get_path_dataframe_five(cguiActivityDailyOutput, dailyOutput, hoursCrowdData, definedCrowdData,
                                    resultOutputData)
        print(f)
