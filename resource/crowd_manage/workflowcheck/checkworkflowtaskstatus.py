#!/usr/bin/python
# -*- coding: utf-8 -*-
import sys

from MysqlConfig import MysqlConfig


class CheckWorkFlow:
    """
        检查工作流是否运行成功，work_flow_name =  projectName=${jdsInnerPlatformProjectName} +"_"+ workflowName=${jdsInnerPlatformProcessName} +"_"+${system.datetime}
    """

    def __init__(self, workflowname):
        self.work_flow_name = workflowname

    def insertRecord(self, fail_msg):
        insertSql = "insert into work_flow_run_log(work_flow_name,fail_msg) value (%s,%s)"
        print(insertSql)
        mysql = None
        try:
            mysql = MysqlConfig().jtlabelMysql()
            mysql.insertValues(insertSql, [(self.work_flow_name, fail_msg)])

        except Exception as e:
            print(e)
            print("插入工作流异常出现异常")
            sys.exit(1)

    def checkStatus(self):
        sql = """select id,work_flow_name from work_flow_run_log where is_delete = 0 and  work_flow_name='%s'""" % self.work_flow_name
        print(sql)
        mysql = None
        try:
            mysql = MysqlConfig().jtlabelMysql()
            result = mysql.selectValues(sql)
            if len(result):
                # 先更新记录为删除
                update_sql = """update work_flow_run_log set is_delete = 1 where is_delete = 0 and  work_flow_name='%s'""" % self.work_flow_name
                print(update_sql)
                mysql.executeSql(update_sql)
                # 有异常，工作流失败
                return False
            return True
        except Exception as e:
            print(e)
            print("检查工作流运行是否出现异常")
            sys.exit(1)
        finally:
            if mysql:
                mysql.closeDb()


if __name__ == '__main__':
    work_flow_name = sys.argv[1]
    c = CheckWorkFlow(work_flow_name)
    result = c.checkStatus()
    print(result)
    if not result:
        sys.exit(1)
