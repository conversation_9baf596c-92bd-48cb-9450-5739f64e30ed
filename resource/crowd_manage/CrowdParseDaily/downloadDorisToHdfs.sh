#!/bin/bash
##  file name     : mainDaily.sh
##  author        : z<PERSON>yong
##  version       : v1.0
##  date          : 2022-11-02
##  copyright     : @qctx
##  description   : 全部数据解析入资源位
##  usage         : 全部数据解析入资源位
##  function list :
##  history       : 第一版脚本，无历史
# set -e

source ~/.bashrc
set -x
unzip offline_label_application_doris
echo $(hostname)
thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todayyyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
  sevenDay=$(/bin/date -d-7day "+%Y%m%d")
  threeDay=$(/bin/date -d-4day "+%Y%m%d")

fi

cwd=$(
  cd $(dirname "$0")
  cd ..
  pwd
)
source $cwd/utils/smsUtil.sh
source $cwd/utils/checkSourceUtil.sh

parentScriptDir=$cwd
localFileParentDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application_doris/resource_crowd_data
### 按日统计数据路径分区信息
#tempParentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/label_user_mapping_crowdrule_offline_daily_temp
tempParentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_label_user_mapping_crowdrule_offline_daily_temp
### 跑数据临时存储目录
#parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/temp_resource_crowd_data/
parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data/

#tableName=credit_data.label_user_mapping_crowdrule_offline_daily_temp
tableName=credit_data.doris_label_user_mapping_crowdrule_offline_daily_temp
pythonPath=/home/<USER>/anaconda2/bin/python

cycleName="daily"

inputDorisHdfs=${tempParentHdfsDir}/pday=$thedate

dorisPageSize=20000

#----------------------------导出doris数据到yushu
function readDorisPutHdfs() {
  localFilePath=${localFileParentDir}/dorisDownData
  $pythonPath ${parentScriptDir}/CrowdParseDaily/mainDaily.py ${localFilePath} ${inputDorisHdfs} ${cycleName} ${dorisPageSize}
  ##上传完毕后创建一个DONE文件
  if [ $? -eq 0 ]; then
    MergeJieTiaoDone
  else
    exit 1
  fi
}

#---------------------------创建人群包Done文件
function MergeJieTiaoDone() {
  #通过毓数节点删除分区
  hive -e "alter table $tableName drop partition(pday='$threeDay')"
  #hadoop fs -rm -r ${parentHdfsDir}/pday=$threeDay/
  hive -e "msck repair table credit_data.doris_label_user_mapping_crowdrule_offline_daily_temp"
  ##自定义hdfs parent路径，本地parent路径
  hadoop fs -touchz ${inputDorisHdfs}/_SUCCESS
}

readDorisPutHdfs
