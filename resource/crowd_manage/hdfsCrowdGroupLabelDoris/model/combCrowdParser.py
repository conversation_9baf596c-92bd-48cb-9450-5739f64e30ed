#!/usr/bin/python
# -*- coding: utf-8 -*-
'''
-- file name     : combCrowdParser.py
-- author        : ca<PERSON>n<PERSON>
-- version       : v1.0
-- date          : 2021-01-01
-- copyright     : @qctx
-- function list : 人群保组合解析.
-- history       : 第一版脚本，无历史
'''
class CombParser(object):
      def __init__(self,parserJson):
          self.parserJson=parserJson


      def ParserOldRule(self):
          return self.newJson(self.parserJson)


      def newJson(self,parserJson):
          '''
          :param bag_json:
          step1: 获取无数层的key 和value
          {"comb_4":{"comb_3":{"comb_2":{"grp_1":"N6353","grp_1_type":2,"grp_2":"N6354","grp_2_type":2,"op":"or"},"grp_3":"N6355","grp_3_type":2,"op":"or"},"grp_4":"N6356","grp_4_type":2,"op":"or"},"grp_5":"N6365","grp_5_type":2,"op":"or"}
          :return:
          '''
          if isinstance(parserJson, dict):
              for x in range(len(parserJson)):
                  temp_key = [k for k,v in parserJson.items() ]
                  temp_value=parserJson[temp_key[x]]
                  if "grp" in temp_key[x] and "_type" not in temp_key[x] :
                      yield (temp_value)
                  yield from self.newJson(temp_value)



      def ParserNewRule(self):
          '''
          step1:获取数据层的对应的key值
          {"new_crowd_c":{"comb_1":{"grp_1":["N6353,N6358"] ,"op":"or"},"comb_2":{ "grp_2":["N6545"],"op":"or" },"op":"or"}}
          :return:
          '''

          for x in self.ParserRecursion(self.parserJson):
               list1= (x.get("comb_1").get("grp_1"))
               list2= (x.get("comb_2").get("grp_2"))
               allList=list1+list2

               yield allList


      def ParserRecursion(self,parserJson):
          if isinstance(parserJson,dict):
             for key,value in parserJson.items():
                 yield (value)
                 #yield from self.ParserRecursion(value)






if __name__ == '__main__':
    bagJson={"comb_4":{"comb_3":{"comb_2":{"grp_1":"N6353","grp_1_type":2,"grp_2":"N6354","grp_2_type":2,"op":"or"},"grp_3":"N6355","grp_3_type":2,"op":"or"},"grp_4":"N6356","grp_4_type":2,"op":"or"},"grp_5":"N6365","grp_5_type":2,"op":"or"}
    x=CombParser(bagJson).ParserOldRule()
    for i in x:
        print (i)

    bagJson={"new_crowd_3":{"op":"or","comb_2":{"op":"or","grp_2":["N36185","N36186"]},"comb_1":{"op":"and","grp_1":["N36181"]}}}
    y = CombParser(bagJson).ParserNewRule()
    for c in y:
        print(c)

