#!/usr/bin/python
# -*- coding: utf-8 -*-
'''
-- file name     : writeDataFile.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2020-01-04
-- copyright     : @qctx
-- function list : 上传数据到hdfs,写入数据到本地配置
-- history       : 第一版脚本，无历史
'''
import os

class WriteDataFile(object):
      def __init__(self):
          pass



      def mkdir_crowd_dir(self,downFilePath):
          if  not os.path.exists(downFilePath):
              os.makedirs(downFilePath)


      ##dos转换为unix
      def dos_to_unix(self,fname):
              fobj=open(fname,'rb+')
              data = fobj.read()
              #data = data.replace('\r\n', '\n')
              fobj.seek(0, 0)
              fobj.truncate()
              fobj.write(data)




