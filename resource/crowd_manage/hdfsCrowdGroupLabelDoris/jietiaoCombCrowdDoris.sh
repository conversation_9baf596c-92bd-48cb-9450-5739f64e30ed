#!/bin/bash
##  file name     : jietiaoCombCrowd.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2022-12-02
##  copyright     : @qctx
##  description   : 借条业务线人群组合
##  usage         : 借条业务线人群组合
##  function list :
##  history       : 第二版脚本，参考caoyanwei第一版
# set -e

source ~/.bashrc
set -x
unzip crowd_manage_prd.zip

thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todayyyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
  sevenDay=$(/bin/date -d-7day "+%Y%m%d")
  threeDay=$(/bin/date -d-4day "+%Y%m%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M%S%N")
fi
currentDatetime="${system.datetime}"

cwd=$(
  cd $(dirname $0)
  pwd
)
# source $cwd/utils/smsUtil.sh
source $cwd/utils/alarm.sh

parentScriptDir=$cwd
localFileParentDir=$cwd
#标签人群
parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/comb_label_crowd

source /home/<USER>/anaconda_yushu_jds/bin/activate py36
pythonPath=python3

##业务线
business_type="jietiao"

##读取业务线
readEnv="online"

##读取处理业务类型
readDisposeType="crowd_composite"

##处理作业人
readDisposeName="zhangyong"


##处理周期
dispose_rel="hourly"

jobname=${readDisposeName}_${readEvn}_${readDisposeType}_${dispose_rel}

outputParent="$parentHdfsDir/${business_type}_${readEvn}/hdfsCrowdGroupLabel"

configFile="rules.conf"

groupConfigFile="groupRules.conf"

configPath=$localFileParentDir/$business_type/$readDisposeType/ruleConfig/$todaymin/

exebatch=${business_type}_${readDisposeType}_${dispose_rel}

definedHdfs=$outputParent/defined

combOutPutResult=$outputParent/combResultOutPut/
#本地
comParentScript=$parentScriptDir/hdfsCrowdGroupLabelDoris

mergeOutPutResult=$outputParent/mergeResult

outputHdfsPath=$outputParent/$readDisposeType/$todaymin

localFilePath=${localFileParentDir}/hdfsCrowdGroupLabelDoris/crowdDownloadData

dorisPageSize=10000


function combCrowdOutPut() {
  source /home/<USER>/anaconda_yushu_jds/bin/activate py36
  pythonPath=python3
  hadoop fs -rm -r $combOutPutResult/${thedate}*/
  combResultOutPutData=$combOutPutResult/$todaymin/
  ### 人群组合后输出结果
  $pythonPath "$comParentScript"/model/hdfsCombineRule.py "$parentScriptDir" "$configPath" $groupConfigFile $mergeOutPutResult/"$todaymin" "$combResultOutPutData" "$todaymin" $localFileParentDir

}

function sparkMerge() {
  # 对数据块进行merge，两类数据块 1.规则数据块  2.自定义上传数据块
  hadoop fs -rm -r $mergeOutPutResult/"$thedate"*/
  resultOutputData=$mergeOutPutResult/$todaymin/
  hadoop fs -rm -r "$resultOutputData"


  py_env=py37_base
  spark_env=spark3
  projectName=${jdsInnerPlatformProjectName}
  workflowName=${jdsInnerPlatformProcessName}
  taskName=${jdsInnerPlatformTaskName}
  export appname=${user_name}:[${spark_env}_shell:[项目名:${projectName}][流程名:${workflowName}][任务名:${taskName}]]
  PY_ZIP=hdfs:///user/hive/warehouse/fin_dm_data_ai.db/dm_eco_hdfs_common/pyenv/base37_venvs.tar.gz#pyenv
  source /home/<USER>/anaconda_data_ai/ocenv ${py_env} ${spark_env}

  definedOutputResultCount=$(hadoop fs -count $definedHdfs/$business_type/$todaymin/ | awk '{print $3}')
  definedOutputResult=$?
  if [ $definedOutputResult -ne 0 ];then
   definedOutputResultCount=0
  fi
 #如果数据块为spark2 证明存在规则包
  outputHdfsPathCount=$(hadoop fs -count $outputHdfsPath | awk '{print $3}')


  if [ $definedOutputResultCount -gt 0 ] && [ $outputHdfsPathCount -gt 0 ]; then
    /usr/hdp/*******-315/spark3.3.2/bin/spark-submit \
          --master yarn \
          --name ${appname} \
          --driver-memory 2G \
          --executor-memory 3G \
          --conf spark.dynamicAllocation.enabled=true  \
          --conf spark.dynamicAllocation.maxExecutors=15 \
          --conf spark.dynamicAllocation.minExecutors=5 \
          --conf spark.shuffle.service.name=spark3_shuffle_seg2 \
          --conf spark.shuffle.service.port=7333 \
          --conf spark.shuffle.push.enabled=true \
          --conf hive.metastore.dml.events=false \
          --conf spark.executor.memoryOverhead=1024 \
          --queue yushu_offline_high \
          --conf spark.yarn.dist.archives=${PY_ZIP} \
          $parentScriptDir/common/sparkJoinData.py \
          "$outputHdfsPath" "$definedHdfs/$business_type/$todaymin/*/*" "$resultOutputData"

  elif [ $outputHdfsPathCount -gt 0 ];then
        /usr/hdp/*******-315/spark3.3.2/bin/spark-submit \
          --master yarn \
          --name ${appname} \
          --driver-memory 2G \
          --executor-memory 3G \
          --conf spark.dynamicAllocation.enabled=true  \
          --conf spark.dynamicAllocation.maxExecutors=15 \
          --conf spark.dynamicAllocation.minExecutors=5 \
          --conf spark.shuffle.service.name=spark3_shuffle_seg2 \
          --conf spark.shuffle.service.port=7333 \
          --conf spark.shuffle.push.enabled=true \
          --conf hive.metastore.dml.events=false \
          --conf spark.executor.memoryOverhead=1024 \
          --queue yushu_offline_high \
          --conf spark.yarn.dist.archives=${PY_ZIP} \
          $parentScriptDir/common/sparkJoinData.py \
          "$outputHdfsPath"  "$resultOutputData"
  elif [ $definedOutputResultCount -gt 0 ];then
       /usr/hdp/*******-315/spark3.3.2/bin/spark-submit \
          --master yarn \
          --name ${appname} \
          --driver-memory 2G \
          --executor-memory 3G \
          --conf spark.dynamicAllocation.enabled=true  \
          --conf spark.dynamicAllocation.maxExecutors=15 \
          --conf spark.dynamicAllocation.minExecutors=5 \
          --conf spark.shuffle.service.name=spark3_shuffle_seg2 \
          --conf spark.shuffle.service.port=7333 \
          --conf spark.shuffle.push.enabled=true \
          --conf hive.metastore.dml.events=false \
          --conf spark.executor.memoryOverhead=1024 \
          --queue yushu_offline_high \
          --conf spark.yarn.dist.archives=${PY_ZIP} \
          $parentScriptDir/common/sparkJoinData.py \
          "$definedHdfs/$business_type/$todaymin/*/*" "$resultOutputData"
  fi
  sparkFlag=$?
  if [ $sparkFlag -ne 0 ]; then
    echo "spark submit fail!!!!!"
    send_notice  "资源位-人群组合提交spark失败"
    exit 1
  fi
  mergeOutputResultCount=$(hadoop fs -count $resultOutputData | awk '{print $3}')

  if [ $? -eq 0 ] && [ $mergeOutputResultCount -gt 0 ]; then
    combCrowdOutPut
  else
    echo "mergeOutputResultCount数据量为0，程序退出！！！"
    send_notice  "资源位-人群组合mergeOutputResultCount数据量为0,程序退出，如有需要，请人工核对"
    exit 0
  fi

}

function dorisConfig() {

    #获取数据库最后一个索引值
  resultConf=$(ls $configPath$configFile | wc -l)
  downloadDoris_exe=0
  if [ $resultConf -gt 0 ]; then
    $pythonPath $comParentScript/jietiaoCombCrowdDoris.py ${localFilePath} $configPath/$configFile ${outputHdfsPath}/  ${dorisPageSize}
    downloadDoris_exe=$?
  fi
  if [ $downloadDoris_exe -eq 0 ]; then
    sparkMerge spark1
  fi
}


function exeProducer() {
  rm -rf "$configPath"$configFile
  rm -rf "$configPath"$groupConfigFile
  hadoop fs -rm -r $definedHdfs/$business_type/"$thedate"*
  $pythonPath $comParentScript/MainCombCrowd.py $business_type $readEnv "$configPath" $configFile $groupConfigFile $definedHdfs/$business_type/$todaymin $comParentScript $localFileParentDir "${currentDatetime}"
  groupRuleConfig=$(ls ${configPath}${groupConfigFile} | wc -l)
  if [ "$groupRuleConfig" -eq 0 ]; then
    echo "配置文件不存在，程序退出"
  else
    dorisConfig
  fi
}
exeProducer
###执行处理脚本脚本:
#sh /home/<USER>/workdir/caoyanwei/_360_jinrong_digital_intel_mr/hdfsCrowdGroupLabel/combCrowd.sh
#echo $business_type $readEnv $readDisposeType $readDisposeName $dispose_rel $jobname $input $inputtag $smsPath $configFile $groupConfigFile $configPath $exebatch $parentHdfsDir $parentScriptDir $todaymin $outputParent $today $thedate
#
