#!/usr/bin/python
# -*- coding: utf-8 -*-
import datetime
import os
import sys
import traceback


sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from common.checkDorisSuccess import <PERSON><PERSON><PERSON>

from model.mysqlDomain import BagInfoDomain
from service.dorisDataService import writeD<PERSON>ToLocal
from utils.sendSmsMail import SendMailSms
from utils.dorisUtil import putLocalHdfsData
from model.combSqlQuery import MysqlQuery


class HdfsCombine:
    def __init__(self, file_path, config_file, hdfs_path, doris_page_size):
        self.localFilePath = file_path
        self.configFile = config_file
        self.hdfsPath = hdfs_path
        self.dorisPageSize = doris_page_size

    def readConfig(self):
        f = open(self.configFile, encoding="utf-8")
        ruleCrowdNoList = []
        crowdInfoList = []
        for line in f:
            fileRow = line.split("@@@")
            ruleCrowdNoList.append(fileRow[0])
        if ruleCrowdNoList:
            bagInfos = MysqlQuery().selectCrowdInfo(ruleCrowdNoList)
            for number, bag_json, bag_class, bag_type, version, data_mechanism in bagInfos:
                crowdInfoList.append(BagInfoDomain(number, bag_json, version, bag_type, data_mechanism, bag_class))

        return crowdInfoList

    def get_business_type_list(self, crowd_list):
        bag_class_list = [str(crowd.bagClass) for crowd in crowd_list]
        return set(bag_class_list)

    def exeRemoteCrowd(self):
        crownList = self.readConfig()
        bag_class_list = self.get_business_type_list(crownList)
        for bagclass in bag_class_list:
            CheckDoris().checkDorisSuccess(bagclass)

        for bagInfo in crownList:
            try:
                crowNo = bagInfo.number
                fileName = self.generateFileName(crowNo)
                filePath = self.generateLocalPath()
                status = writeDorisToLocal(bagInfo, filePath + fileName, True, self.dorisPageSize)
                if status == 0:
                    count = len(open(filePath + fileName, 'r').readlines())
                    if count > 0:
                        print('==================' + self.hdfsPath + "  crowd_no=" + crowNo)
                        # raise Exception(1111)
                        putLocalHdfsData(filePath, fileName, self.hdfsPath, crowNo)
                else:
                    content = "组合人群包处理异常%s:请检查写本地磁盘!!!" % crowNo
                    SendMailSms(content).notice_teams()
                    raise Exception(content)
            except Exception as e:
                print(e)
                traceback.print_exc()
                exit(1)

    def generateLocalPath(self):
        return self.localFilePath + "/"

    def generateFileName(self, crowdNo):
        fileName = crowdNo + "_" + datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".tsv"
        return fileName


if __name__ == '__main__':
    localFilePath = sys.argv[1]
    configPath = sys.argv[2]
    hourHdfsPath = sys.argv[3]
    dorisPageSize = sys.argv[4]
    HdfsCombine(localFilePath, configPath, hourHdfsPath, dorisPageSize).exeRemoteCrowd()
