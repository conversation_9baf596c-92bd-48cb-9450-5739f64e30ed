#!/usr/bin/python2.6
# -*- coding: utf-8 -*-
"""
-- file name     : splitCrowdHourlyDownLoad.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-05-11
-- copyright     : @qctx
-- function list : 拆分人群校验dori标签是否准备好
-- history       : 第一版脚本，无历史
"""
import os
import sys
import traceback
import uuid

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from service.dbSelect.mysqlDomain import BagInfoDomain
from service.dorisDataService import writeDorisToLocal
from utils.MysqlConfig import MysqlConfig
from utils.dorisUtil import putLocalHdfsData
from utils.sendSmsMail import SendMailSms


class DownLoadCrowd(object):
    def __init__(self, local_file_path, hdfs_out_put_path, config_path, doris_size):
        self.hdfsOutPutPath = hdfs_out_put_path
        self.configFile = config_path
        self.localFilePath = local_file_path
        self.dorisPageSize = doris_size

    def readConfig(self):
        f = open(self.configFile, 'r')
        ruleCrowdNoList = []
        crowdInfoList = []
        for line in f:
            fileRow = line.split("@@@")
            ruleCrowdNoList.append(fileRow[0])
        if ruleCrowdNoList:
            number = ",".join("'" + x + "'" for x in ruleCrowdNoList)
            sql = '''
                         select
                             number,bag_json,bag_class,bag_type,version,data_mechanism
                          from fa_bag_info a where a.number in (%s)
                            ''' % number
            mysql = MysqlConfig.jtlabelMysql()
            bagInfos = mysql.selectValues(sql)
            mysql.closeDb()
            for number, bag_json, bag_class, bag_type, version, data_mechanism in bagInfos:
                crowdInfoList.append(BagInfoDomain(number, bag_json, version, bag_type, data_mechanism, bag_class))

        return crowdInfoList

    def exeRemoteCrowd(self):
        crowdDomainList = self.readConfig()
        if not crowdDomainList:
            print("crowd_domain_list is empty")
            return
        for bagInfo in crowdDomainList:
            try:
                crowNo = bagInfo.number
                fileName = self.generateFileName(crowNo)
                filePath = self.generateLocalPath(crowNo)
                status = writeDorisToLocal(bagInfo, filePath + fileName, True, self.dorisPageSize)
                if status == 0:
                    count = len(open(filePath + fileName, 'r').readlines())
                    if count > 0:
                        print('==================' + self.hdfsOutPutPath + "  crowd_no=" + crowNo)
                        putLocalHdfsData(filePath, fileName, self.hdfsOutPutPath, crowNo)
                else:
                    content = "组合人群包处理异常%s:请检查写本地磁盘!!!" % crowNo
                    SendMailSms(content).notice_teams()
                    raise Exception(content)
                    # print(content)
            except Exception as e:
                print(e)
                traceback.print_exc()
                sys.exit(1)

    @staticmethod
    def generateFileName(crowNo):
        fileName = crowNo + "_" + str(uuid.uuid1()).replace("-", "")
        return fileName

    @staticmethod
    def mkdir_crowd_dir(downFilePath):
        if not os.path.exists(downFilePath):
            os.makedirs(downFilePath)

    def generateLocalPath(self, crowNo):
        localChildPath = self.localFilePath + "/" + crowNo + "/"
        self.mkdir_crowd_dir(localChildPath)
        return localChildPath


if __name__ == '__main__':
    localFilePath = sys.argv[1]
    hdfsOutPutPath = sys.argv[2]
    configPath = sys.argv[3]
    cycle = sys.argv[4]
    dorisPageSize = sys.argv[5]

    DownLoadCrowd(localFilePath + "/" + cycle, hdfsOutPutPath, configPath, dorisPageSize).exeRemoteCrowd()
