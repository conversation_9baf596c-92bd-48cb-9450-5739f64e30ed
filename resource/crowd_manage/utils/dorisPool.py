#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
-- file name     : dorisDataService.py
-- author        : z<PERSON><PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2023-03-25
-- copyright     : @qctx
-- function list : 数据服务新环境版本
-- history       : 第一版脚本，无历史
"""
import os
import sys
import traceback

from dbutils.pooled_db import PooledDB

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)

import pymysql as pymysql


class DorisPool(object):

    def __init__(self, data):
        host = data['host']
        port = data['port']
        user = data['user']
        password = data['password']
        database = data['database']
        max_connections = data['max_connections']
        self._pool = PooledDB(
            creator=pymysql,  # 使用链接数据库的模块
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8',
            autocommit=True,
            maxconnections=max_connections,  # 连接池允许的最大连接数，0和None表示不限制连接数
            mincached=0,  # 初始化时，链接池中至少创建的空闲的链接，0表示不创建
            maxcached=5,  # 链接池中最多闲置的链接，0和None不限制
            maxshared=0,  # 链接池中最多共享的链接数量，0和None表示全部共享
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待。True，等待；False，不等待然后报错
            maxusage=None,  # 一个链接最多被重复使用的次数，None表示无限制
            setsession=[],  # 开始会话前执行的命令列表。
            ping=1
        )

    def get_connection(self):
        return self._pool.connection()

    def execute(self, sql, params=None, commit=True):
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, params)
            if commit:
                conn.commit()
            result = cursor.fetchall()
            return result
        except Exception as e:
            conn.rollback()
            traceback.print_exc()
            raise e
        finally:
            cursor.close()
            conn.close()

    def executemany(self, sql, params=None, commit=True):
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.executemany(sql, params)
            if commit:
                conn.commit()
        except Exception as e:
            conn.rollback()
            traceback.print_exc()
            raise e
        finally:
            cursor.close()
            conn.close()
