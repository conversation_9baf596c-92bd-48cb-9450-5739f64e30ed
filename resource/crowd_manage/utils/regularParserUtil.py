#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
-- file name     : regularParser.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2018-10-29
-- copyright     : @qctx
-- function list : 规则解析数据
-- history       : 第一版脚本，无历史
"""
import re


class Regular(object):
    def __init__(self, wordType, inputData):
        self.regularType = ".*"
        self.headWord = "^"
        self.wordType = wordType
        self.inputData = inputData

    pass

    def matchParserData(self):
        pattern = self.headWord + self.wordType + self.regularType
        print(pattern)
        regex = re.compile(pattern)
        return regex.search(self.inputData)

    pass


if __name__ == '__main__':
    f = Regular("match", "nomatch._018:1888").matchParserData()
    print(f)
