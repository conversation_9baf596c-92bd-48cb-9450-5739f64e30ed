#!/usr/bin/python3
# -*- coding: utf-8 -*-
__author__ = 'lijiacheng'
'''
-- file name     : MysqlConfig.py
-- author        : lijiacheng
-- version       : v1.0
-- date          : 2020-08-17
-- description   : 
'''
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from MysqlUtils import MysqlUtils

class MysqlConfig:

    @staticmethod
    def dataCenterMysql():
        host = "************"
        port = 3171
        user = "jt_dw"
        db = "jt_dw"
        passwd = "0f0aa2de4ce3d8b3"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def labelMysql():
        #host = "mysql.dev.daikuan.qihoo.net"
        #port = 2490
        # host = "*************"
        host = "pro-4213-r.mysql.daikuan.qihoo.net"
        port = 4213
        user = "ucs_wr"
        db = "ucs"
        passwd = "36d8b819369cb385"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils
        # return MysqlConfig().ucsTestMysql()

    @staticmethod
    def testLabelMysql():
        host = "*************"
        port = 2076
        user = "jt_label_wr"
        db = "jt_label"
        passwd = "40d73b9744186b33"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def integrationMysql():
        # host = "*************"
        host = "pro-20417-r.mysql.daikuan.qihoo.net"
        port = 20417
        user = "ipss_r1"
        db = "ipss"
        passwd = "05e1b4fb23727217"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils
        # return MysqlConfig.integrationTestMysql()

    @staticmethod
    def clickhouseMysql():
        host = "*************"
        port = 20854
        user = "jt_dw_dev"
        db = "jt_dw_dev"
        passwd = "331dae205006c3cf"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def jtlabelMysql():
        host = "pro-4958-rw.mysql.daikuan.qihoo.net"
        port = 4958
        user = "jt_label"
        db = "jt_label"
        passwd = "520124ef2ddcfd69"
        return MysqlUtils(host, port, user, passwd, db)
        # return MysqlConfig.testLabelMysql()


    @staticmethod
    def ipssReportMysql():
        host = "*************"
        port = 21799
        user = "ipss_report_rw2"
        db = "ipss_report"
        passwd = "4dcb697e4765f08a"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils
        # return MysqlConfig.ipssTestReportMysql()

    @staticmethod
    def ipssTestReportMysql():
        host = "*************"
        port = 2321
        user = "ipss_report"
        db = "ipss_report"
        passwd = "0cdc02e44b129408"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def ucsTestMysql():
        host = "mysql.dev.daikuan.qihoo.net"
        port = 2490
        user = "ucs"
        db = "ucs"
        passwd = "67f6b9193eec55a9"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils

    @staticmethod
    def integrationTestMysql():
        host = "stg1-2029-rw.mysql.daikuan.qihoo.net"
        port = 2029
        user = "ipss_stg1"
        db = "ipss_stg1"
        passwd = "68a571a73298bf85"
        # host = "mysql.dev.daikuan.qihoo.net"
        # port = 2490
        # user = "ipss"
        # db = "ipss"
        # passwd = "76b622ad5888cfa2"
        mysqlUtils = MysqlUtils(host, port, user, passwd, db)
        return mysqlUtils
