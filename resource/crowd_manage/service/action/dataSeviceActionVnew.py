#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
-- file name     : dataSeviceActionVnew.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2022-11-02
-- copyright     : @qctx
-- function list : 数据服务action
-- history       : 第一版脚本，无历史
"""
import logging
import os
import sys
import traceback

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(base_dir)

from utils.logUtil import Log
from service.model.esNestedDocVnew import NestedMatchRangeDispose


class DataSeviceAction(object):
    def __init__(self, actionValue):
        self.actionValue = actionValue

    def ParserActionData(self):
        '''
        解析json，拼接nested json字符串
        :return:
        '''
        try:

            actionKey = self.actionValue["condition"]
            dateValue = self.actionValue["kd1"]
            actionBoolean = self.actionValue["action"]

            NestedkeysHead, NestedkeysEnd = self.NestedTitleEnd(actionKey, actionBoolean)

            NestedJsonDate = NestedMatchRangeDispose(actionKey, "kd1", dateValue, "").__Nested_date_range__()

            if "cond_and" in self.actionValue:

                boolHead, boolEnd = self.NestedMidHead("cond_and")
                cond = self.actionValue["cond_and"]
                self.condActionData(cond, actionKey)

                dataJson = boolHead + ",".join(self.condActionData(cond, actionKey)) + boolEnd

                jsonBody = NestedkeysHead + NestedJsonDate + "," + dataJson + NestedkeysEnd

                return jsonBody

            elif "cond_or" in self.actionValue:

                boolHead, boolEnd = self.NestedMidHead("cond_or")
                cond = self.actionValue["cond_or"]

                dataJson = boolHead + ",".join(self.condActionData(cond, actionKey)) + boolEnd

                jsonBody = NestedkeysHead + NestedJsonDate + "," + dataJson + NestedkeysEnd

                return jsonBody

            return NestedkeysHead + NestedJsonDate + NestedkeysEnd
        except Exception as e:
            Log("es_select_log_record")
            logging.info("code=" + str() + "Exception" + str(e))
            print(e)
            return 0

    def ParserActionNoDate(self):
        '''
        解析json，拼接nested json字符串 无日期的样式
        :return:
        '''
        try:

            actionKey = self.actionValue["condition"]
            actionBoolean = self.actionValue["action"]

            NestedkeysHead, NestedkeysEnd = self.NestedTitleEnd(actionKey, actionBoolean)

            if "cond_and" in self.actionValue:

                boolHead, boolEnd = self.NestedMidHead("cond_and")
                cond = self.actionValue["cond_and"]
                self.condActionData(cond, actionKey)

                dataJson = boolHead + ",".join(self.condActionData(cond, actionKey)) + boolEnd

                jsonBody = NestedkeysHead + dataJson + NestedkeysEnd

                return jsonBody

            elif "cond_or" in self.actionValue:

                boolHead, boolEnd = self.NestedMidHead("cond_or")
                cond = self.actionValue["cond_or"]

                dataJson = boolHead + ",".join(self.condActionData(cond, actionKey)) + boolEnd

                jsonBody = NestedkeysHead + dataJson + NestedkeysEnd

                return jsonBody
        except Exception as e:
            Log("es_select_log_record")
            logging.info("code=" + str() + "Exception" + str(e))
            print(e)
            traceback.print_stack()
            return 0

    def condActionData(self, cond, actionKey):
        conList = []
        for keyJson in cond:
            subKey = keyJson["cond"]
            subCondition = keyJson["condition"]
            subOperate = keyJson["operate"]

            conList.append(NestedMatchRangeDispose(actionKey, subKey, subCondition, subOperate).MatchData())

        return conList

    def NestedTitleEnd(self, netstedkeys, boolean):
        '''
        :param netstedkeys:
        :param boolean:
        :return: 嵌套文档bodyTitle值
        '''

        if boolean == "not_must":
            boolean = "must_not"

        head = """{ "bool": {"%s": [ {"nested": {"path": "%s","query": {"bool": {"must": [""" % (boolean, netstedkeys)
        end = """] }}}} ]}}"""
        return head, end

    def NestedMidHead(self, shouldcond):
        '''
        :param shouldcond:
        :return: 嵌套文档模糊匹配
        '''
        head = '''{ "bool": {"%s": [ ''' % ("must" if shouldcond == "cond_and" else "should")

        end = ''']}}'''

        return head, end


if __name__ == '__main__':
    '''
    '''
