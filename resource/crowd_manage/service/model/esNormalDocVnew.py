#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
-- file name     : esNormalDocVnew.py
-- author        : ca<PERSON>n<PERSON>
-- version       : v1.0
-- date          : 2021-03-01
-- copyright     : @qctx
-- function list : 对es普通文档处理
-- history       : 第一版脚本，无历史
"""

import json

class MatchRangeDispose(object):
    '''
    正常文档部分：业务类型range,match拼接
    '''
    def __init__(self,keys,values,operate):
        self.keys=keys
        self.values=values
        self.operate=operate
        self.lte='lte'
        self.lt='lt'
        self.gte='gte'
        self.gt='gt'
        self.like='like'
        self.eq='eq'
        self.ne='ne'
        self.gte_lte='gte_lte'
        self.gt_lte='gt_lte'
        self.gte_lt='gte_lt'
        self.gt_lt='gt_lt'


    def __normalMatchRangeData__(self):
        matchList=[]
        for value in self.values:
              if self.operate==self.eq:
                      if str(self.keys).startswith("kli"):
                          likeJson='''{ "wildcard": { "%s":{ "value": "*%s*" } }}''' %(self.keys,value)
                          matchList.append(likeJson)
                      else:
                          matchJson='''{ "match": { "%s": "%s" } }''' %(self.keys,value)
                          matchList.append(matchJson)

              elif self.operate==self.like:
                  likeJson='''{ "wildcard": { "%s":{ "value": "*%s*" } }}''' %(self.keys,value)
                  matchList.append(likeJson)

              elif self.operate==self.gt:
                  gtJson='''{"range": {"%s": {"%s":"%s"}}}''' %(self.keys,self.gt,value)
                  matchList.append(gtJson)

              elif self.operate==self.gte:
                  gteJson='''{"range": {"%s": {"%s":"%s"}}}''' %(self.keys,self.gte,value)
                  matchList.append(gteJson)

              elif self.operate==self.lte:
                  lteJson='''{"range": {"%s": {"%s":"%s"}}}''' %(self.keys,self.lte,value)
                  matchList.append(lteJson)

              elif self.operate==self.lt:
                  ltJson='''{"range": {"%s": {"%s":"%s"}}}''' %(self.keys,self.lt,value)
                  matchList.append(ltJson)

              elif self.operate==self.gte_lte:
                  headVaule=value.split("-")[0]
                  endValue=value.split("-")[1]
                  gte_lteJson='''{"range": {"%s": {"%s": "%s","%s": "%s"}}}''' %(self.keys,self.gte,headVaule,self.lte,endValue)
                  matchList.append(gte_lteJson)

              elif self.operate==self.gte_lt:
                  headVaule=value.split("-")[0]
                  endValue=value.split("-")[1]
                  gte_ltJson='''{"range": {"%s": {"%s": "%s","%s": "%s"}}}''' %(self.keys,self.gte,headVaule,self.lt,endValue)
                  matchList.append(gte_ltJson)

              elif self.operate==self.gt_lte:


                  headVaule=value.split("-")[0]
                  endValue=value.split("-")[1]
                  gt_lteJson='''{"range": {"%s": {"%s": "%s","%s": "%s"}}}''' %(self.keys,self.gt,headVaule,self.lte,endValue)
                  matchList.append(gt_lteJson)

              elif self.operate==self.gt_lt:
                  headVaule=value.split("-")[0]
                  endValue=value.split("-")[1]
                  gt_lteJson='''{"range": {"%s": {"%s": "%s","%s": "%s"}}}''' %(self.keys,self.gt,headVaule,self.lt,endValue)
                  matchList.append(gt_lteJson)



        if self.operate==self.ne:
            normalJson=self.booleanHead='''{"bool":{"must_not":[{"bool":{"should":[ '''+",".join(matchList)+''' ]}}]}}'''
        else:
            normalJson=self.booleanHead='''{"bool":{"must":[ {"bool":{"should":[ '''+",".join(matchList)+''' ]}}]}}'''

        return normalJson




if __name__ == '__main__':
    f=MatchRangeDispose("appProperty",["km1","km2"]).__normalMatchRangeData__()
    print (f)










