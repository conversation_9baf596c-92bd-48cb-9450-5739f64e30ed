#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
-- file name     : esNestedCompare.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2018-10-29
-- copyright     : @qctx
-- function list : es实现嵌套文档比较
-- history       : 第一版脚本，无历史
"""
from  esNormalCompare import Compare


class NestedCompare(object):
    '''
    es嵌套文档比较级封装，包含lte,lt,gte,ge等属性
    '''

    def __init__(self, NestedKeys, key, value):
        self.type = type
        self.key = key
        self.value = value
        self.NestedKeys = NestedKeys
        self.gte = "gte"
        self.lte = "lte"
        self.lt = "lt"
        self.gt = "gt"

    def __Nested_lte_lt_gte_gt__(self, filterData, compareType):
        filter = self.filterData(filterData)
        lte_lt_gte_gtJson = filter + """ {
            "range": {
                "%s.%s": {
                    "%s":"%s"
                }
            }
        }]}}""" % (self.NestedKeys, self.key, compareType, self.value)
        return lte_lt_gte_gtJson

    pass

    def __Nested_lte_lt_gte_gt_subset__(self, compareType):
        valuse = self.value.split("-")
        if compareType == "gt_lt":
            lte_lt_gte_gtJson = """
              {
               "range": {
                   "%s.%s": {
                       "%s": "%s"
                      ,"%s": "%s"
                   }
               }
               }
           """ % (self.NestedKeys, self.key, self.gt, valuse[0], self.lt, valuse[1])
        elif compareType == "gt_lte":
            lte_lt_gte_gtJson = """
              {
               "range": {
                   "%s.%s": {
                       "%s": "%s"
                      ,"%s": "%s"
                   }
               }
               
              }
           """ % (self.NestedKeys, self.key, self.gt, valuse[0], self.lte, valuse[1])
        elif compareType == "gte_lt":
            lte_lt_gte_gtJson = """
              {
               "range": {
                   "%s.%s": {
                       "%s": "%s"
                      ,"%s": "%s"
                   }
               }
               }
           """ % (self.NestedKeys, self.key, self.gte, valuse[0], self.lt, valuse[1])

        elif compareType == "gte_lte":
            lte_lt_gte_gtJson = """
              {
               "range": {
                   "%s.%s": {
                       "%s": "%s"
                      ,"%s": "%s"
                   }
               }}
           """ % (self.NestedKeys, self.key, self.gte, valuse[0], self.lte, valuse[1])
        return lte_lt_gte_gtJson

    def __Nested_rangeEq__(self, filterData):
        filter = self.filterData(filterData)
        eqJson = filter + """{ "match": { "%s.%s": "%s" } }]}}""" % (self.NestedKeys, self.key, self.value)
        return eqJson

    pass

    def __Nested_date_range__(self):

        if self.value.get("date_type") == "normal":
            rangeJson = """ {
                "range": {
                    "%s.%s": {
                        "%s": "%s",
                        "%s": "%s"
                    }
                }
            }""" % (
            self.NestedKeys, self.key, self.gte, self.value.get("start_date"), self.lte, self.value.get("end_date"))
            return rangeJson

        elif self.value.get("date_type") == "roll":

            rangeJson = """ {
                "range": {
                    "%s.%s": {
                        "%s": "%s",
                        "%s": "%s"
                    }
                }
            }""" % (self.NestedKeys, self.key, self.gte,
                    self.day_get(self.value.get("to_date")) if self.value.get("to_date") != 0 else "2000-12-31"
                                            , self.lte,
                    self.day_get(self.value.get("up_to_date")) if self.value.get("up_to_date") != 0 else "9999-12-31")

            return rangeJson

    def day_get(self, days):
        from datetime import date
        from datetime import timedelta
        return (date.today() - timedelta(days=days)).strftime("%Y-%m-%d")

    def filterData(self, filterData):
        filterData = '''{"bool":{"must_not":[''' if filterData == "Y" else '''{"bool":{"must":['''
        return filterData


if __name__ == "__main__":
    f = Compare("range", "k15", "18-39")
    print(f.__range__())
