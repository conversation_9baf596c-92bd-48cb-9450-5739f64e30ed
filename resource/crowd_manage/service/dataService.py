#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
-- file name     : dataService.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2022-11-03
-- copyright     : @qctx
-- function list : 数据服务
-- history       : 第一版脚本，无历史
"""
import json
import logging
import os
import sys

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)

from utils.logUtil import Log
from utils import esUtil
from service.action.dataSeviceAction import DataSeviceAction
from service.model.esNormalDoc import MatchRangeDispose
from service.model.esNormalDocVague import VagueMatchDispose
import traceback

class Service(object):
    def __init__(self, number, bagJson, bagClass, filePath='', inputHdfs=None):
        self.bagJson = bagJson
        self.number = number
        self.bagClass = bagClass
        self.filePath = filePath
        self.inputHdfs = inputHdfs
        self.head = """
                   {
                   "query": {
                     "bool": {
                       "must": [
                 """
        self.end = """
                   ]
                       }
                     }
                   }
             """

    def queryListMain(self, ruleJson=''):
        """
        :param ruleJson:
        :return:规则解析入口
        """
        try:
            transformJson = ruleJson  # 将前端传数据转换未字典
            allJsonList = []

            ru = json.loads(transformJson)
            for trans in ru.items():
                if str(trans[0]).startswith("krr"):  ##如果不是嵌套属性并且为自定义范围属性，执行range区间值
                    dataJson = MatchRangeDispose(trans[0], trans[1]).__Range__()
                elif str(trans[0]).startswith("kli"):
                    dataJson = VagueMatchDispose(trans[0], trans[1]).__wildcardMatch__()
                elif str(trans[0]).startswith("k") and not str(trans[0]).startswith("krr") and not str(
                        trans[0]).startswith("kli"):
                    dataJson = MatchRangeDispose(trans[0], trans[1]).__Match__()
                elif str(trans[0]).startswith("app-"):
                    dataJson = MatchRangeDispose("appProperty", trans[1]).__Range__()
                elif str(trans[0]).startswith("action-"):
                    dataJson = DataSeviceAction(trans).parserNomalAction()
                # elif str(trans[0]).startswith("groupAction-"):
                #    dataJson=DataSeviceAction(trans)
                else:
                    boolean = trans[0]
                    head, end = self.actionBoolean(boolean)
                    actionJsonList = []
                    for actionJson in trans[1]:
                        actionL = DataSeviceAction(actionJson).ParserData()
                        actionJsonList.append(actionL)
                    dataJson = head + ",".join(actionJsonList) + end
                allJsonList.append(dataJson)
            allJsonMerge = ",".join(allJsonList)
            return allJsonMerge
        except Exception as e:
            traceback.print_exc()
            print(e)
            return 0

    def mainBodyQueryEs(self):
        """
        :param ruleJson:
        :return:
        """
        try:

            allJsonMerge = self.queryListMain(self.bagJson)

            esJsonBody = self.head + allJsonMerge + self.end

            Log("es_select_log_record")
            logging.info("code=" + json.dumps(esJsonBody, ensure_ascii=False) + ",type=" + str(
                self.number) + ",defind=" + json.dumps(self.bagJson, ensure_ascii=False))
            return esUtil.mainEsUtil(esJsonBody, self.bagJson, self.number, self.bagClass, self.filePath,
                                     self.inputHdfs)
        except Exception as e:
            print(e)
            return 0

    def actionBoolean(self, boolean):
        if str(boolean).endswith("and"):
            booleanData = "and"
        else:
            booleanData = "or"
        head = '''  { "bool": {"%s": [ ''' % ("must" if booleanData == 'and' else "should")
        end = ''']}}'''
        return head, end

    def filterRequestJson(self, filterType):
        head = '''  { "bool": {"%s": [ ''' % ("must" if filterType == 'must' else "must_not")
        end = ''']}}'''
        return head, end


if __name__ == '__main__':
    defind = 1
    querycondition = {
        "k1": [
            "123",
            "2345"
        ],
        "app-loan": ["km1", "km2", "km3", "km4"],
        "insure_or": [
            {
                "kd1": [
                    "1999-01-01",
                    "9999-12-30"
                ],
                "action": "match",
                "condition": "insured_action",
                "cond_or": [
                    {
                        "cond": "k4",
                        "operate": "eq",
                        "condition": "健康险",
                        "filter": "Y"
                    },
                    {
                        "cond": "k4",
                        "operate": "eq",
                        "condition": "重疾险",
                        "filter": "N"
                    }
                ]
            },
            {
                "kd1": [
                    "1999-12-31",
                    "9999-12-30"
                ],
                "action": "match",
                "condition": "insured_action"
            },
            {
                "kd1": [
                    "1999-12-31",
                    "9999-12-30"
                ],
                "action": "match",
                "condition": "gift_action"
            }
        ],
        "renew_or": [
            {
                "kd1": [
                    "1999-12-31",
                    "9999-12-30"
                ],
                "action": "match",
                "condition": "renewal_long_action",
                "cond_or": [
                    {
                        "cond": "k1",
                        "operate": "eq",
                        "condition": "是",
                        "filter": "N"
                    },
                    {
                        "cond": "krr1",
                        "operate": "gt",
                        "filter": "N",
                        "condition": "3"
                    }
                ]
            },
            {
                "kd1": [
                    "1999-12-31T16:00:00.000Z",
                    "9999-12-30T16:00:00.000Z"
                ],
                "action": "match",
                "condition": "renewal_short_action"
            }
        ]
    }

    f = Service(defind, querycondition, type).queryListMain()
