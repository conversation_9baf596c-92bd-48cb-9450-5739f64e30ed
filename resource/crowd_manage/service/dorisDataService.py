#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
-- file name     : dorisDataService.py
-- author        : <PERSON><PERSON><PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2023-03-25
-- copyright     : @qctx
-- function list : 数据服务新环境版本
-- history       : 第一版脚本，无历史
"""
import datetime
import json
import os
import sys
import time
import traceback
from importlib import reload

import pymysql

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)

from utils.dorisPoolManager import DorisPoolManager
from utils.httpRequestUtil import requestCrowdRuleParse
from utils.httpRequestUtil import requestDelayCrowdRule
from utils.dorisUtil import mainDorisUtil

reload(sys)


class DorisDataService(object):

    def __init__(self, bagInfo, filePath, inputHdfs, dorisPageSize):
        self.bagInfo = bagInfo
        self.filePath = filePath
        self.inputHdfs = inputHdfs
        self.pageSize = int(dorisPageSize)

    def handDorisToHdfs(self):
        businessType = self.convertBagClassToBusinessType(self.bagInfo.bagClass)
        localFilePath = self.generateLocalFilePath(businessType)
        localFileName = self.generateLocalFileName()
        fileName = localFilePath + localFileName
        filterSql = buildCrowSql(self.bagInfo, False)
        self.handDorisToLocal(fileName, filterSql)
        mainDorisUtil(localFilePath, localFileName, self.inputHdfs, businessType, self.bagInfo.number)

    def handDorisToLocal(self, fileName, filterSql):
        conn = DorisPoolManager.offline_doris_pool.get_connection()
        cursor = conn.cursor(pymysql.cursors.SSCursor)
        f = open(fileName, 'w')
        try:
            cursor.execute("set query_timeout=7200")
            cursor.execute(filterSql)
            while True:
                result = cursor.fetchmany(self.pageSize)
                if not result:
                    break
                for row in result:
                    for user_no in row:
                        f.write(user_no + "\t" + self.bagInfo.number + "\n")
        except Exception as e:
            traceback.print_exc()
            print(e)
            raise e
        finally:
            f.close()
            cursor.close()
            conn.close()

    def generateLocalFileName(self):
        return self.bagInfo.number + "_" + datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".tsv"

    def generateLocalFilePath(self, business_type):
        return self.makeLocalDir(business_type)

    def makeLocalDir(self, business_type):
        result = False
        localFilePath = self.filePath + "/" + business_type + "/"

        for i in range(5):
            print(localFilePath + " number=" + self.bagInfo.number)
            if not os.path.exists(localFilePath):
                try:
                    os.makedirs(localFilePath)
                    result = True
                    break
                except Exception as e:
                    time.sleep(5)
            else:
                result = True
                break
        if not result:
            raise Exception("创建文件路径失败" + localFilePath)

        return localFilePath

    def convertBagClassToBusinessType(self, bag_class):
        bagClass = str(bag_class)
        business_type = ''
        if bagClass in ("1", "3", "5"):
            business_type = "CREDITLOAN"
        elif bagClass in ("4"):
            business_type = "NICAIFU"
        elif bagClass in ("999"):
            business_type = "JIETIAONINEC"
        return business_type


def buildCrowSql(bagInfo, isDelay):
    dataJson = {"bagType": bagInfo.bagType,
                "ruleJson": bagInfo.bagJson,
                "version": bagInfo.version,
                "dataMechanism": bagInfo.dataMechanism,
                "businessType": bagInfo.bagClass}
    print(json.dumps(dataJson))
    if isDelay:
        filterSql = requestDelayCrowdRule(dataJson)
    else:
        filterSql = requestCrowdRuleParse(dataJson)
    sqlReplace = filterSql.split(";")[1]
    print(sqlReplace)
    return sqlReplace


# todo 重试逻辑
def writeDorisToLocal(bagInfo, fileName, isTab, pageSize):
    pageSize = int(pageSize)
    crowSql = buildCrowSql(bagInfo, True)
    conn = DorisPoolManager.offline_doris_pool.get_connection()
    cursor = conn.cursor(pymysql.cursors.SSCursor)
    f = open(fileName, 'w')
    try:
        cursor.execute("set query_timeout=7200")
        cursor.execute(crowSql)
        while True:
            result = cursor.fetchmany(pageSize)
            if not result:
                break
            for row in result:
                for user_no in row:
                    if isTab:
                        f.write(user_no + "\t" + bagInfo.number + "\n")
                    else:
                        f.write(user_no + "\n")
        return 0
    except Exception as e:
        print(e)
        traceback.print_exc()
        return 1
    finally:
        f.close()
        cursor.close()
        conn.close()
