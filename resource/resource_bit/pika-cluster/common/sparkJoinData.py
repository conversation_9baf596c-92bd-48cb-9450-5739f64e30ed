#!/usr/bin/python2.6
# -*- coding: utf-8 -*-

'''
-- file name     : sparkJoinData.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2019-04-03
-- copyright     : @qctx
-- function list : join merge数据三层目录
-- history       : 第一版脚本，无历史
'''

import sys

from pyspark import SparkConf, SparkContext, Row
from pyspark.sql import *
from pyspark.sql.functions import *

conf = SparkConf().setMaster("yarn-client").setAppName("yushu_pyspark_merge_join_crowd").set(
    "spark.hive.mapred.supports.subdirectories", "true").set(
    "spark.hadoop.mapreduce.input.fileinputformat.input.dir.recursive", "true")
sc = SparkContext(conf=conf)
sqlContext = HiveContext(sc)


def _get_path_dataframe_one(onepath, outputPath):
    print("yushu_pyspark_merge_join_one_crowd")
    one_data = sc.textFile(onepath, use_unicode=False).map(lambda line: line.split("\t"))

    data = sqlContext.createDataFrame(one_data.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    data.registerTempTable("tableA")

    sql = """
     select userno,concat_ws(',',collect_set(ruleno)) as ruleno from tableA where userno !='' and length(userno)>1 and userno is not null and ruleno !='' and ruleno is not null group by userno
    """

    dataFrame = sqlContext.sql(sql)

    dataFrame.rdd.repartition(10).map(lambda p: p.userno + "\t" + p.ruleno).saveAsTextFile(outputPath)


def _get_path_dataframe(onepath, twopath, outputPath):
    print("yushu_pyspark_merge_join_two_crowd")
    one_data = sc.textFile(onepath, use_unicode=False).map(lambda line: line.split("\t"))
    two_data = sc.textFile(twopath, use_unicode=False).map(lambda line: line.split("\t"))

    onedata001 = sqlContext.createDataFrame(one_data.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    twodata001 = sqlContext.createDataFrame(two_data.map(lambda p: Row(userno=p[0], ruleno=p[1])))

    data = onedata001.unionAll(twodata001)
    data.registerTempTable("tableA")

    sql = """
     select userno,concat_ws(',',collect_set(ruleno)) as ruleno from tableA where userno !='' and length(userno)>1 and userno is not null and ruleno !='' and ruleno is not null group by userno
    """

    dataFrame = sqlContext.sql(sql)

    dataFrame.rdd.repartition(10).map(lambda p: p.userno + "\t" + p.ruleno).saveAsTextFile(outputPath)


def _get_path_dataframe_three(onepath, twopath, outputPath, threePath):
    print("yushu_pyspark_merge_join_three_crowd")
    one_data = sc.textFile(onepath, use_unicode=False).map(lambda line: line.split("\t"))
    two_data = sc.textFile(twopath, use_unicode=False).map(lambda line: line.split("\t"))
    three_data = sc.textFile(threePath, use_unicode=False).map(lambda line: line.split("\t"))

    onedata001 = sqlContext.createDataFrame(one_data.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    twodata001 = sqlContext.createDataFrame(two_data.map(lambda p: Row(userno=p[0], ruleno=p[1])))
    threedata001 = sqlContext.createDataFrame(three_data.map(lambda p: Row(userno=p[0], ruleno=p[1])))

    data = onedata001.unionAll(twodata001).unionAll(threedata001)
    data.registerTempTable("tableA")

    sql = """
     select userno,concat_ws(',',collect_set(ruleno)) as ruleno from tableA where userno !='' and length(userno)>1 and userno is not null and ruleno !='' and ruleno is not null group by userno
    """

    dataFrame = sqlContext.sql(sql)

    dataFrame.rdd.repartition(10).map(lambda p: p.userno + "\t" + p.ruleno).saveAsTextFile(outputPath)


if __name__ == '__main__':
    if len(sys.argv) == 3:
        onepath = sys.argv[1]
        outputPath = sys.argv[2]
        f = _get_path_dataframe_one(onepath, outputPath)
        print(f)
    elif len(sys.argv) == 4:
        onepath = sys.argv[1]
        twopath = sys.argv[2]
        outputPath = sys.argv[3]
        f = _get_path_dataframe(onepath, twopath, outputPath)
        print(f)
    elif len(sys.argv) == 5:
        onepath = sys.argv[1]
        twopath = sys.argv[2]
        outputPath = sys.argv[3]
        threepath = sys.argv[4]
        f = _get_path_dataframe_three(onepath, twopath, outputPath, threepath)
        print(f)
