class BusinessMapping(object):
    def __init__(self):
        pass

    @staticmethod
    def convertBagClassToBusinessType(bag_class):
        bagClass = str(bag_class)
        business_type = ''
        if bagClass in ("1", "3", "5"):
            business_type = "CREDITLOAN"
        elif bagClass in ("4"):
            business_type = "NICAIFU"
        elif bagClass in ("999"):
            business_type = "JIETIAONINEC"
        return business_type
