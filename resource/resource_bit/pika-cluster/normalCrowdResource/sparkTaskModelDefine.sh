#!/bin/bash

##  filename      : sparkTaskModel.sh
##  author        : luoxin
##  version       : v1.0
##  date          : 2025-02-06
##  copyright     : @qctx
##  description   : spark任务模式写入mongo使用
##  usage         : 借条业务线自定义人群入资源位
##  function list :
##  history       : 第一版脚本
# set -e

set -x
echo $(hostname)
yesterday=${system.biz.date}
todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
thedate=`date -d ${yesterday} +%Y%m%d`
todayminSecond=$(/bin/date -d-0day "+%Y%m%d%H%M%S")

unzip prd_resource_bit_pika_cluster.zip
cwd=$(
  cd $(dirname $0)
  pwd
)

#source  ./alarm.sh
parentScriptDir=$cwd
localFileParentDir=$cwd

### 跑数据临时存储目录
exe_type=defined_Big
config_name=conf_${exe_type}_${todayminSecond}
hdfs_file_path=/user/hive/warehouse/credit_data.db/prd_doris_resource_crowd_data_pika_cluster
hdfs_config_file_path=$hdfs_file_path
definedHdfsDir=$hdfs_file_path/defined_v2/$todayyyymmdd
hdfs_config_file_name=$hdfs_config_file_path/$todayyyymmdd/$config_name

source /home/<USER>/anaconda_yushu_jds/bin/activate py36
pythonPath=python3
config_file_path=$cwd/

projectName=${jdsInnerPlatformProjectName}
workflowName=${jdsInnerPlatformProcessName}
taskName=${jdsInnerPlatformTaskName}
pikaClusterHost=pro-54024-rw.redis.daikuan.qihoo.net
pikaClusterPort=54024
pikaClusterPsw=06130930362ac54d

# 第一步 生成配置文件数据存在 本地
echo "步骤1执行开始..."
$pythonPath $parentScriptDir/normalCrowdResource/produceCrowdConfig.py $config_file_path $config_name $exe_type $pikaClusterHost $pikaClusterPort $pikaClusterPsw
result=$1
echo "步骤1执行结束..."$result
if [ ! -e  $config_file_path$config_name ] || [ ! -s  $config_file_path$config_name  ] ; then
    echo "文件不存在或者数据不存在"
	  rm -rf $config_file_path$config_name
    exit 0
fi

# 第二步 步骤1生成的本地文件 上传hdfs目录
echo "步骤2执行开始..."
hadoop fs -test -e $hdfs_config_file_path/$todayyyymmdd
result=$?
if [ $result -ne 0 ] ;then
  hadoop fs -mkdir -p $hdfs_config_file_path/$todayyyymmdd
fi
hadoop fs -put $config_file_path$config_name $hdfs_config_file_path/$todayyyymmdd/
flag_success=$?
num=0
until [ $flag_success -eq 0 ]; do
   sleep 10s
   ((num++))
   if [ $num -gt 3 ]; then
     subject="${todaymin}智能运营，重试三次上传配置文件到hdfs失败"
     echo -e $subject
     exit 1
   else
     hadoop fs -put $config_file_path$config_name $hdfs_config_file_path
     flag_success=$?
   fi
done
if [ $num -ge 4 ]; then
     subject="${todaymin}智能运营，重试三次上传配置文件到hdfs失败"
     echo -e $subject
     exit 1
fi
echo "步骤2执行结束..."$flag_success

# 第三步 步骤二hdfs配置文件驱动 下载数据到hdfs中
echo "步骤3执行开始..."
$pythonPath $parentScriptDir/normalCrowdResource/transferCrowdFileToHdfs.py $parentScriptDir $definedHdfsDir/ $exe_type $config_file_path$config_name
result=$?
echo "步骤3执行结束..."$result

# 第四步 步骤3 hdfs配置文件驱动 spark-submit 并发执行写入 pika cluster
echo "步骤4执行开始..."

source /home/<USER>/anaconda_yushu_jds/bin/activate py36
export HDP_VERSION=*******-315
export PYSPARK_PYTHON="./environment/bin/python"
export PYSPARK_DRIVER_PYTHON="/home/<USER>/anaconda_yushu_jds/envs/py36/bin/python"
HDFS_PYTHON="hdfs:///yushu/conda_environment_package/yushu_jds_py36_environment_20250114.tar.gz#environment"
export SPARK_HOME=/usr/hdp/*******-315/spark3.3.2/
SPARK_SUBMIT="/usr/hdp/*******-315/spark3.3.2/bin/spark-submit"

export appname=${user_name}:[${spark_env}_shell:[项目名:${projectName}][流程名:${workflowName}][任务名:${taskName}]]

${SPARK_SUBMIT} \
--master yarn \
--name ${appname} \
--queue yushu_offline_normal \
--archives ${HDFS_PYTHON} \
--conf spark.yarn.appMasterEnv.PYSPARK_DRIVER_PYTHON=${PYSPARK_DRIVER_PYTHON} \
--conf spark.yarn.appMasterEnv.PYSPARK_PYTHON=${PYSPARK_PYTHON} \
--conf spark.executorEnv.PYSPARK_DRIVER_PYTHON=${PYSPARK_DRIVER_PYTHON} \
--conf spark.executorEnv.PYSPARK_PYTHON=${PYSPARK_PYTHON} \
--conf spark.driver.memory=8g \
--conf spark.driver.vcores=4 \
--conf spark.executor.memory=8g \
--conf spark.executor.vcores=4 \
--conf spark.yarn.executor.memoryOverhead=8192 \
--conf spark.executor.heartbeatInterval=2000s  \
--conf spark.dynamicAllocation.enabled=true \
--conf spark.dynamicAllocation.minExecutors=4 \
--conf spark.dynamicAllocation.initExecutors=1 \
--conf spark.dynamicAllocation.maxExecutors=125 \
--conf spark.dynamicAllocation.cachedExecutorIdleTimeout=600 \
--conf spark.default.parallelism=5000 \
--conf spark.sql.shuffle.partitions=5000 \
--conf spark.sql.autoBroadcastJoinThreshold=30240000 \
--conf spark.shuffle.service.enabled=true \
--conf spark.shuffle.push.enabled=true \
--conf spark.shuffle.service.name=spark3_shuffle_seg2 \
--conf spark.shuffle.service.port=7333 \
--conf spark.shuffle.registration.maxAttempts=10 \
--conf spark.shuffle.io.maxRetries=10 \
--conf spark.shuffle.io.retryWait=10s \
--conf spark.shuffle.spill.numElementsForceSpillThreshold=50000 \
--conf spark.ui.showConsoleProgress=true \
--conf spark.sql.statistics.fallBackToHdfs=true \
--conf spark.network.timeout=4000s \
--conf spark.sql.sources.partitionOverwriteMode='DYNAMIC' \
$parentScriptDir/normalCrowdResource/sparkTaskModelWriteData2PikaCluster.py \
"${pikaClusterHost}" "${pikaClusterPort}" "${pikaClusterPsw}" 10000 "${definedHdfsDir}" "R_" "${hdfs_config_file_name}" "${exe_type}"

sparkFlag=$?
echo "步骤4执行结束..."$sparkFlag
if [ $sparkFlag -ne 0 ]; then
    echo "spark submit fail!!!!!"
    exit 1
fi

# 步骤五 更新 mysql 记录表状态为1，并且记录人群包编号对应的版本号
echo "步骤5执行开始..."
$pythonPath $parentScriptDir/normalCrowdResource/updateDb.py $exe_type $config_file_path$config_name "${pikaClusterHost}" "${pikaClusterPort}" "${pikaClusterPsw}"
updateDbFlag=$?
echo "步骤5执行结束..."$updateDbFlag

# 步骤六 写入pika cluster 用户数据表版本信息 和hdfs版本信息校验
echo "步骤6执行开始..."

${SPARK_SUBMIT} \
--master yarn \
--name ${appname} \
--queue yushu_offline_normal \
--archives ${HDFS_PYTHON} \
--conf spark.yarn.appMasterEnv.PYSPARK_DRIVER_PYTHON=${PYSPARK_DRIVER_PYTHON} \
--conf spark.yarn.appMasterEnv.PYSPARK_PYTHON=${PYSPARK_PYTHON} \
--conf spark.executorEnv.PYSPARK_DRIVER_PYTHON=${PYSPARK_DRIVER_PYTHON} \
--conf spark.executorEnv.PYSPARK_PYTHON=${PYSPARK_PYTHON} \
--conf spark.driver.memory=8g \
--conf spark.driver.vcores=4 \
--conf spark.executor.memory=8g \
--conf spark.executor.vcores=4 \
--conf spark.yarn.executor.memoryOverhead=8192 \
--conf spark.executor.heartbeatInterval=2000s  \
--conf spark.dynamicAllocation.enabled=true \
--conf spark.dynamicAllocation.minExecutors=4 \
--conf spark.dynamicAllocation.initExecutors=1 \
--conf spark.dynamicAllocation.maxExecutors=125 \
--conf spark.dynamicAllocation.cachedExecutorIdleTimeout=600 \
--conf spark.default.parallelism=5000 \
--conf spark.sql.shuffle.partitions=5000 \
--conf spark.sql.autoBroadcastJoinThreshold=30240000 \
--conf spark.shuffle.service.enabled=true \
--conf spark.shuffle.push.enabled=true \
--conf spark.shuffle.service.name=spark3_shuffle_seg2 \
--conf spark.shuffle.service.port=7333 \
--conf spark.shuffle.registration.maxAttempts=10 \
--conf spark.shuffle.io.maxRetries=10 \
--conf spark.shuffle.io.retryWait=10s \
--conf spark.shuffle.spill.numElementsForceSpillThreshold=50000 \
--conf spark.ui.showConsoleProgress=true \
--conf spark.sql.statistics.fallBackToHdfs=true \
--conf spark.network.timeout=4000s \
--conf spark.sql.adaptive.advisoryPartitionSizeInBytes=67108864 \
--conf spark.sql.sources.partitionOverwriteMode='DYNAMIC' \
$parentScriptDir/normalCrowdResource/sparkTaskModePikaClusterDataCheck.py \
"${pikaClusterHost}" "${pikaClusterPort}" "${pikaClusterPsw}" "${hdfs_config_file_name}"  "${definedHdfsDir}" "R_" "${exe_type}"


sparkFlag=$?
echo "步骤6执行结束..."$sparkFlag
if [ $sparkFlag -ne 0 ]; then
    echo "spark submit fail!!!!!"
    exit 1
fi
