#!/bin/bash
##  file name     : compare_deleteExpiredCrowd.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2023-05-24
##  copyright     : @qctx
##  description   : 对比
##  usage         : 对JSON解析输出到HDFS,形成redis需要得key，value
##  function list :
##  history       : 第一版脚本，无历史
# set -e
set -x
unzip offline_label_application_doris
thedate=$1
twodate=$(/bin/date -d-2day "+%Y%m%d")
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
  todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
fi
cwd=$(
  cd $(dirname "$0")
  pwd
)
source ./alarm.sh

local_file_path=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application_doris/resource_crowd_data_v2/expired/
file_name="expired.conf"$todaymin
file_path=$local_file_path$file_name
mergepath1=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test/new_resource_data_merge/pday=${twodate}
mergepath2=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test/new_resource_data_merge/pday=${thedate}
output_path=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test/delete_compare_data_merge/$todaymin
hadoop fs -rm -r hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test/delete_compare_data_merge/$todaymin/$twodate*/

spark-submit --num-executors 300 --executor-memory 4g --driver-memory 1g  --queue yushu_offline_high --name "spark_resource_compare2_$thedate"  \
$parentScriptDir/normalCrowdResource/deleteExpired/sparkExplodeNewResourceCrowd.py $mergepath1 $mergepath2 $output_path $todaymin

result=$?
if [ $result -ne 0 ];then
    exit 1
fi


hadoop fs -getmerge ${output_path} $file_path
#resultConf=$(ls $file_path | wc -l)
exec=$?
if [ $exec -ne 0 ]; then
    echo "数据不存在"
    exit 0
fi
pythonPath=python3
parentScriptDir=$cwd
if [ ! -e  $file_path ] && [ ! -s $file_path ] ; then
    echo "文件不存在"
    exit 0
fi
$pythonPath $parentScriptDir/normalCrowdResource/deleteExpired/deleteExpiredCrowd.py $file_path
result=$?
if [ $result -eq 0 ]; then
    rm -rf $file_path
else
    echo "deleteExpiredCrowd run failed"
    exit 1
fi