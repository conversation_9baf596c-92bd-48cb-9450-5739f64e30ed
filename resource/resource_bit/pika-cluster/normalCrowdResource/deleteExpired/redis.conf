[conf_env]
env=prd
business=normal

[resource_redis_isc]
# 生产环境
# 一体化策略人群包pika
# hostname=************
hostname=pro-26049-rw.redis.daikuan.qihoo.net1
port=26049
passwd=1caad4b27efadf27
db=0

[resource_redis_normal]
#普通人群新的pika
# hostname=*************
hostname=pro-5009-rw.redis.daikuan.qihoo.net1
port=5009
passwd=782c2a7f1b4d71c0360
db=0


[resource_redis_stg]
#测试环境
hostname=10.185.161.2311
port=5987
passwd=051c5f5a4c05f7ad
db=12
# mock=true
; hostname=**************
;替换为
; hostname=**************
; port=25877
; passwd=5fc544524941253b
; db=0
[resource_redis_local]
; mock=false
hostname=localhost
port=6379
passwd=
db=0
# mock=false
