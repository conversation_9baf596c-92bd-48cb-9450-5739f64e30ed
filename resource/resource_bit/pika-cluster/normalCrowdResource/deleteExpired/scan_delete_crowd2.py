#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
-- file name     : scan_delete_crowd2.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 20230627
-- copyright     : @qctx
-- function list : 删除过期的key,优化，先查出所有的人群包
-- history       : 第一版脚本，无历史
-- desc          : scan命令在python2.7中不支持，需要python3，该脚本使用python3执行
'''
__author__ = 'zhangyong'

import json
import multiprocessing
import os
import sys
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

'''
-- file name     : deleteExpiredCrowd.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-06-12
-- description   : 删除redis已经过期的人群包信息
'''


class DeleteExpired(object):

    @staticmethod
    def get_redis_client_proto():
        from common.redisPoolManager import get_client

        return get_client()


def split_list(keys, n):
    if not len(keys):
        return []
    if len(keys) <= n:
        return keys
    return [keys[i: i + n] for i in range(0, len(keys), n)]


def calbb(obj):
    print(obj)


def get_all_crowd():
    # 1.加载所有的人群包数据，不用循环查询
    match_str = "N*"
    start = datetime.now()
    batch_size = 1000
    cursor = 0
    crowd_info_map = {}
    crowd_redis_client = DeleteExpired.get_redis_client_proto().connect()
    try:
        count = 0
        while True:
            cursor, keys = crowd_redis_client.scan(cursor, count=batch_size, match=str(match_str))
            crowd_version_list = crowd_redis_client.mget(keys)
            for index, c in enumerate(keys):
                crowd_version = crowd_version_list[index]
                c_j = json.loads(crowd_version)
                crowd_info_map[c] = c_j
            count = count + len(keys)
            if cursor == 0:
                print("获取的所有人群包处理结束")
                break

        count = count + len(keys)

        print("获取的所有人群包处理结束，数量%s" % count)
        end = datetime.now()

        print("删除过期的人群包处理耗时：" + str(end - start))
        return crowd_info_map
    finally:
        crowd_redis_client.close()


def scan_redis_key(match_str):
    batch_size = 5000
    start = datetime.now()
    cursor = 0
    # sum = 500000
    split_num = 500

    redis_client = DeleteExpired.get_redis_client_proto().connect()
    try:
        all_crowd_map = get_all_crowd()
        print(all_crowd_map)
        count = 0
        while True:
            pool = multiprocessing.Pool(processes=1)
            cursor, keys = redis_client.scan(cursor, count=batch_size, match=str(match_str))
            count = count + len(keys)
            print("key length=%s" % len(keys))
            for key_list in split_list(keys, split_num):
                pool.apply_async(delete_expired_value1, args=[key_list, all_crowd_map])
            pool.close()
            pool.join()
            if cursor == 0:
                print("删除过期的人群包处理结束")
                break
            # if count >= sum:
            #     print("处理100w的人群包处理结束")
            #     break
        print("处理%s开头的人群包处理结束，数量%s" % (match_str, count))
        end = datetime.now()

        print("删除过期的人群包处理耗时：" + str(end - start))
    finally:
        redis_client.close()


def delete_expired_value1(key_list, all_crowd_map):
    """
    :param key:
    :param crowd_map:
    :return:
    """
    print("初始化 redis")
    client = DeleteExpired.get_redis_client_proto().connect()
    for key in key_list:
        crowd_map = client.hgetall(key)
        delete_count = delete_expired_value(key, crowd_map, all_crowd_map, client)
        print("userno=%s,过期人群包数量%s" % (key, delete_count))
    # for c, v in crowd_map.items():
    #     delete_expired_value(key, c, v)


def delete_expired_value(key, crowd_map: dict, all_crowd_map: dict, client):
    """
    批量查询人群编号
    :param key:
    :param crowd_map:
    :return:
    """
    delete_count = 0
    redis_client = client
    # delete_crowd_list = ['N41480', 'N32018', 'N32019', 'N32020', 'N32021', 'N32022', 'N32023', 'N32024', 'N32025',
    #                      'N33599', 'N33600']
    delete_crowd_list = deleteCrowdList.split(",")
    #   print(crowd_map)
    with redis_client.pipeline(transaction=False) as p:
        keylist = list(crowd_map.keys())
        # crowd_version_list = redis_client.mget(keylist)
        for index, c in enumerate(keylist):
            v = crowd_map[c]

            crowd_version_info = all_crowd_map.get(c)
            #            print("=============== 获取到缓存人群包数据", crowd_version_info)

            # 如果不存在，表示人群包过期了，需要删除人群包
            if crowd_version_info is None:
                if c in delete_crowd_list:
                    print("key=", key, c, "不存在，已过期删除")
                    p.hdel(key, c)
                    delete_count = delete_count + 1
                # redis_client.hdel(key, c)
                # p.hdel(key, c)
                # delete_count = delete_count + 1
            else:
                c_j = crowd_version_info
                if v.decode("utf-8") < str(c_j.get("v")):
                    print(key, "版本过期，删除人群包", c, v)
                    # redis_client.hdel(key, c)
                    p.hdel(key, c)
                    delete_count = delete_count + 1
        p.execute()

    return delete_count


if __name__ == '__main__':
    matchstr = sys.argv[1]
    deleteCrowdList = sys.argv[2]
    print(deleteCrowdList.split(","))
    scan_redis_key(matchstr)

