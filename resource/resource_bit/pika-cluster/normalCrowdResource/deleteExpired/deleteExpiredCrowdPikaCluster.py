#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
-- file name     : deleteExpiredCrowd_2.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 20230627
-- copyright     : @qctx
-- function list : 删除过期的key 格式  key : value1,value2,value3
-- history       : 第一版脚本，无历史
-- desc          : scan命令在python2.7中不支持，需要python3，该脚本使用python3执行
'''
__author__ = 'zhangyong'

import datetime
import json
import multiprocessing
import os
import subprocess
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

# 获取 utils 的路径
parent_directory2 = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'utils'))
sys.path.append(parent_directory2)

from deleteRedisPoolManager import DeleteRedisPoolManager, get_client
import pikaClusterClientUtil


'''
-- file name     : deleteExpiredCrowd.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-06-12
-- description   : 删除redis已经过期的人群包信息
'''


class DeleteExpired(object):
    @staticmethod
    def get_redis_client():
        """config_name  一体化pika"""
        redis_pool = DeleteRedisPoolManager.redis_pool_instance
        return redis_pool.connect()

    @staticmethod
    def get_redis_client2(crowdtype):
        """config_name2 普通策略新pika"""
        # redis_pool = DeleteRedisPoolManager.redis_pool_instance2
        # return redis_pool.connect()
        return get_client(crowdtype).connect()


def delete_expired_value(pikaLusterClientUtil, key, crowd_no_list_str=None):
    """
    批量查询人群编号
    :param key:
    :param crowd_map:
    :return:
    """
    if not crowd_no_list_str:
        return

    crowd_no_list = list(crowd_no_list_str.split(","))
    crowd_version_list = pikaLusterClientUtil.hmget(key, crowd_no_list)

    if not crowd_version_list:
        print("key=%s,crowd_version_list=%s 不在资源位", (key, crowd_version_list))
        return

    crowd_version_info_list = pikaLusterClientUtil.mget(crowd_no_list)
    # 如果不存在，表示人群包过期了，需要删除人群包
    if crowd_version_info_list is None:
        print("pika cluster key=", key, crowd_version_info_list, "不存在，已过期删除", )
        pikaLusterClientUtil.hdel(key, crowd_version_info_list)
    else:
        pikaLusterClientUtil.r_expire_data_clear(key,crowd_no_list,crowd_version_info_list,crowd_version_list)


def remove_expired_crowd(local_expired_file_path):
    print("pika cluster local_expired_file_path", local_expired_file_path)
    if not os.path.exists(local_expired_file_path):
        return

    # iscClient = DeleteExpired.get_redis_client2("2")
    # normalClient = DeleteExpired.get_redis_client2("1")
    pikaLusterClientUtil = pikaClusterClientUtil.PikaClusterClientUtil(pikaClusterHost, pikaClusterPort, pikaClusterPsw)
    with open(local_expired_file_path, "r") as f:
        for line in f:
            (userno, crowdno_list, crowd_type) = line.split("\t")
            user_no = userno.strip()
            crowd_no_list = crowdno_list.strip()
            crowdType = crowd_type.strip()
            if "2" == crowdType:
                # delete_expired_value(iscClient, "ISC_" + user_no, crowd_no_list)
                continue
            if "1" == crowdType:
                delete_expired_value(pikaLusterClientUtil, "R_" + user_no, crowd_no_list)

    return local_expired_file_path


def splitFile(local_path_file):
    files = []
    cmd = """split -d -50000 -a 3 %s %s_ && ls -l %s | grep %s_ | awk '{print $NF}'
      """ % (local_path_file, local_path_file, os.path.dirname(local_path_file),
             os.path.basename(local_path_file))
    print(cmd)
    status, output = subprocess.getstatusoutput(cmd)
    if status != 0:
        print(output)
        raise Exception("文件切分失败")
    files.extend(output.split("\n"))
    print(files)
    return files


def remove_expired_dir(filepath):
    dir_path = os.path.dirname(filepath)
    split_files = splitFile(filepath)
    pool = multiprocessing.Pool(processes=10)
    for item in split_files:
        file = f"{dir_path}/{item}"
        pool.apply_async(remove_expired_crowd, [file], callback=callback)
    pool.close()
    pool.join()


def callback(obj):
    print("%s处理结束" % obj)


if __name__ == '__main__':
    local_expired_file_path = sys.argv[1]
    pikaClusterHost = sys.argv[2]
    pikaClusterPort = int(sys.argv[3])
    pikaClusterPsw = sys.argv[4]
    # local_expired_file_path = 'd:/data/logs/test_expired.data'
    start = datetime.datetime.now()
    remove_expired_dir(local_expired_file_path)
    end = datetime.datetime.now()
    print("删除过期数据结束，耗时=%s" % (end - start))
    # DeleteExpired.get_redis_client().get("1")
    # DeleteExpired.get_redis_client2().get("2")
