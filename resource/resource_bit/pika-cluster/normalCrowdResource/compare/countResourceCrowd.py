#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
-- file name     : deleteExpiredCrowd.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 20230627
-- copyright     : @qctx
-- function list : 删除过期的key
-- history       : 第一版脚本，无历史
-- desc          : scan命令在python2.7中不支持，需要python3，该脚本使用python3执行
'''
__author__ = 'zhangyong'

import os
import sys
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

from common.redisPoolManager import RedisPoolManager

'''
    -- file name     : countResourceCrowd.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-06-12
-- description   : 删除redis已经过期的人群包信息
'''


class CountKeys(object):
    @staticmethod
    def get_redis_client():
        redis_pool = RedisPoolManager.redis_pool_instance

        return redis_pool.connect()


def scan_redis_key():
    batch_size = 5000
    start = datetime.now()
    cursor = 0
    redis_client = CountKeys.get_redis_client()
    try:
        count = 0
        while True:
            cursor, keys = redis_client.scan(cursor, count=batch_size, match='R_UR*')
            count = count + len(keys)

            if cursor == 0:
                print("统计人群包处理结束")
                break
        end = datetime.now()

        print("统计人群包处理结束，耗时：%s ,数量：%s" % (str(end - start), count))
    finally:
        redis_client.close()


if __name__ == '__main__':
    scan_redis_key()
