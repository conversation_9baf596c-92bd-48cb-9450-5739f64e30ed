#!/usr/bin/python
# -*- coding: utf-8 -*-

import datetime
# 文件路径，人群包版本，
import sys

from pyspark import Row
from pyspark.sql import SparkSession
import redis
import random
import socket

class PikaClusterClientUtil:
    def __init__(self, host, port,passwrd):
        """
        初始化 RedisMasterSlaveUtil，连接到 Redis 主从架构
        :param host: 主节点的 IP 或主机名
        :param port: 主节点的端口号
        :param db: Redis 数据库索引（默认为 0）
        """
        # 创建 Redis 客户端连接，连接主节点
        self.host=host
        self.passwrd=passwrd
        self.port=port
        self.pool = redis.ConnectionPool(host=self.host, port=self.port,password=self.passwrd, socket_keepalive=True,
                                         socket_keepalive_options={socket.TCP_KEEPIDLE: 90,
                                                                   socket.TCP_KEEPCNT: 2,
                                                                   socket.TCP_KEEPINTVL: 20
                                                                   })
        self.master = redis.StrictRedis(connection_pool=self.pool)

class CheckBagVersion:
    writeCountAccum = None

    @classmethod
    def create_pika_cluster(cls):
        return PikaClusterClientUtil(pikaClusterHost, pikaClusterPort, pikaClusterPsw)

    @classmethod
    def checkBagVersion(cls):
        spark = SparkSession.builder.enableHiveSupport().getOrCreate()
        sc = spark.sparkContext
        cls.writeCountAccum = sc.accumulator(0)
        configData = sc.textFile(config_file_path).map(lambda line: line.split("@@@"))
        finalHdfsPath = ""

        if exec_type.startswith('defined'):
            config_data = spark.createDataFrame(
                configData.map(lambda p: Row(number=p[0], filename=p[1], update_time_str=p[2], crowd_version=p[3])))
            config_data.registerTempTable("baseData")
            sql0 = "select number,filename,crowd_version from baseData"
            df0 = spark.sql(sql0)
            rows = df0.collect()
            finalHdfsPath = ",".join(hdfs_file_path + "/" + str(p["number"]) + "_" + str(p["filename"]) for p in rows)
        else:
            config_data = spark.createDataFrame(
                configData.map(lambda p: Row(number=p[0],bag_class=p[5],  crowd_version=p[7])))
            config_data.registerTempTable("baseData")
            sql0 = "select number,bag_class,crowd_version from baseData"
            df0 = spark.sql(sql0)
            rows = df0.collect()
            finalHdfsPath = ",".join(hdfs_file_path + "/" + cls.convertBagClassToBusinessType(bag_class=str(p["bag_class"]))+"/" + str(p["number"]) + "_*" for p in rows)
        print("finalHdfsPath=", finalHdfsPath)
        if finalHdfsPath == '':
            raise Exception("资源位-资源位人群包文件路径不存在")

        crowdNoList = ",".join(("'" + str(p["number"] + "'") for p in rows))
        crowdVersion = (lambda x: [p['crowd_version'] for p in x][0] if x else None)(rows)

        if not crowdVersion:
            raise Exception("资源位-资源位人群包版本校验")

        # 将文件路径传入进来
        hdfsData = sc.textFile(finalHdfsPath).map(lambda line: line.split("\t"))
        data = spark.createDataFrame(hdfsData.map(lambda p: Row(pika_key=p[0], pika_value=p[1])))
        data.registerTempTable("tableA")

        sql = "select  pika_key , pika_value from (select b.pika_key,b.pika_value,ROW_NUMBER() over (partition by pika_value order by pika_key desc) as topRank  from tableA b where b.pika_value in (" + crowdNoList + ")) t  where t.topRank <1000"
        print("sql=",sql)
        df = spark.sql(sql)
        df.repartition(16).foreachPartition(lambda lines: cls.checkPikaClusterVersion(crowdVersion, lines))
        # 更新版本信息
        datestr = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print("==========%s 批量检查条数:%s =============" % (datestr, cls.writeCountAccum.value))

    @classmethod
    def checkBagVersionSingle(cls):
        spark = SparkSession.builder.enableHiveSupport().getOrCreate()
        sc = spark.sparkContext
        cls.writeCountAccum = sc.accumulator(0)
        configData = sc.textFile(config_file_path).map(lambda line: line.split("@@@"))
        for parts in configData.collect():
            # print("part值为:",parts)
            try:
                if exec_type.startswith('defined'):
                    number = parts[0]
                    filename = parts[1]
                    update_time_str = parts[2]
                    crowd_version = parts[3]
                    config_data = spark.createDataFrame([Row(number=number, filename=filename, update_time_str=update_time_str, crowd_version=crowd_version)])
                    config_data.registerTempTable("baseData")
                    sql0 = "select number,filename,crowd_version from baseData"
                    df0 = spark.sql(sql0)
                    rows = df0.collect()
                    finalHdfsPath = ",".join(hdfs_file_path + "/" + str(p["number"]) + "_" + str(p["filename"]) for p in rows)
                else:
                    number = parts[0]
                    bag_class = parts[5]
                    crowd_version = parts[7]
                    print("now=",datetime.datetime.now() , " number=", number, " bag_class=", bag_class, " crowd_version=", crowd_version)
                    config_data = spark.createDataFrame([Row(number=str(number), bag_class=str(bag_class), crowd_version=str(crowd_version))])
                    config_data.registerTempTable("baseData")
                    sql0 = "select number,bag_class,crowd_version from baseData"
                    df0 = spark.sql(sql0)
                    rows = df0.collect()
                    finalHdfsPath = ",".join(hdfs_file_path + "/" + cls.convertBagClassToBusinessType(bag_class=str(bag_class)) + "/" + str(p["number"]) + "_*" for p in rows)
                print("finalHdfsPath=", finalHdfsPath)
                if not finalHdfsPath:
                    print("pika cluster 资源位-资源位人群包文件路径不存在")
                    continue
                    # raise Exception("资源位-资源位人群包文件路径不存在")

                # 将文件路径传入进来
                hdfsData = sc.textFile(finalHdfsPath).map(lambda line: line.split("\t"))
                data = spark.createDataFrame(hdfsData.map(lambda p: Row(pika_key=p[0], pika_value=p[1])))
                data.registerTempTable("tableA")

                sql = "select b.pika_key,b.pika_value from tableA b where b.pika_value = '" + number + "' limit 1000"
                print("sql=", sql)
                df = spark.sql(sql)
                df.repartition(8).foreachPartition(lambda lines: cls.checkPikaClusterVersion(crowd_version, lines))
            except Exception as e:
                print("pika cluster 校验数据异常:,parts=",parts,e)

            # 更新版本信息
            datestr = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print("==========%s 批量检查条数:%s =============" % (datestr, cls.writeCountAccum.value))

    @classmethod
    def checkPikaClusterVersion(cls, crowd_version, lines):
        if not lines:
            print("当前数据为空")
            raise Exception("资源位-资源位人群包版本校验,数据为空")

        pClusterClient = cls.create_pika_cluster()

        count = 0
        try:
            for line in lines:
                key = line["pika_key"]
                value = line["pika_value"]
                # print(" value=",value)
                if len(key) > 0:
                    cls.writeCountAccum.add(1)  # Increment accumulator
                    final_user_no = key.strip()
                    if prefix:
                        final_user_no = prefix + final_user_no
                    redis_crowd_version = pClusterClient.master.hget(final_user_no, value)
                    if count == 0:
                        print("r_crowd_version=",redis_crowd_version," final_user_no=",final_user_no," value=",value)
                    count += 1
                    if not redis_crowd_version :
                        raise Exception("pika cluster 资源位-资源位人群包版本校验版本不存在,final_user_no=",final_user_no," crowd_version=",crowd_version," crowno=", str(value))
                    redis_crowd_version_str = redis_crowd_version.decode('utf-8')
                    if redis_crowd_version_str < crowd_version:
                        raise Exception("pika cluster 资源位-资源位人群包版本校验版本未更新,final_user_no=", final_user_no,
                                        " redis_crowd_version_str=", redis_crowd_version_str, " crowd_version=", crowd_version,
                                        " crowno=", str(value))
            print("checkPikaClusterVersion count=", count)
        finally:
            pClusterClient.master.close()

    @staticmethod
    def convertBagClassToBusinessType(bag_class):
        bagClass = str(bag_class)
        business_type = ''
        if bagClass in ("1", "3", "5"):
            business_type = "CREDITLOAN"
        elif bagClass in ("4"):
            business_type = "NICAIFU"
        elif bagClass in ("999"):
            business_type = "JIETIAONINEC"
        return business_type

if __name__ == '__main__':
    pikaClusterHost = sys.argv[1]
    pikaClusterPort = int(sys.argv[2])
    pikaClusterPsw = sys.argv[3]
    config_file_path = sys.argv[4]
    hdfs_file_path = sys.argv[5]
    prefix = sys.argv[6]
    exec_type = sys.argv[7]
    # CheckBagVersion.checkBagVersion()
    CheckBagVersion.checkBagVersionSingle()
