#!/bin/bash


#!/bin/bash
##  file name     : newResouceSinkShell.sh
##  author        : luoxin
##  version       : v1.0
##  date          : 2024-07-02
##  copyright     : @qctx
##  description   : 借条业务线人群组合
##  usage         : 借条业务线人群组合
##  function list :
##  history       : 第一版脚本
export dataCenter=${dataCenter}
# set -e
set -x

unzip test_resource_bit_lx.zip

cwd=$(
  cd $(dirname $0)
  pwd
)
source /home/<USER>/anaconda_yushu_jds/bin/activate py36

parentScriptDir=$cwd
pythonPath=python3

redisHost=**************
redisPort=25877
redisPsw=5fc544524941253b
redisDB=0
exe_type=defined_big

$pythonPath $parentScriptDir/utils/pikaClusterClientUtil.py
result=$?
if [ $result -ne 0 ] ;then
    exit 1
fi
