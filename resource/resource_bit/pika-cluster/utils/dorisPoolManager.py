#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
-- file name     : DorisManager.py
-- author        : <PERSON><PERSON><PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2023-03-25
-- copyright     : @qctx
-- function list : 数据服务新环境版本
-- history       : 第一版脚本，无历史
"""
import os
import sys

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)

from utils.DorisPool import DorisPool


class DorisPoolManager:

    def __init__(self):
        pass

    # 离线标签
    offline_label_config = {
        'host': '*************',
        'port': 9030,
        'user': 'tag_prd',
        'password': 'inW/OX/dL87QY5kR',
        'database': 'fin_ads_online',
        'charset': 'utf8mb4',
        'max_connections': 10
    }
    offline_doris_pool = DorisPool(offline_label_config)
