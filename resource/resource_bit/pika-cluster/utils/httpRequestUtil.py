#!/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import os
import sys
import time

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)
import requests


def requestCrowdRuleParse(dataJson):
    headers = {'Content-Type': 'application/json'}
    url = "http://gws-bps.daikuan.360.cn/ucs/getCrowdRuleSql"
    # url = "http://stg3-gws-bps-web.daikuan.360.cn/ucs/getCrowdRuleSql"
    for i in range(4):
        try:
            res = requests.post(url, data=json.dumps(dataJson), headers=headers, verify=False).json()
            # print(res)
            if res["status"] == '200':
                return res["data"]
            else:
                print(res["msg"])
                raise Exception("请求人群包规则解析服务异常")
        except Exception as e:
            print(e)
            time.sleep(10)
            if i == 3:
                raise Exception("请求3次请求人群包规则解析服务异常，程序退出")


def requestDelayCrowdRule(dataJson):
    headers = {'Content-Type': 'application/json'}
    url = "http://gws-bps.daikuan.360.cn/ucs/getCrowdRuleSql"
    # url = "http://stg3-gws-bps-web.daikuan.360.cn/ucs/getSqlResourceBit"
    for i in range(4):
        try:
            res = requests.post(url, data=json.dumps(dataJson), headers=headers, verify=False).json()
            # print(res)
            if res["status"] == '200':
                return res["data"]
            else:
                raise Exception("请求人群包规则解析服务异常")
        except Exception as e:
            print(e)
            time.sleep(10)
            if i == 3:
                sys.exit(1)

if __name__ == '__main__':
    data=  {"bagType": "1", "ruleJson": "{\"or###not_must_tag\":{\"action_tag_rule\":{\"or###action_parent\":[{\"or###action_child\":[{\"action_sub\":{\"condition\":\"dtappl_loan\",\"kd1\":{\"date_type\":\"roll\",\"to_date\":14.0,\"up_to_date\":1.0},\"action\":\"must\",\"cond_or\":[{\"condition\":[\"JT\"],\"operate\":\"eq\",\"cond\":\"k365\"}]}}]},{\"or###action_child\":[{\"action_sub\":{\"condition\":\"loan_fk_refuse\",\"action\":\"must\",\"cond_or\":[{\"condition\":[\"30\"],\"operate\":\"lte\",\"cond\":\"krr380\"}]}}]},{\"or###action_child\":[{\"action_sub\":{\"condition\":\"black_type\",\"action\":\"must\",\"cond_or\":[{\"condition\":[\"black_001\",\"black_002\",\"black_005\",\"black_006\"],\"operate\":\"eq\",\"cond\":\"k400\"}]}}]}]},\"property_tag_rule\":{\"or###attribute_tag_level\":[{\"operate\":\"eq\",\"tag_value\":[\"\u5458\u5de5\",\"\u516c\u68c0\u6cd5\",\"\u654f\u611f\",\"\u7ba1\u5236\"],\"tag_key\":\"k2\"},{\"operate\":\"gte_lte\",\"tag_value\":[\"5-99999\"],\"tag_key\":\"krr310\"},{\"operate\":\"eq\",\"tag_value\":[\"Y\"],\"tag_key\":\"k458\"},{\"operate\":\"eq\",\"tag_value\":[\"\u662f\"],\"tag_key\":\"k122\"}]}},\"and###must_tag\":{\"action_tag_rule\":{\"and###action_parent\":[{\"or###action_child\":[{\"action_sub\":{\"cond_and\":[{\"condition\":[\"SHJT\"],\"operate\":\"eq\",\"cond\":\"k723\"},{\"condition\":[\"5000\"],\"operate\":\"gt\",\"cond\":\"krr720\"},{\"condition\":[\"10000\"],\"operate\":\"lte\",\"cond\":\"krr720\"}],\"condition\":\"all_product_remain_amount\",\"action\":\"must\"}}]},{\"or###action_child\":[{\"action_sub\":{\"condition\":\"appl_noloan\",\"kd1\":{\"end_date\":\"2023-08-07\",\"date_type\":\"normal\",\"start_date\":\"2023-08-07\"},\"action\":\"must\",\"cond_or\":[{\"condition\":[\"all_product\"],\"operate\":\"eq\",\"cond\":\"k243\"}]}}]}]},\"property_tag_rule\":{\"and###attribute_tag_level\":[{\"operate\":\"eq\",\"tag_value\":[\"\u5927\u989d\"],\"tag_key\":\"k56\"},{\"operate\":\"gte_lte\",\"tag_value\":[\"30-9999999\"],\"tag_key\":\"krr263\"},{\"operate\":\"eq\",\"tag_value\":[\"1\",\"2\",\"3\",\"4\",\"8\",\"9\",\"5\"],\"tag_key\":\"k252\"},{\"operate\":\"eq\",\"tag_value\":[\"N\"],\"tag_key\":\"k777\"}]}}}", "version": "v3", "dataMechanism": "1", "businessType": "1"}

    requestCrowdRuleParse(data)