#!/bin/bash
##  file name     : alarm.sh
##  author        : l<PERSON><PERSON><PERSON>
##  version       : v1.0
##  date          : 2023-03-09
##  copyright     : @360shuke.com
##  description   : alarm
##  usage         : alarm
##  function list :
##  history       : 第一版脚本，无历史
# set -e

## 入参(content)

send_notice(){
    if [ -z "$1" ]; then
      echo "非法入参:$1"
      exit 1
    fi
    content=$1
    echo "${content}"

    # curl
    alarm_url="https://im.360teams.com/api/qfin-api/rce-app/robot/send?access_token=0928611ca7964f91b622ce1fb805ec593be249de99834d9eaa372990241ad265"
    echo "alarm_url:${alarm_url}"

    # alarm
    echo `curl -X POST -H "Content-type:application/json" -d '{"msgtype": "text", "text": {"content": "'$content'"},"at": {"isAtAll": true, "userIds": [] }}' $alarm_url`

    }


