#!/bin/python
# -*- coding: utf-8 -*-
import os
import sys
import traceback
from importlib import reload

import pymysql

from realLabelReconsitutionMvNew.offline_label_application_v3.utils.DorisPool import DorisPool

reload(sys)
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from pyspark.sql.functions import *
from pyspark.sql import *

class TestMap(object):

    def __init__(self):
        super(TestMap, self).__init__()

    def testmap(self):
        scaleList = getScale()
        intscale = map(eval, scaleList)
        print(list(intscale))


def getScale():
    scaleList = []
    scaleList.append("0.3")
    scaleList.append("0.7")
    return scaleList

def square(x):
    return x**2

if __name__ == '__main__':
    testMap = TestMap()
    testMap.testmap()

    numbers =[1,2,3,4,5]
    squares = map(square,numbers)
    print(list(squares))


    offline_label_config = {
        'host': '*************',
        'port': 2076,
        'user': 'jt_label_wr',
        'password': '40d73b9744186b33',
        'database': 'jt_label',
        'charset': 'utf8mb4',
        'max_connections': 10
    }
    offline_doris_pool = DorisPool(offline_label_config)

    filterSql = '''select * from fa_plan_user '''
    connect = offline_doris_pool.get_connection()
    cursor = connect.cursor(pymysql.cursors.SSCursor)
    count = 0
    try:
        # cursor.execute("set query_timeout=7200")
        cursor.execute(filterSql)
        while True:
            result = cursor.fetchmany(1000)
            print("changdu=%d" % len(result))
            if not result:
                break
            for row in result:
                print(row)
                count += 1
        print("结束====")
    except Exception as e:
        traceback.print_exc()
        print(e)
    finally:
        print("关闭游标")
        cursor.close()
        connect.close()
