import multiprocessing
import traceback
from datetime import datetime, timedelta

import pandas

from common.redisPoolManager import get_client
from utils import SinkLocalData2redis


def cuont():
    count = 0
    with SinkLocalData2redis.get_client().connect().pipeline() as p:
        try:
            for i in range(5):
                p.get("TEST_94")
                # time.sleep(0)
                count = count + 1
        except Exception as e:
            traceback.print_stack()
        print(p.execute())
    print(count)
    return count


def getstr(i):
    count = cuont()

    print("===", count)


def split_list(keys, n):
    if not len(keys):
        return []
    if len(keys) <= n:
        return keys
    return [keys[i: i + n] for i in range(0, len(keys), n)]


def printa(*a, **b):
    print(a, b)


def multi_process():
    total = 300
    count = 0
    pool = multiprocessing.Pool(processes=1)
    pool.apply(printa, args=[1, 2, 3], kwds={"1": get_client()})
    pool.close()
    pool.join()
    # while True:
    #     pool = multiprocessing.Pool(processes=4)
    #     for i in range(500):
    #         pool.apply_async(getstr, args=[i])
    #     pool.close()
    #     pool.join()
    #
    #     count = count + 100
    #     print(count)
    #     if count >= total:
    #         break


def redis_():
    client = get_client().connect()
    # map = client.hgetall("ISC_UR6307767968417714177")
    # keys = map.keys()
    # crowd_version_list = client.mget(map.keys())
    #
    # for i, key in enumerate(keys):
    #     print(map[key])
    #     crowd_version_info = crowd_version_list[i]
    #     print(crowd_version_info)
    #     c_j = json.loads(crowd_version_info)
    #
    #     a = "2"
    #     print(a, str(a))
    #     print(map[key].decode("utf-8") < str(c_j['v']))
    print(client.hmget("N1111"))


def mysql1():
    line = "22212Exception1111"

    print(not line)
    if line.index("Exception"):
        print("2123123")
    print(not line.index("Exception"))


def readcsv():
    df = pandas.read_csv(
        "C:/Users/<USER>/Downloads/20230817_2302_resource_compare_temp_table_Presto_SAFE_LYCC.csv")
    # ddf = df.items()
    values = df.values
    count = 0
    sets = set()

    for v in values:
        ur = v[0]
        old = v[1]
        list1 = set(old.split(","))
        # list1.sort()
        new = v[2]
        list2 = set(new.split(","))
        # list2.sort()
        # print(list1.symmetric_difference(list2))
        if len(list1.symmetric_difference(list2)) > 0:
            print(len(list1), len(list2))
            print("在list1 不在list2", getDiff(list1, list2), " 在list2 不在list1 ", getDiff(list2, list1))
            list3 = getDiff(list1, list2)
            list3.extend(getDiff(list2, list1))
            sets.update(list3)

            # list3.append(list1.symmetric_difference(list2))
            print(ur, list1.symmetric_difference(list2))
            count = count + 1
    print("不一致数量 count", count)
    print("不一致数量人群包", sets)
    # for label,content in ddf:
    #     print(label,content)


def getDiff(list1, list2):
    list3 = []
    for l in list1:
        if l not in list2:
            list3.append(l)
    return list3


def write_test_data():
    with open("d:/data/logs/test_expired.data", "wt") as f:
        for i in range(200):
            f.write("R_UR" + str(i) + "\t" + "N1,N2,N3" + "\t" + "1" + "\n")


def write_test_data_redis():
    client = get_client().connect()
    for i in range(200):
        client.hset("R_UR" + str(i), "N" + str(i % 5), "1")
        client.hset("R_UR" + str(i), "N" + str(i % 3), "1")


def removeDuli():
    s = "N39590,N35966,N22425,N36853,N38186,N24443,N31942,N28435,N24443,N24137,N31942,N26189,N31809,N31033,N37890,N26380,N35810,N23714,N25721,N22877,N36654,N36533,N37531,N30459,N30458,N34498,N35606,N25900,N23929,N35229,N37027,N35499,N39565,N22377,N38997,N35966,N22425,N18943,N36853,N31030,N24128,N38186,N23823,N33141,N24398,N35612,N31924,N39447,N22882,N30456,N36532,N30530,N38704,N13836,N22376,N27016,N39061,N24768,N22880,N27121,N38068,N14676,N38113,N28930,N21070,N38607,N24690,N24226,N14376,N33591,N26175,N21216,N26726,N25564,N26189,N35612,N31924,N31033,N26380,N37890,N22882,N35810,N23714,N36532,N30530,N22877,N36654,N36533,N27016,N37531,N39061,N34498,N22880,N35606,N25900,N38113,N35229,N38607,N21070,N35499,N24690,N39565,N33591,N21216,N26726,N25564"
    list = sorted(set(s.split(",")))
    print(",".join(list))


def rounded_to_the_last_30th_minute_epoch():
    now = datetime.now()
    # now = datetime.strptime("2023-08-23 20:33:11", "%Y-%m-%d %H:%M:%S")
    print(timedelta(minutes=39))
    print((now - datetime.min))
    print((now - datetime.min) % timedelta(minutes=30))
    print(now)
    rounded = now - (now - datetime.min) % timedelta(minutes=30)
    return rounded


def rang_time():
    now_time = rounded_to_the_last_30th_minute_epoch()
    start_time = now_time - timedelta(minutes=30)
    start_time = start_time.replace(second=0)
    end_time = now_time - timedelta(minutes=1)

    end_time = end_time.replace(second=59)
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    return start_time_str, end_time_str


if __name__ == '__main__':
    print(179 % 5)
    print(179 % 3)
    a = [1, 2, 3, 4, 5, 6, 7, 8, 9]
    # redis_()
    # write_test_data()
    # write_test_data_redis()
    # multi_process()
    # mysql1()
    # a ="20200201\n"
    # b ="20210201\n\n"
    # print(a > b)
    # readcsv()
    # removeDuli()
    # print(os.path.basename("d:/data/logs/EXP_FILE_1663812979"))
    # print(ss.index("Exception"))
    now_str = rounded_to_the_last_30th_minute_epoch()

    # print(dateminute)
    # dateminute = datetime.now().minute
    # if dateminute % 10 > 0:
    #     dateminte = dateminute // 10
    #
    # print(dateminute)
    # print(datetime.strptime("2023-08-23 20:19:11", "%Y-%m-%d %H:%M:%S"))
    #
    # dateminute = datetime.now().second
    # print(dateminute)
