#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'caoyanwei'

import sys

'''
-- file name     : sinkData2redis.py
-- author        : caoyan<PERSON>
-- version       : v1.0
-- date          : 2022-10-28
-- copyright     : @qctx
-- description   : 通用数据上传redis
-- usage         : 通用数据上传redis
-- function list : 实时事件数据上传至redis(非实时事件定制话处理需要单独开发)
-- history       : 第一版脚本，无历史
'''
import commands
import os
import redis
import traceback

class SinkData2Redis(object):
    def __init__(self, redisHost, redisPort, redisPsw, redisDB, clusterPath, localPathAndFile):
        '''
        :param redisHost:
        :param redisPort:
        :param redisPsw:
        :param redisDB:
        :param clusterPath:  集群的数据路径，数据必须为textfile 格式且数据必须为 \001分割
        :param localPathAndFile: 本地路径需要指定数据路径
        '''
        self.redisHost = redisHost
        self.redisPort = redisPort
        self.redisPsw = redisPsw
        self.redisDB = redisDB
        self.clusterPath = clusterPath
        self.localPathAndFile = localPathAndFile

    def connect(self):
        pool = redis.ConnectionPool(host=self.redisHost, port=self.redisPort, db=self.redisDB, password=self.redisPsw)
        connect = redis.StrictRedis(connection_pool=pool)
        return connect

    def getMergeHdfs(self):
        cmd = '''rm -f %s;hadoop fs -getmerge %s %s''' % (
        self.localPathAndFile, self.clusterPath, self.localPathAndFile)

        status, output = commands.getstatusoutput(cmd)
        if status == 0:
            print ("========== getmerge cluster path %s 路径执行 ============= 成功." % (self.clusterPath))
        else:
            print ("========== getmerge cluster path %s 路径执行 ============= 失败.失败原因 %s " % (self.clusterPath), output)
            sys.exit(1)

    def getDataToRedis(self):
        '''
        :To: batch pipeline sink data to redis
        '''
        if os.path.exists(self.localPathAndFile):
            filename = self.localPathAndFile
            batch_size = 100000
            with open(filename) as file:
                try:
                    count = 0
                    writeCount = 0
                    with self.connect().pipeline(transaction=False) as p:
                        for lines in file:
                            (user_no, value) = lines.split('\t')
                            count = count + 1
                            if len(user_no) > 0:
                                p.set(user_no, value.strip())
                                if not count % batch_size:
                                    writeCount += count
                                    p.execute()
                                    print("========== 批量 sink data to redis  条数:%s =============" % (writeCount))
                                    count = 0
                        # send the last batch
                        writeCount += count
                        print("========== 批量 sink data to redis  条数:%s =============" % (writeCount))
                        p.execute()
                except Exception   as e:
                    traceback.print_exc()
                    print("========== sink data to redis  exception ============= failed,异常原因:%s." % (e))
                    sys.exit(1)
            file.close()
            print("========== batch sink data to redis success =============")

    def rmrDataCommand(self):
        '''
        :Todo: local data remove
        '''
        cmd = '''rm -f %s ''' % (self.localPathAndFile)
        status, output = commands.getstatusoutput(cmd)
        if status == 0:
            print("========== remove local data success =============")
        else:
            print("========== remove local data failed,原因:%s=============" % (output))
            sys.exit(1)


if __name__ == '__main__':
    print( "===========")
    redisHost = sys.argv[1]
    redisPort = sys.argv[2]
    redisPsw = sys.argv[3]
    redisDB = sys.argv[4]
    clusterPath = sys.argv[5]
    localPathAndFile = sys.argv[6]
    f = SinkData2Redis(redisHost, int(redisPort), redisPsw, redisDB, clusterPath, localPathAndFile)
    f.getMergeHdfs()
    f.getDataToRedis()
    f.rmrDataCommand()
