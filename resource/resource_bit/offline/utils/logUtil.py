#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
-- file name     : logUtil.py
-- author        : ca<PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2018-11-27
-- copyright     : @qctx
-- function list : 记录执行日志.进行拆分
-- history       : 第一版脚本，无历史
'''
import logging
import ConfigParser
import os
import time
from logging.handlers import TimedRotatingFileHandler


def Log(logFileName):
    cf=ConfigParser.ConfigParser()
    confPath= os.path.realpath(__file__)
    confDir= os.path.dirname(confPath)
    cf.read(confDir + "/config/db.conf")
    log_file_path=cf.get("logOutputPath",'logPath')
    logFileName=logFileName
    createDir(log_file_path)
    outPutLog(log_file_path,logFileName)


def createDir(log_file_path):
    if not os.path.exists(log_file_path):
        os.makedirs(log_file_path)

def outPutLog(log_file_path,logFileName):
    #日志文件的存放路径，根据自己的需要去修改
    logger = logging.getLogger()
    if not logger.handlers:
        fileHandler =TimedRotatingFileHandler(filename=log_file_path+"/"+logFileName+"_%s" %(time.strftime('%Y%m%d', time.localtime()))+".log",when='D')
        #日志的输出格式
        fmt = '%(asctime)s - %(filename)s:%(lineno)s  - %(message)s'
        formatter = logging.Formatter(fmt)  # 实例化formatter
        fileHandler.setFormatter(formatter)
        logger.addHandler(fileHandler)
        logger.setLevel(logging.INFO)
