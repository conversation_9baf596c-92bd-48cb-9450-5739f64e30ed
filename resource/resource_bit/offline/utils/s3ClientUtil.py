#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'caoyanwei'

import datetime
import threading
import time

'''
-- file name     : s3ClientConn.py
-- author        : caoyanwei
-- version       : v1.0
-- date          : 2018-12-26
-- copyright     : @qctx
-- description   : s3工具类
-- usage         : 获取s3数据
-- function list : 
-- history       : 第一版脚本，无历史
'''
import os

import configparser
import boto3
from botocore.client import Config


class S3Util(object):
    def __init__(self, filename):
        self.cf = configparser.ConfigParser()
        self.confPath = os.path.realpath(__file__)
        self.confDir = os.path.dirname(self.confPath)
        self.read = self.cf.read(self.confDir + "/config/db.conf")
        self.service = self.cf.get("s3Config", "service")
        self.endpoint_url = self.cf.get("s3Config", "endpoint_url")
        self.aws_secret_access_key = self.cf.get("s3Config", "aws_secret_access_key")
        self.aws_access_key_id = self.cf.get("s3Config", "aws_access_key_id")
        self.bucket_name = self.cf.get("s3Config", "bucket_name")
        self.fileName = filename

    def getS3conn(self):
        s3_cli = boto3.client('s3', self.service,
                              config=Config(signature_version='s3v4'), use_ssl=False,
                              endpoint_url=self.endpoint_url,
                              aws_secret_access_key=self.aws_secret_access_key,
                              aws_access_key_id=self.aws_access_key_id)

        return s3_cli

    def downloadData(self):
        conn = self.getS3conn()
        data = conn.get_object(Bucket=self.bucket_name, Key=self.fileName)

        return data

    def downloadFlie(self):
        conn = self.getS3conn()
        with open(self.fileName, "wb") as data:
            conn.download_fileobj(self.bucket_name, self.fileName, data)
        data.close()

    def downloadFilePath(self, downFilePath):
        conn = self.getS3conn()
        with open(downFilePath + self.fileName, "wb") as data:
            conn.download_fileobj(self.bucket_name, self.fileName, data)
        data.close()

    def uploadFile(self, fileName):
        conn = self.getS3conn()
        with open(self.fileName, 'rb') as data:
            conn.upload_fileobj(data, self.bucket_name, fileName)
        data.close()
        print(fileName)
        print("上传ok")

    def deleteData(self):
        conn = self.getS3conn()
        conn.delete_objects(Bucket=self.bucket_name, Delete={
            'Objects': [
                {
                    'Key': self.fileName
                }
            ]
        })


if __name__ == '__main__':
    S3Util("1").getS3conn()
    # fileName=["N22787_2e9d63f4571411ecb9930425c5e7d56b","N23007_1724cae258c611ecbfbf0425c5e7d56b"]
    fileName=['EXP_FILE_1676622226']
    start = time.time()
    print(start)


    for x  in fileName:
        S3Util(x).downloadFlie()
    end = time.time()
    print(end - start)