; [mysqldb]
; host=**************
; port=14958
; user=jt_label
; pass=520124ef2ddcfd69
; db=jt_label
; [redisdb]
; host=**********
; port=8558
; pass=38143cbb5959a9d0
; db=1
; [dirPath]
; filePath=/home/<USER>/system/credit.label.cn/server/_360_jinrong_digital_intel_plat/data/
[logOutputPath]
logPath=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application/logs
; [shanghaiConfig]
; url=http://***************/openapi/notice.do
; [shenzhenWarehouse]
; url=http://sfs.daikuan.qihoo.net/sfs/hessianservice/v1
; merchantName=mktpartners
; merchantPwd=mkt123456
; service=whs.uploadBytes
; publickey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCu0g9fe8tpLBsCej2Fqm/cFevTzLDwtpBkzswJj6wC5CWgy0m/LR4h60hmO/lQK/Pb8XvZZgZ9dqxqpxzPtA4dFrSOR/AGpSPmiEEquUALwREQY9oVy8QrldzdkRgcvf60xdJZpZjUmlHVKlI6nj34ZkAaFZy3my0lnkPl7klUgwIDAQAB
; [uploadFilePath]
; #uploadPath=/home/<USER>/system/credit.label.cn/web/public/customfile/
[s3Config]
service=pub-bjyt-s3
endpoint_url=http://pub-bjyt.s3.addops.soft.360.cn
aws_secret_access_key=sEO9sOs9yYf5E37JjsCXNReY1lJLEflVodvhzsdg
aws_access_key_id=f1Wz8UnG0sCaUGwzhnJE
bucket_name=definedlabel01
dataPath=/home/<USER>/system/credit.label.cn/server/data
; [requestPostData]
; url=http://*************:8080/web/api/crowd/crowdList
; [signKey]
; sign=7ysPNOEJe6Pz4Zp
; [sftp]
; host=*************
; port=63276
; username=tagsftp
; password=****************
; [insuranceMysql]
; host=***************
; port=3306
; db=bx_label
; username=jt_label
; password=*************************
; [rabitMQ]
; username=j-zhoujiuan-jk
; password=**********
; hostname=**************
; port=5672
; queue=label_self_service
; [clickHouseConfig]
; host=*************
; port=80
; user=ck_dev
; password=****************
; tableName=test.tag_user_hour_realtime_info
; [esConfig]
; es_env_type=test
; jietiao_es_index_name=CREDITLOAN
; nicaifu_es_index_name=NICAIFU
; [grpcRequestConfig]
; host=*************
; port=8388
[emailConfig]
switch=false
appkey=creditdcplat
appsecret=95CDEAF62D4BFBFF0A0BB20F12A36A71
url=http://open.odin.qihoo.net:8360/alarm/message/open/alarm/send