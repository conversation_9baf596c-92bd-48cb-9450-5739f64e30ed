#!/bin/bash
##  file name     : copySafe3Utils.sh
##  author        : caoyanwei
##  version       : v1.0
##  date          : 2019-04-23
##  copyright     : @qctx
##  description   : 工具类
##  usage         : 复制safe3数据到safe.lycc
##  function list :
##  history       : 第一版脚本，无历史

cwd=$(cd $(dirname $0); pwd)

source $cwd/utils/smsUtil.sh

checkdone() {
if [ $# -ne 3 ]; then
echo -e "invalid arguments, usage: \n\tcheckdone donefile checktimes sleepseconds mailto"
return 1;
fi

donefiles=$1
checktimes=$2
sleepseconds=$3
counttimes=0

for donefile in $donefiles; do

while ! hadoop fs -ls "$donefile" &>/dev/null && [ $counttimes -lt $checktimes ]; do
echo "`date '+%Y%m%d %H:%M:%S'` - donefile[$donefile] not exists, sleep $sleepseconds($counttimes/$checktimes)..."
sleep $sleepseconds
let counttimes++
done
if [ $counttimes -ge $checktimes ]; then
location=""
subject="subject: donefile not exists"
message="message:$0: donefile[$donefile] not exists for $checktimes times checks"
#send_sms "$message"
echo -e "  $location \n  $subject \n  $message"
return 1
else
echo "`date '+%Y%m%d %H:%M:%S'` - donefile[$donefile] exists..."
fi
done
}

checkYesterday(){

if [ $# -ne 3 ]; then
echo -e "invalid arguments, usage: \n\tcheckdone donefile checktimes sleepseconds mailto"
return 1;
fi

donefiles=$1
checktimes=$2
sleepseconds=$3
counttimes=0

for donefile in $donefiles; do
while ! hadoop fs -ls "$donefile" &>/dev/null && [ $counttimes -lt $checktimes ]; do
echo "`date '+%Y%m%d %H:%M:%S'` - donefile[$donefile] not exists, sleep $sleepseconds($counttimes/$checktimes)..."
sleep $sleepseconds
let counttimes++
done
if [ $counttimes -ge $checktimes ]; then
location=""
subject="subject: donefile not exists"
message="message: `hostname`@$0: donefile[$donefile] not exists for $checktimes times checks"
echo -e "  $location \n  $subject \n  $message"
return 1
else
echo "`date '+%Y%m%d %H:%M:%S'` - donefile[$donefile] exists..."
fi
done
}
