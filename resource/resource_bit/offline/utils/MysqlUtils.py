#!/usr/bin/python3
# -*- coding: utf-8 -*-

__author__ = 'jinjing'

import traceback

'''
-- file name     : MysqlUtils.py
-- author        : jinjing
-- version       : v1.0
-- date          : 2019-12-05
-- copyright     : @qctx
-- description   : mysql工具类
-- history       : 第一版，无历史
'''

import pymysql
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))

class MysqlUtils:
    def __init__(self,host,port,user,passwd,db):
        self.conn = pymysql.connect(
            host = host,
            port = int(port),
            user = user,
            passwd = passwd,
            db = db,
            charset="utf8",
            local_infile=1
        )

    def selectValues(self,sql):
        cur = self.conn.cursor()
        info = cur.fetchmany(cur.execute(sql))
        cur.close()
        self.conn.commit()
        return info

    def insertValues(self,sql,values):
        cur = None
        try:
            cur = self.conn.cursor()
            cur.executemany(sql,values)
            self.conn.commit()
        except Exception as e:
            print(e)
            self.conn.rollback()
            cur.close()
            traceback.print_stack()

    def executeSql(self,sql):

        try:
            cur = self.conn.cursor()
            cur.execute(sql)
            self.conn.commit()
        except Exception as e:
            print (e)
            self.conn.rollback()
            cur.close()

    def updateValus(self,sql,values):
        try:
            cur = self.conn.cursor()
            cur.execute(sql % values)
            self.conn.commit()
            cur.close()
        except Exception as e:
            print (e)
            self.conn.rollback()
            cur.close()

    def closeDb(self):
        try:
            if self.conn:
                self.conn.close()
        except Exception as e:
            print (e)


if __name__ == "__main__":
    # op = MysqlUtils("10.216.30.109",4213,"ucs","67f6b9193eec55a9","ucs")
    # sql = "show tables"
    op = MysqlUtils("10.203.1.168",2481,"jt_label","520124ef2ddcfd69","jt_label")

    sql= "select * from fa_plan_strategy;"

    line=op.selectValues(sql)
    print (len(line))
