#!/usr/bin/python
# -*- coding: utf-8 -*-
import os.path
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from domain.crowdInfoDomain import CrowdInfoDomain
from dbOperator import Db<PERSON><PERSON>ator


def dos_to_unix(fname):
    with open(fname, 'rt+') as fobj:
        data = fobj.read()
        data = data.replace('\r\n', '\n')
        fobj.seek(0, 0)
        fobj.truncate()
        fobj.write(data)


def callback(result):
    print(" === 执行结束  %s" % result)


class UpdateVersion:

    def __init__(self, exe_type, config_file):
        self.exe_type = exe_type
        self.config_file = config_file

    def run(self):
        if self.exe_type.startswith('label'):
            self.updateLabelResourceBitRecord()
        if self.exe_type.startswith('defined'):
            self.updateDefinedResourceBitRecord()

    def get_business_type_list(self, crowd_list):
        bag_class_list = [str(crowd.bagClass) for crowd in crowd_list]
        return set(bag_class_list)

    def updateDefinedResourceBitRecord(self):
        """
                从s3下载文件
                :return:
                """
        crowd_list = self.getDefinedCrowd()
        if len(crowd_list) == 0:
            return

        self.updateRecord(crowd_list)

    def updateLabelResourceBitRecord(self):
        """从doris下载文件
                 :return:
                 """
        crowd_list = self.getLabelCrowd()
        if len(crowd_list) == 0:
            return
        self.updateRecord(crowd_list)

    def updateRecord(self, crowd_list):
        for crowd in crowd_list:
            crowd_version = crowd.resource_version.strip()
            crowd_no = crowd.number
            DbOperator.update_record(crowd_no, crowd_version)
            DbOperator.update_crowd_version(crowd_no, crowd_version)

    def getDefinedCrowd(self):
        list_data = []
        with open(self.config_file, "r") as file:
            for line in file:
                (number, upload_file_name, update_time_str, resource_version) = line.split("@@@")
                list_data.append(CrowdInfoDomain(number, upload_file_name, number + update_time_str,
                                                 update_time_str=update_time_str,
                                                 resource_version=resource_version.strip()))

        return list_data

    def getLabelCrowd(self):
        list_data = []
        with open(self.config_file, "r") as file:
            for line in file:
                (number, bag_json, version, bag_type, data_mechanism, bag_class, update_time_str,
                 resource_version) = line.split("@@@")
                list_data.append(
                    CrowdInfoDomain(number, "", number + "_" + update_time_str, bag_json, version, bag_type,
                                    data_mechanism,
                                    bag_class, update_time_str, resource_version=resource_version.strip()))

        return list_data


if __name__ == '__main__':
    exetype = sys.argv[1]
    config_file = sys.argv[2]
    UpdateVersion(exetype, config_file).run()
