#!/usr/bin/python
# -*- coding:utf-8 -*-
"""
-- file name     : mrResourceCrowdCompare.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-08002
-- copyright     : @qctx
-- function list : 数据比较 spark
-- history       : 第一版脚本，无历史
"""
import sys

from pyspark import SparkConf, SparkContext, HiveContext, Row

conf = SparkConf().setMaster("yarn-client") \
    .set("spark.hive.mapred.supports.subdirectories", "true").set(
    "spark.hadoop.mapreduce.input.fileinputformat.input.dir.recursive", "true")


class Compare(object):
    def __init__(self, old_path, new_path, out_path, todaymin):
        self.old_path = old_path
        self.new_path = new_path
        self.out_path = out_path
        self.todaymin = todaymin

    def comare(self):
        jobName = "resource_crowd_compare_%s" % self.todaymin
        conf.setAppName(jobName)
        sc = SparkContext(conf=conf)
        sqlContext = HiveContext(sc)

        old_data = sc.textFile(self.old_path, use_unicode=False).map(lambda ln: ln.split("\t"))
        old_data_sql = sqlContext.createDataFrame(old_data.map(lambda p: Row(userno=p[0], crowdno=p[1])))
        old_data_sql.registerTempTable("old_table")
        new_data = sc.textFile(self.new_path, use_unicode=False).map(lambda ln: ln.split("\t"))
        new_data_sql = sqlContext.createDataFrame(new_data.map(lambda p: Row(userno=p[0], crowdno=p[1])))
        new_data_sql.registerTempTable("new_table")
        # df.join(df2, df.name == df2.name, 'outer').select(df.name, df2.height)
        # .sort(desc("name")).collect()
        cond = [old_data_sql.userno == new_data_sql.userno]
        data_frame = old_data_sql.join(new_data_sql, cond, 'outer') \
            .select(old_data_sql.userno.alias('userno'), old_data_sql.crowdno.alias('crowdno'),
                    new_data_sql.crowdno.alias('ncno')) \
            .where("ncno != crowdno")
        # sql = """select distinct  userno ,crowdno from old_table lateral view explode(crowdnolist,',') as crowdno"""
        # old_data_frame = sqlContext.sql(sql)
        data_frame.rdd.repartition(10).map(
            lambda p: p.userno + "\t" + p.crowdno + "\t" + (p.ncno if p.ncno else '')).saveAsTextFile(
            self.out_path)

    def run(self):
        self.comare()


if __name__ == '__main__':
    old_path = sys.argv[1]
    new_path = sys.argv[2]
    out_path = sys.argv[3]
    todaymin = sys.argv[4]
    Compare(old_path, new_path, out_path, todaymin).run()
