#!/usr/bin/python

# -*- coding:utf-8 -*-
"""
-- file name     : normalDefinedCrowdInit.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-08002
-- copyright     : @qctx
-- function list : 数据比较
-- history       : 第一版脚本，无历史
"""
import json
import os
import sys
from concurrent.futures import ThreadPoolExecutor, wait
from datetime import datetime

from normalCrowdResource.compare.OldRedisPoolManager import OldRedisPoolManager
from utils.MysqlConfig import MysqlConfig

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from common.redisPoolManager import RedisPoolManager


def convert_old_crowd(old_crowd_str):
    if len(old_crowd_str) == 0:
        return []
    crowd_no_set = set(old_crowd_str.decode("utf-8").split(","))
    return crowd_no_set


def convert_new_crowd(key, redis_client):
    new_normal_user_prefix = "R_"
    crowd_no_set = set([])
    crowd_map = redis_client.hgetall(new_normal_user_prefix + (key.decode("utf-8")))
    key_list = list(crowd_map.keys())
    crowd_version_list = redis_client.mget(key_list)
    for index, c in enumerate(key_list):
        v = crowd_map[c]
        crowd_version_info = crowd_version_list[index]
        # 如果不存在，表示人群包过期了，需要删除人群包
        if crowd_version_info is not None:
            c_j = json.loads(crowd_version_info)
            if v.decode("utf-8") >= c_j.get("v"):
                crowd_no_set.add(c.decode("utf-8"))
    return crowd_no_set


def compare(keys):
    """
    批量查询人群编号进行比对
    :param key:
    :return:
    """
    redis_client = RedisPoolManager.redis_pool_instance.connect()

    old_redis_client = OldRedisPoolManager.redis_pool_instance.connect()

    old_crowd_list = old_redis_client.mget(keys)
    for index, user_no in enumerate(keys):

        old_crowd_str = old_crowd_list[index]
        old_crowd_set = convert_old_crowd(old_crowd_str)
        new_crowd_set = convert_new_crowd(user_no, redis_client)
        diff_set = old_crowd_set.symmetric_difference(new_crowd_set)

        if len(diff_set) > 0:
            if len(diff_set) != 1 or (len(diff_set) == 1 and diff_set.pop() != ""):
                print("老的和新的不匹配", diff_set)
                #插入到db




def scan_redis_key():
    batch_size = 5000
    start = datetime.now()
    cursor = 0
    redis_client = RedisPoolManager.redis_pool_instance.connect()
    old_redis_client = OldRedisPoolManager.redis_pool_instance.connect()
    try:
        while True:
            cursor, keys = old_redis_client.scan(cursor, count=batch_size, match='UR*')
            futures = []
            with ThreadPoolExecutor(max_workers=15) as executor:
                compareKeys = []
                for key in keys:
                    compareKeys.append(key)
                    if len(compareKeys) == 100:
                        futures.append(executor.submit(compare, compareKeys))
                        compareKeys = []
                if len(compareKeys) > 0:
                    futures.append(executor.submit(compare, compareKeys))
            wait(futures)
            for f in futures:
                print(f.result())
            if cursor == 0:
                print("比对人群包处理结束")
                break

        end = datetime.now()

        print("比对人群包处理耗时：" + str(end - start))
    finally:
        redis_client.close()


if __name__ == '__main__':
    scan_redis_key()
