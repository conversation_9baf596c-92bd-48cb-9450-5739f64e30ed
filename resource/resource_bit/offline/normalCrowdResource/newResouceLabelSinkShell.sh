#!/bin/bash


#!/bin/bash
##  file name     : newResouceLabelSinkShell.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2024-12-18
##  copyright     : @qctx
##  description   : 借条业务资源位
##  usage         : 借条业务资源位
##  function list :
##  history       : 第一版脚本
export dataCenter=${dataCenter}
#set -e
set -x
#unzip resource_bit_2024.zip
cwd=$(
  cd $(dirname $0)
  pwd
)
source /home/<USER>/anaconda_yushu_jds/bin/activate py36

parentScriptDir=$cwd
pythonPath=python3


# 指定appname
# 指定appname
projectName=${jdsInnerPlatformProjectName}
workflowName=${jdsInnerPlatformProcessName}
taskName=${jdsInnerPlatformTaskName}


todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
todayminSecond=$(/bin/date -d-0day "+%Y%m%d%H%M%S")
todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")

redisHost=**************
redisPort=25877
redisPsw=5fc544524941253b
redisDB=0
exe_type=label_today

config_file_path=$cwd/
config_name=conf_${exe_type}_${todayminSecond}

hdfs_file_path=/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test
hdfs_config_file_path=$hdfs_file_path
tempParentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_offline_resource_crowdrule_test
inputDorisHdfs=${tempParentHdfsDir}/pday=$todayyyymmdd

inputDorisHdfs_file=${tempParentHdfsDir}/pday=$todayyyymmdd/*

hdfs_config_file_name=$hdfs_config_file_path/$todayyyymmdd/$config_name
$pythonPath $parentScriptDir/normalCrowdResource/produceCrowdConfig.py $config_file_path $config_name $exe_type
result=$?
if [ $result -ne 0 ] ;then
    exit 1
fi
cat $config_file_path$config_name
### 1.资源位数据落库
if [ ! -e  $config_file_path$config_name ] ||[ ! -s  $config_file_path$config_name  ] ; then
    exit 0
fi
hadoop fs -test -e $hdfs_config_file_path/$todayyyymmdd
result=$?
if [ $result -ne 0 ] ;then
  hadoop fs -mkdir -p $hdfs_config_file_path/$todayyyymmdd
fi
hadoop fs -put $config_file_path$config_name $hdfs_config_file_path/$todayyyymmdd/
flag_success=$?
num=0
until [ $flag_success -eq 0 ]; do
   sleep 10s
   ((num++))
   echo "4444444444"
   if [ $num -gt 3 ]; then
     subject="${todaymin}智能运营，重试三次上传配置文件到hdfs失败"
     echo -e $subject
     exit 1
   else
     echo "5555555555555"
     hadoop fs -put $config_file_path$config_name $hdfs_config_file_path
     flag_success=$?
	   echo "6666666666666666666666"
   fi
done
if [ $num -ge 4 ]; then
     subject="${todaymin}智能运营，重试三次上传配置文件到hdfs失败"
     echo -e $subject
     exit 1
fi

echo "111111111111111111111"
### 拼接文件路径  传递
$pythonPath $parentScriptDir/normalCrowdResource/transferCrowdFileToHdfs.py $parentScriptDir $inputDorisHdfs $exe_type $config_file_path$config_name
result=$?
echo "22222222222222222222"
if [ $result -ne 0 ] ;then
   echo '处理标签人群上传失败'
   exit 1
fi
###### 第一步，资源位数据落pika

py_env=py37_base
spark_env=spark3
# 加载python , spark 环境 。如果py_env指定的是python zip 包，则需要自己去按照源代码的格式指定 python 环境
source /home/<USER>/anaconda_data_ai/ocenv ${py_env} ${spark_env}
export appname=${user_name}:[${spark_env}_shell:[项目名:${projectName}][流程名:${workflowName}][任务名:${taskName}]]
PY_ZIP=hdfs:///user/hive/warehouse/fin_dm_data_ai.db/dm_eco_hdfs_common/pyenv/base37_venvs.tar.gz#pyenv

/usr/hdp/*******-315/spark3.3.2/bin/spark-submit \
--master yarn \
--name ${appname} \
--driver-memory 2G \
--executor-memory 3G \
--conf spark.dynamicAllocation.enabled=true  \
--conf spark.dynamicAllocation.maxExecutors=15 \
--conf spark.dynamicAllocation.minExecutors=5 \
--conf spark.shuffle.service.name=spark3_shuffle_seg2 \
--conf spark.shuffle.service.port=7333 \
--conf spark.shuffle.push.enabled=true \
--conf hive.metastore.dml.events=false \
--conf spark.executor.memoryOverhead=1024 \
--queue yushu_offline_client \
--conf spark.yarn.dist.archives=${PY_ZIP} \
$parentScriptDir/normalCrowdResource/pySparkFileSinkData2Redis.py \
"${redisHost}" "${redisPort}" "${redisPsw}" "${redisDB}" 100000 "1" "${inputDorisHdfs_file}" "R_" "${hdfs_config_file_name}"

sparkFlag=$?
echo "111111111111111111111111111111111111======================"
echo $sparkFlag
if [ $sparkFlag -ne 0 ]; then
    echo "spark submit fail!!!!!"
    exit 1
fi


/usr/hdp/*******-315/spark3.3.2/bin/spark-submit \
--master yarn \
--name ${appname} \
--driver-memory 2G \
--executor-memory 3G \
--conf spark.dynamicAllocation.enabled=true  \
--conf spark.dynamicAllocation.maxExecutors=15 \
--conf spark.dynamicAllocation.minExecutors=5 \
--conf spark.shuffle.service.name=spark3_shuffle_seg2 \
--conf spark.shuffle.service.port=7333 \
--conf spark.shuffle.push.enabled=true \
--conf hive.metastore.dml.events=false \
--conf spark.executor.memoryOverhead=1024 \
--queue yushu_offline_client \
--conf spark.yarn.dist.archives=${PY_ZIP} \
$parentScriptDir/normalCrowdResource/checkWriteData.py \
"${redisHost}" "${redisPort}" "${redisPsw}" "${redisDB}" "${hdfs_config_file_name}"  "${inputDorisHdfs_file}" "R_"


sparkFlag=$?
echo "111111111111111111111111111111111111======================"
echo $sparkFlag
if [ $sparkFlag -ne 0 ]; then
    echo "spark submit fail!!!!!"
    exit 1
fi
echo " start update crowd record"

$pythonPath $parentScriptDir/normalCrowdResource/updateDb.py $exe_type $config_file_path$config_name
