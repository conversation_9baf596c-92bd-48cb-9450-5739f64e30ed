#!/usr/bin/python
# -*- coding: utf-8 -*-
import os.path
import subprocess
import sys
import traceback

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from domain.crowdInfoDomain import CrowdInfoDomain
from common.checkDorisSuccess import <PERSON><PERSON><PERSON>
from common.BusinessMapping import BusinessMapping
from dbOperator import DbOperator
from service.dorisDataService import DorisDataService
from utils import FileUtil
from utils.SinkLocalData2redis import SinkLocalData2Redis
from utils.s3ClientUtil import S3Util
from utils.sendSmsMail import SendMailSms
import multiprocessing


def dos_to_unix(fname):
    with open(fname, 'rt+') as fobj:
        data = fobj.read()
        data = data.replace('\r\n', '\n')
        fobj.seek(0, 0)
        fobj.truncate()
        fobj.write(data)


def callback(result):
    print(" === 执行结束  %s" % result)


class NormalCrowdLoad:

    def __init__(self, file_path, hdfs_file_path, page_size, exe_type, config_file):
        self.localFilePath = file_path
        self.hdfsFilePath = hdfs_file_path
        self.page_size = page_size
        self.exe_type = exe_type
        self.config_file = config_file

    def run(self):
        if self.exe_type.startswith('label'):
            self.down_load_doris_to_pika()
        if self.exe_type == 'defined':
            self.down_load_s3_to_pika()

    def get_business_type_list(self, crowd_list):
        bag_class_list = [str(crowd.bagClass) for crowd in crowd_list]
        return set(bag_class_list)

    def down_load_s3_to_pika(self):
        """
                从s3下载文件
                :return:
                """
        crowd_list = self.getDefinedCrowd()
        if len(crowd_list) == 0:
            return
        pool = multiprocessing.Pool(processes=3)
        for crowd in crowd_list:
            pool.apply_async(self.download_s3, args=[crowd], callback=callback)
        pool.close()
        pool.join()

    def download_s3(self, crowd):
        """
        从s3下载文件
        :return:
        """
        file_id = crowd.file_id
        crowd_no = crowd.number
        task_no = crowd.taskNo
        bag_time = crowd.bag_time
        download_file_path = self.localFilePath + "/" + "s3DowloadData" + "/" + crowd_no + "/"
        merge_download_file_path = self.localFilePath + "/" + "mergeCrowdData" + "/" + crowd_no + "/"
        print("downFilePath= " + download_file_path)
        FileUtil.rmr_file(download_file_path, file_id)
        FileUtil.rmr_file(merge_download_file_path, file_id)
        FileUtil.mkdir(download_file_path)
        FileUtil.mkdir(merge_download_file_path)
        print(download_file_path, file_id)
        # 第1步.保存开始记录(生成文件的时候已经保存了）
        # old_version, crowd_version = DbOperator.insert_record(bag_time, crowd_no)
        crowd_version = crowd.resource_version.strip()
        DbOperator.update_record_start_time(crowd_no, crowd_version)
        try:
            S3Util(file_id).downloadFilePath(download_file_path)
            if len(open(download_file_path + "/" + file_id, "r").readline()) == 0:
                # s3文件为空，该人群包不存在用户，需要删除
                DbOperator.update_record(crowd_no, crowd_version)
                message = "资源位-从s3下载文件为空，人群包:%s 文件:%s" % (crowd_no, file_id)
                SendMailSms(message).notice_teams()
                DbOperator.update_crowd_version(crowd_no, crowd_version)
                return

            with open(download_file_path + "/" + file_id, "r") as file:
                # 读取第一行
                first_line = file.readline()

            fileCommand = '''cd %s;sed -e 's/^M/\n/g' %s;cat %s|awk '{sub("^ *","");sub(" *$","");print}'|awk '$0=$0"\t%s"'>%s''' % (
                download_file_path, download_file_path + file_id, download_file_path + file_id, crowd_no,
                merge_download_file_path + file_id)

            if "user_no" == first_line:
                fileCommand = '''cd %s;sed -i '1d' %s;sed -e 's/^M/\n/g' %s;cat %s|awk '{sub("^ *","");sub(" *$","");print}'|awk '$0=$0"\t%s"'>%s''' % (
                    download_file_path, file_id, download_file_path + file_id, download_file_path + file_id, crowd_no,
                    merge_download_file_path + file_id)

            status, output = subprocess.getstatusoutput(fileCommand)
            print("status=%s,output=%s", (status, output))
            # 存放s3到hdfs
            file = FileUtil.puts3_to_hdfs(self.hdfsFilePath, merge_download_file_path, file_id, crowd_no)
            # 存放到pika

            put_data_to_redis(file, crowd_version, "R_", crowd_no)
            if task_no and crowd_version:
                DbOperator.update_record(crowd_no, crowd_version)
            DbOperator.update_crowd_version(crowd_no, crowd_version)
            return crowd_no
        except Exception as e:
            traceback.print_exc()
            DbOperator.update_record(crowd_no, crowd_version, 2)

            message = "资源位-从s3下载文件异常，请检查 人群包:%s 文件:%s" % (crowd_no, file_id)
            SendMailSms(message).notice_teams()
            FileUtil.rmr_file(download_file_path, file_id)
            FileUtil.rmr_file(merge_download_file_path, file_id)
            return "%s,执行失败" % crowd_no

    def getDefinedCrowd(self):
        list_data = []
        with open(self.config_file, "r") as file:
            for line in file:
                (number, upload_file_name, update_time_str, resource_version) = line.split("@@@")
                list_data.append(CrowdInfoDomain(number, upload_file_name, number + update_time_str,
                                                 update_time_str=update_time_str,
                                                 resource_version=resource_version.strip()))

        return list_data

    def getLabelCrowd(self):
        list_data = []
        with open(self.config_file, "r") as file:
            for line in file:
                (number, bag_json, version, bag_type, data_mechanism, bag_class, update_time_str,
                 resource_version) = line.split("@@@")
                list_data.append(
                    CrowdInfoDomain(number, "", number + "_" + update_time_str, bag_json, version, bag_type,
                                    data_mechanism,
                                    bag_class, update_time_str, resource_version=resource_version.strip()))

        return list_data

    def down_load_doris_to_pika(self):
        """从doris下载文件
                 :return:
                 """
        crowd_list = self.getLabelCrowd()
        if len(crowd_list) == 0:
            return
        # 多进程执行下载，先判断是否有hdfs路径，不存在则新建一个新的
        bag_class_list = self.get_business_type_list(crowd_list)
        for bag_class in bag_class_list:
            CheckDoris().checkDorisSuccess(bag_class)
            # 检查标签是否准备完成
            FileUtil.check_hdfs_path(self.hdfsFilePath + "/" + BusinessMapping.convertBagClassToBusinessType(bag_class))

        pool = multiprocessing.Pool(processes=3)

        for crowd in crowd_list:
            pool.apply_async(self.download_doris, args=[crowd], callback=callback)
        pool.close()
        pool.join()

    def download_doris(self, crowd):
        """
        download_doris
        :param crowd:
        :return:
        """
        crowd_no = None
        file_path = None
        crowd_version = crowd.resource_version.strip()
        try:
            crowd_no = crowd.number
            crowd_json = crowd.bagJson
            task_no = crowd.taskNo
            # 第1步.保存开始记录
            # old_version, crowd_version = DbOperator.insert_record(bag_time, crowd_no)
            DbOperator.update_record_start_time(crowd_no, crowd_version)
            if '' == crowd_json:
                # 圈选json为空时，该人群包不存在用户，需要删除
                DbOperator.update_record(crowd_no, crowd_version)
                content = "资源位-普通人群包规则为空%s:!!!" % crowd_no
                SendMailSms(content).notice_teams()
                return

            file_path = FileUtil.mkdir(self.localFilePath + "/resource_crowd_data/normalCrowd/" + crowd_no)
            file = DorisDataService(crowd, file_path,
                                    self.hdfsFilePath,
                                    self.page_size).put_doris_to_hdfs()

            # 写入pika
            put_data_to_redis(file, crowd_version, "R_", crowd_no)

            if task_no and crowd_version:
                DbOperator.update_record(crowd_no, crowd_version)
            DbOperator.update_crowd_version(crowd_no, crowd_version)
            return crowd_no
        except Exception as e:
            # 版本号回滚
            # DbOperator.update_crowd_version(crowd_no, old_version)
            DbOperator.update_record(crowd_no, crowd_version, 2)
            print(e)
            traceback.print_exc()
            if file_path:
                FileUtil.command_rmr(file_path + "/*")
            content = "资源位-普通人群包处理异常%s:!!!" % crowd_no
            SendMailSms(content).notice_teams()
            return "%s,执行失败" % crowd_no


def put_data_to_redis(file_path, crowd_version, prefix, crowdno=None):
    try:
        print(" start load to pika crowdno=%s,filepath=%s" % (crowdno, file_path))
        SinkLocalData2Redis.put_local_data2redis_2(file_path, crowd_version, batch_size=batch_size, prefix=prefix,
                                                   crowd_no=crowdno)
        SinkLocalData2Redis.rmr_data_command(file_path)
        print(" end load to pika crowdno=%s,filepath=%s" % (crowdno, file_path))
    except Exception as e:
        raise e


if __name__ == '__main__':
    filepath = sys.argv[1]
    hdfspath = sys.argv[2]
    pagesize = sys.argv[3]
    exetype = sys.argv[4]
    config_file = sys.argv[5]
    batch_size = int(sys.argv[6])
    NormalCrowdLoad(filepath, hdfspath, pagesize, exetype, config_file).run()
