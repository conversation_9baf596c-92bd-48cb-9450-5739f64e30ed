#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
-- file name     : normalDefinedCrowdInit.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-07-28
-- copyright     : @qctx
-- function list : 自定义人群初始化
-- history       : 第一版脚本，无历史
"""
import datetime
import json
import os
import subprocess
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from dbOperator import DbOperator

from domain.crowdInfoDomain import CrowdVersionInfo, CrowdEncoder
from utils import FileUtil
from utils.SinkLocalData2redis import SinkLocalData2Redis


class DefinedInit(object):

    def __init__(self, local_file_path, defined_hdfs_path):
        self.localPath = local_file_path
        self.defined_hdfs_path = defined_hdfs_path

    def get_hdfs_file(self):
        rm_file = """rm -f %s""" % self.localPath
        status, output = subprocess.getstatusoutput(rm_file)
        FileUtil.mkdir(self.localPath)
        get_command = """hadoop fs -get -f %s/* %s""" % (self.defined_hdfs_path, self.localPath)
        print("get_command=", get_command)
        status, output = subprocess.getstatusoutput(get_command)
        print("status=%s ,output=%s", (status, output))

    def load2pika(self, filepath):
        dir_list = []
        if os.path.isfile(filepath):
            dir_list.append(filepath)
        else:
            dirlist = [filepath + "/" + path for path in os.listdir(filepath)]
            dir_list.extend(dirlist)
        while len(dir_list) > 0:
            temp_path = dir_list.pop()
            crowd_version = "1"
            if os.path.isdir(temp_path):
                dir_list.extend(temp_path + "/" + p for p in os.listdir(temp_path))
            elif os.path.isfile(temp_path):
                print("start load to pika file_path =", temp_path)
                with open(temp_path) as fp:
                    (user_no, cur_crowd_no) = fp.readline().split("\t")
                    print(user_no, cur_crowd_no)
                bag_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                DbOperator.insert_record(bag_time, cur_crowd_no.strip(), crowd_version)
                SinkLocalData2Redis.put_local_data2redis_2(temp_path, crowd_version,
                                                           50000,
                                                           "R_")
                filename = temp_path.split("/")[-1]
                FileUtil.rmr_file(os.path.dirname(temp_path), filename)
                value = json.dumps(CrowdVersionInfo(crowd_version, -1), cls=CrowdEncoder)
                # 插入redis
                SinkLocalData2Redis.set(cur_crowd_no.strip(), value)
                DbOperator.update_record(cur_crowd_no.strip(), bag_time)


if __name__ == '__main__':
    definedhdfspath = sys.argv[1]
    localfilepath = sys.argv[2]
    # definedhdfspath = ""
    # localfilepath = "D:\\data\\log\\N35870_20230509180015964414.tsv"

    defInit = DefinedInit(localfilepath, definedhdfspath)
    defInit.get_hdfs_file()
    defInit.load2pika(localfilepath)
