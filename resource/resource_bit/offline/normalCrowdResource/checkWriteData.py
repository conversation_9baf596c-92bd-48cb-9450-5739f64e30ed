#!/usr/bin/python
# -*- coding: utf-8 -*-

import datetime
# 文件路径，人群包版本，
import sys

from pyspark import Row
from pyspark.sql import SparkSession
from redis import Redis


class CheckBagVersion:
    writeCountAccum = None

    @classmethod
    def create_redis(cls):
        return Redis(host=redisHost, port=int(redisPort), password=redisPsw, db=int(redisDB), decode_responses=True,
                     retry_on_timeout=True)

    @classmethod
    def checkBagVersion(cls):
        spark = SparkSession.builder.enableHiveSupport().getOrCreate()
        sc = spark.sparkContext
        cls.writeCountAccum = sc.accumulator(0)
        # config_file_path = "/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data_test/TEST2.txt"
        configData = sc.textFile(config_file_path).map(lambda line: line.split("@@@"))
        finalHdfsPath = ""

        if exec_type.startswith('defined'):
            config_data = spark.createDataFrame(
                configData.map(lambda p: Row(number=p[0], filename=p[1], update_time_str=p[2], crowd_version=p[3])))
            config_data.registerTempTable("baseData")
            sql0 = "select number,filename,crowd_version from baseData"
            df0 = spark.sql(sql0)
            rows = df0.collect()
            finalHdfsPath = ",".join(hdfs_file_path + "/" + str(p["number"]) + "_" + str(p["filename"]) for p in rows)
        else:
            config_data = spark.createDataFrame(
                configData.map(lambda p: Row(number=p[0], crowd_version=p[7])))
            config_data.registerTempTable("baseData")
            sql0 = "select number,crowd_version from baseData"
            df0 = spark.sql(sql0)
            rows = df0.collect()
            finalHdfsPath = ",".join(hdfs_file_path + "/" + str(p["number"]) + "_*" for p in rows)
        # hdfs_file_path = "/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data/defined_v2/20241216"
        if finalHdfsPath == '':
            raise Exception("资源位-资源位人群包文件路径不存在")

        crowdNoList = ",".join(("'" + str(p["number"] + "'") for p in rows))
        crowdVersion = (lambda x: [p['crowd_version'] for p in x][0] if x else None)(rows)

        if not crowdVersion:
            raise Exception("资源位-资源位人群包版本校验")

        # 将文件路径传入进来
        hdfsData = sc.textFile(finalHdfsPath).map(lambda line: line.split("\t"))
        # data = hdfsData.map(lambda p:Row(pika_key=p))
        data = spark.createDataFrame(hdfsData.map(lambda p: Row(pika_key=p[0], pika_value=p[1])))
        # data.select("pika_value=").filter()
        data.registerTempTable("tableA")

        sql = "select  pika_key , pika_value from (select b.pika_key,b.pika_value,RANK() over (partition by pika_value order by pika_key desc) as topRank  from tableA b where b.pika_value in (" + crowdNoList + ")) t  where t.topRank <1000"
        # sql = "select  b.pika_key,b.pika_value from tableA b where b.pika_value in (" + crowdNoList + ")"
        # sql = "select count(1) from tableA where pika_value='%s'" % crowdNo
        df = spark.sql(sql)
        # df.show(100)
        df.repartition(5).foreachPartition(lambda lines: cls.checkRedisVersion(crowdVersion, lines))
        # 更新版本信息
        datestr = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print("==========%s 批量检查条数:%s =============" % (datestr, cls.writeCountAccum.value))

    @classmethod
    def checkRedisVersion(cls, crowd_version, lines):
        if not lines:
            print("当前数据为空")
            raise Exception("资源位-资源位人群包版本校验,数据为空")

        client = cls.create_redis()
        try:
            for line in lines:
                key = line["pika_key"]
                value = line["pika_value"]
                if len(key) > 0:
                    cls.writeCountAccum.add(1)  # Increment accumulator
                    final_user_no = key.strip()
                    if prefix:
                        final_user_no = prefix + final_user_no
                    redis_crowd_version = client.hget(final_user_no, value)
                    if not redis_crowd_version or redis_crowd_version < crowd_version:
                        raise Exception("资源位-资源位人群包版本校验版本未更新,crowd_no" + str(value))
        finally:
            client.close()

    @classmethod
    def testExeception(cls, lines):
        count = 0
        for r in lines:
            print(r)
            count = count + 1
            if count >= 2:
                raise Exception("test exception=========")


if __name__ == '__main__':
    redisHost = sys.argv[1]
    redisPort = sys.argv[2]
    redisPsw = sys.argv[3]
    redisDB = sys.argv[4]
    config_file_path = sys.argv[5]
    hdfs_file_path = sys.argv[6]
    prefix = sys.argv[7]
    exec_type = sys.argv[8]
    CheckBagVersion.checkBagVersion()
