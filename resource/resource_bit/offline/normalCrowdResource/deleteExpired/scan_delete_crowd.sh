#!/bin/bash
##  file name     : scan_delete_crowd.sh
##  author        : z<PERSON>yong
##  version       : v1.0
##  date          : 2023-05-24
##  copyright     : @qctx
##  description   : 一体化人群入资源位数据过期数据删除
##  usage         : 对JSON解析输出到HDFS,形成redis需要得key，value
##  function list :
##  history       : 第一版脚本，无历史
# set -e
set -x
thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
  todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
fi
cwd=$(
  cd $(dirname "$0")
  pwd
)
source ./alarm.sh
pythonPath=python3
parentScriptDir=$cwd

$pythonPath $parentScriptDir/normalCrowdResource/deleteExpired/scan_delete_crowd.py

