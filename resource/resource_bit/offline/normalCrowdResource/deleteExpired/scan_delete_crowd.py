#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
-- file name     : scan_delete_crowd.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 20230627
-- copyright     : @qctx
-- function list : 删除过期的key
-- history       : 第一版脚本，无历史
-- desc          : scan命令在python2.7中不支持，需要python3，该脚本使用python3执行
'''
__author__ = 'zhangyong'

import json
import multiprocessing
import os
import sys
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

'''
-- file name     : deleteExpiredCrowd.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-06-12
-- description   : 删除redis已经过期的人群包信息
'''


class DeleteExpired(object):

    @staticmethod
    def get_redis_client_proto():
        from common.redisPoolManager import get_client

        return get_client()


def split_list(keys, n):
    if not len(keys):
        return []
    if len(keys) <= n:
        return keys
    return [keys[i: i + n] for i in range(0, len(keys), n)]


def calbb(obj):
    print(obj)


def scan_redis_key(match_str):
    batch_size = 5000
    start = datetime.now()
    cursor = 0
    # sum = 500000
    split_num = 100
    redis_client = DeleteExpired.get_redis_client_proto().connect()
    try:
        count = 0
        while True:
            pool = multiprocessing.Pool(processes=1)
            cursor, keys = redis_client.scan(cursor, count=batch_size, match=str(match_str))
            count = count + len(keys)

            print("key length=%s" % len(keys))
            for key_list in split_list(keys, split_num):
                pool.apply_async(delete_expired_value1, args=[key_list])
            pool.close()
            pool.join()
            if cursor == 0:
                print("删除过期的人群包处理结束")
                break
            # if count >= sum:
            #     print("处理100w的人群包处理结束")
            #     break
        print("处理%s开头的人群包处理结束，数量%s" % (match_str, count))
        end = datetime.now()

        print("删除过期的人群包处理耗时：" + str(end - start))
    finally:
        redis_client.close()


def delete_expired_value1(key_list):
    """
    :param key:
    :param crowd_map:
    :return:
    """
    print("初始化 redis")
    client = DeleteExpired.get_redis_client_proto().connect()
    for key in key_list:
        crowd_map = client.hgetall(key)
        delete_count = delete_expired_value(key, crowd_map, client)
        print("userno=%s,过期人群包数量%s" % (key, delete_count))
    # for c, v in crowd_map.items():
    #     delete_expired_value(key, c, v)


# def delete_expired_value_all(key, crowd_map: dict):
#     redis_client = DeleteExpired.get_redis_client()
#     redis_client.delete(key)


def delete_expired_value(key, crowd_map: dict, client):
    """
    批量查询人群编号
    :param key:
    :param crowd_map:
    :return:
    """
    delete_count = 0
    redis_client = client
    print(crowd_map)
    with redis_client.pipeline(transaction=False) as p:
        keylist = list(crowd_map.keys())
        crowd_version_list = redis_client.mget(keylist)
        for index, c in enumerate(keylist):
            v = crowd_map[c]
            crowd_version_info = crowd_version_list[index]
            # 如果不存在，表示人群包过期了，需要删除人群包
            if crowd_version_info is None:
                print("key=", key, c, "不存在，已过期删除", )
                redis_client.hdel(key, c)
                p.hdel(key, c)
                delete_count = delete_count + 1
            else:
                c_j = json.loads(crowd_version_info)
                if v.decode("utf-8") < c_j.get("v"):
                    print(key, "版本过期，删除人群包", c, v)
                    # redis_client.hdel(key, c)
                    p.hdel(key, c)
                    delete_count = delete_count + 1
        p.execute()

    return delete_count


# def delete_expired_value2(key, c, v):
#     print("start %s" % key)
#     # start = datetime.now()
#     redis_client = DeleteExpired.get_redis_client()
#     crowd_version_info = redis_client.get(c)
#     # 如果不存在，表示人群包过期了，需要删除人群包
#     if crowd_version_info is None:
#         print(c, "不存在，已过期删除", v)
#         redis_client.hdel(key, c)
#     else:
#         c_j = json.loads(crowd_version_info)
#         if v.decode("utf-8") < c_j.get("v"):
#             print(key, "版本过期，删除人群包", c, v)
#             redis_client.hdel(key, c)
#     # end = datetime.now()
#     # print("单个key=%s,处理耗时=%s" % (key, end - start))


if __name__ == '__main__':
    matchstr = sys.argv[1]
    scan_redis_key(matchstr)
