#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
-- file name     : RedisPoolManager.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-06-06
-- copyright     : @qctx
-- function list : 数据服务新环境版本
-- history       : 第一版脚本，无历史
"""
import os
import sys
from configparser import ConfigParser

import redis

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)


def getConfig():
    cf = ConfigParser()
    confPath = os.path.realpath(__file__)
    confDir = os.path.dirname(confPath)
    cf.read(confDir + "/redis.conf", encoding="utf-8")
    env = cf.get("conf_env", "env")
    config_name = "resource_redis_" + env
    config_name2 = "resource_redis_" + env
    if "prd" == env:
        config_name = "resource_redis_" + "isc"
        config_name2 = "resource_redis_" + "normal"

    host_name = cf.get(config_name, "hostname")
    port = cf.get(config_name, "port")
    db = cf.get(config_name, "db")
    passwd = cf.get(config_name, "passwd")
    # redis
    redis_conf = {
        'redisHost': host_name,
        'redisPort': port,
        'redisPsw': passwd,
        'redisDB': db,
    }
    host_name = cf.get(config_name2, "hostname")
    port = cf.get(config_name2, "port")
    db = cf.get(config_name2, "db")
    passwd = cf.get(config_name2, "passwd")
    # redis2
    redis_conf2 = {
        'redisHost': host_name,
        'redisPort': port,
        'redisPsw': passwd,
        'redisDB': db,
    }
    return redis_conf, redis_conf2


class RedisClient(object):
    def __init__(self, data):
        """
        :param redisHost:
        :param redisPort:
        :param redisPsw:
        :param redisDB:
        """
        print(data)
        self.redisHost = data['redisHost']
        self.redisPort = data['redisPort']
        self.redisPsw = data['redisPsw']
        self.redisDB = data['redisDB']
        self.pool = redis.ConnectionPool(host=self.redisHost, port=int(self.redisPort), db=self.redisDB,
                                         password=self.redisPsw)

    def connect(self, ):
        connect = redis.StrictRedis(connection_pool=self.pool)
        return connect


def get_client(type):
    """

    :param type: 1:普通策略 2:一体化策略
    :return:
    """
    if "1" == type:
        return RedisClient(getConfig()[1])
    if "2" == type:
        return RedisClient(getConfig()[0])


class DeleteRedisPoolManager:
    redis_pool_instance = RedisClient(getConfig()[0])
    redis_pool_instance2 = RedisClient(getConfig()[1])


if __name__ == '__main__':
    # print(1)
    r1 = DeleteRedisPoolManager().redis_pool_instance.connect()
    # r2 = RedisPoolManager().redis_pool_instance.connect()
    r1.get("11")
    r1.get("11")
    r1.get("11")
    # print(r1.client_list())
    # print(r2.client_list())
    # print(r2.get("k1"))
    # print(r1, r2)
