#!/bin/bash
##  file name     : normalDefinedCrowdInit.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2023-07-31
##  copyright     : @qctx
##  description   : 借条业务线自定义人群初始化
##  usage         : 借条业务线自定义人群初始化
##  function list :
##  history       : 第一版脚本
# set -e

set -x
yesterday=${system.biz.time}
thedate=`date -d ${yesterday} +%Y%m%d%H%M`
todaymin=`date -d ${yesterday} +%Y%m%d%H%M`
if ! test thedate; then
   thedate=`date -d-0day +%Y%m%d`
   todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
fi
cwd=$(
  cd $(dirname $0)
  pwd
)

source  ./alarm.sh

parentScriptDir=$cwd
localFileParentDir=$cwd/resource_crowd_data_v2
### 跑数据临时存储目录
parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data/

definedHdfsDir=$parentHdfsDir/defined_v2/
#需要动态变更
year_str=2000*
pythonPath=/home/<USER>/anaconda3.5.1/bin/python

$pythonPath $parentScriptDir/normalCrowdResource/normalDefinedCrowdInit.py $definedHdfsDir/$year_str  $localFileParentDir



