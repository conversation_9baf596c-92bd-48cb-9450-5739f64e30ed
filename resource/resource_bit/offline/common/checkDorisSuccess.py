#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'zhangyong'

from utils.MysqlConfig import MysqlConfig

'''
-- file name     : checkDorisSuccess.py
-- author        : zhangyong
-- version       : v1.0
-- date          : 2023-05-11
-- copyright     : @qctx
-- function list : 检查doris标签是否准备好
-- history       : 第一版脚本，无历史
'''
import os
import sys
import time

base_dir = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
sys.path.append(base_dir)


class CheckDoris(object):

    def __init__(self):
        super(CheckDoris, self).__init__()

    def returnYesterday(self):
        """
        获取昨天日期
        :return:
        """
        v_today = time.strftime('%Y%m%d', time.localtime())
        # print v_today
        date_ts = time.mktime(time.strptime(v_today, '%Y%m%d'))
        yesterday = time.strftime('%Y%m%d', time.localtime(date_ts - 1 * 60 * 60 * 24))

        return yesterday

    def selectDorisMeta(self, business_type):
        print("checkEsSuccess business_type=%s" % (business_type))
        sql = '''
                       select biz_type,max(biz_date) as latest_time
                          from cdp_label_offline_label_produce_flow 
                          where biz_type='%s' and status = 2 limit 1
                       ''' % business_type
        print(sql)
        mysql = MysqlConfig().labelMysql()
        select = mysql.selectValues(sql)
        mysql.closeDb()

        return select

    def checkDorisSuccess(self, businessType):

        if businessType in ("1", "3", "5"):
            businessType = "1"
        elif businessType in "999":
            businessType = "1"
        yesterday = self.returnYesterday()
        result = False
        for count in range(30):
            metaResult = self.selectDorisMeta(businessType)
            for bizType, latestTime in metaResult:
                if latestTime == yesterday:
                    print("依赖业务线【" + businessType + "】检索索引【" + latestTime + "】检查OK!!!")
                    result = True
                    break
                else:
                    print("依赖业务线【" + businessType + "】标签【" + latestTime + "】未就绪!!! times= " + str(count))
                    time.sleep(180)
                    count += 1
                    result = True
            if result:
                break
        return result


if __name__ == '__main__':
    bagClass = sys.argv[1]
    CheckDoris().checkDorisSuccess(bagClass)
