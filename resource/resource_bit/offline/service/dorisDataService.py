#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
-- file name     : dorisDataService.py
-- author        : <PERSON><PERSON><PERSON><PERSON><PERSON>
-- version       : v1.0
-- date          : 2023-03-25
-- copyright     : @qctx
-- function list : 数据服务新环境版本
-- history       : 第一版脚本，无历史
"""
import datetime
import json
import os
import sys
import traceback

import pymysql

from utils import FileUtil
from utils.MysqlConfig import MysqlConfig

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)

from utils.dorisPoolManager import DorisPoolManager
from utils.httpRequestUtil import requestCrowdRuleParse
from utils.dorisUtil import mainDorisUtil


class DorisDataService(object):

    def __init__(self, bagInfo, filePath, inputHdfs, dorisPageSize):
        self.bagInfo = bagInfo
        self.filePath = filePath
        self.inputHdfs = inputHdfs
        self.pageSize = int(dorisPageSize)

    def handDorisToHdfs(self):
        businessType = self.convertBagClassToBusinessType(self.bagInfo.bagClass)
        localFilePath = self.generateLocalFilePath(businessType)
        localFileName = self.generateLocalFileName()
        fileName = localFilePath + localFileName
        filterSql = buildCrowSql(self.bagInfo)
        self.handDorisToLocal(fileName, filterSql)
        mainDorisUtil(localFilePath, localFileName, self.inputHdfs, businessType, self.bagInfo.number)

    def put_doris_to_hdfs(self):
        """
        将doris下载数据put到hdfs
        :return:
        """
        business_type = self.convertBagClassToBusinessType(self.bagInfo.bagClass)
        local_file_path = self.generateLocalFilePath(business_type)
        local_file_name = self.generateLocalFileName()
        file_name = local_file_path + local_file_name
        filter_sql = buildCrowSql(self.bagInfo)
        self.handDorisToLocal(file_name, filter_sql)
        FileUtil.put_to_hdfs(self.inputHdfs + "/" + business_type + "/", local_file_path, local_file_name,
                             self.bagInfo.number)
        return file_name

    def handDorisToLocal(self, fileName, filterSql):
        conn = DorisPoolManager.offline_doris_pool.get_connection()
        cursor = conn.cursor(pymysql.cursors.SSCursor)
        f = open(fileName, 'w')
        try:
            cursor.execute("set query_timeout=7200")
            cursor.execute(filterSql)
            while True:
                result = cursor.fetchmany(self.pageSize)
                if not result:
                    break
                for row in result:
                    for user_no in row:
                        f.write(user_no + "\t" + self.bagInfo.number + "\n")
        except Exception as e:
            traceback.print_exc()
            print(e)
            raise e
        finally:
            f.close()
            cursor.close()
            conn.close()

    def generateLocalFileName(self):
        return self.bagInfo.number + "_" + datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".tsv"

    def generateLocalFilePath(self, business_type):
        localFilePath = self.filePath + "/" + business_type + "/"
        print(localFilePath + " number=" + self.bagInfo.number)
        if not os.path.exists(localFilePath):
            os.makedirs(localFilePath)
        return localFilePath

    def convertBagClassToBusinessType(self, bag_class):
        bagClass = str(bag_class)
        business_type = ''
        if bagClass in ("1", "3", "5"):
            business_type = "CREDITLOAN"
        elif bagClass in ("4"):
            business_type = "NICAIFU"
        elif bagClass in ("999"):
            business_type = "JIETIAONINEC"
        return business_type

    def selectDorisMeta(self, businessType):
        sql = '''
        select biz_type,max(biz_date) as latest_time
           from cdp_label_offline_label_produce_flow 
           where biz_type='%s' and status = 2 limit 1
        ''' % businessType
        print(sql)
        mysql = MysqlConfig().labelMysql()
        select = mysql.selectValues(sql)
        mysql.closeDb()
        return select


def buildCrowSql(bagInfo):
    dataJson = {"bagType": bagInfo.bagType,
                "ruleJson": bagInfo.bagJson,
                "version": bagInfo.version,
                "dataMechanism": bagInfo.dataMechanism,
                "businessType": bagInfo.bagClass,
                "number": bagInfo.number}
    print(json.dumps(dataJson))
    filterSql = requestCrowdRuleParse(dataJson)
    sqlReplace = filterSql.split(";")[1]
    print(sqlReplace)
    return sqlReplace


# todo 重试逻辑
def writeDorisToLocal(bagInfo, fileName, isTab, pageSize):
    pageSize = int(pageSize)
    crowSql = buildCrowSql(bagInfo)
    conn = DorisPoolManager.offline_doris_pool.get_connection()
    cursor = conn.cursor(pymysql.cursors.SSCursor)
    f = open(fileName, 'w')
    try:
        cursor.execute("set query_timeout=7200")
        cursor.execute(crowSql)
        while True:
            result = cursor.fetchmany(pageSize)
            if not result:
                break
            for row in result:
                for user_no in row:
                    if isTab:
                        f.write(user_no + "\t" + bagInfo.number + "\n")
                    else:
                        f.write(user_no + "\n")
        return 0
    except Exception as e:
        print(e)
        traceback.print_exc()
        return 1
    finally:
        f.close()
        cursor.close()
        conn.close()


if __name__ == '__main__':
    print(11)
