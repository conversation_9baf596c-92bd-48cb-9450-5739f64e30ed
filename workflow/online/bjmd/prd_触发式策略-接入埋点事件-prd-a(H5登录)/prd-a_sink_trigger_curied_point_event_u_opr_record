任务名称：prd_a_sink_trigger_curied_point_event_u_opr_record
服务器地址：kafka-pro-a.daikuan.qihoo.net:9092
Topic规则：固定topic
Topic：ipss-trigger-app-event-filter-topic
分区规则：哈希分区
输出格式：JSON
数据获取SQL:
    select
     msgId,
     requestNo,
     toJsonStr(MAP[
       'triggerEventSource', 'buried_point_event',
       'pageName', bodyMap['pageName'],
       'originalSourceType', bodyMap['sourceType'],
       'sourceType', 'H5',
    'sourceTypeMapping', case bodyMap['sourceType'] when 'H5QB' then 'apkqb' when 'H5ZZL' then 'zzl' else '360jietiao' end,
       'channelSource', bodyMap['channelSource'],
       'channelId', concat(bodyMap['sourceType'], '_' ,bodyMap['channelSource']),
       'eventCode', bodyMap['eventCode'],
       'productCode', bodyMap['productCode'],
       'osType', lower(bodyMap['osType']),
       'userNo', bodyMap['userNo'],
       'bizData', bodyMap['bizData'],
       'mobileNoEncryptx', bodyMap['mobileNoEncryptx'],
       'triggerEventTime', bodyMap['userOprTime']
     ]
    ) as body
    from
    (
     select
      msgId,
      requestNo,
      parseJson(body) as bodyMap
     from prd_a_source_trigger_curied_point_event_u_opr_record
     union all
     select
      msgId,
      requestNo,
      parseJson(body) as bodyMap
     from prd_a_source_trigger_curied_point_event_u_opr_record_nu
    )
    where bodyMap['eventCode'] in ('LOGIN_SP','LOGIN_NPS_SP')
    and bodyMap['sourceType'] like '%H5%'
    and bodyMap['pageName'] not like '%newsuccess.html'
    and bodyMap['pageName'] not like '%oldsuccess.html'
    and bodyMap['pageName'] not like '%nocredit.html'