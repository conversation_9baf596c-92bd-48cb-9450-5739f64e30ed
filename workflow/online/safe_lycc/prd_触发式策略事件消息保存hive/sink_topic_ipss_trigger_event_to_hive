任务名称：sink_topic_ipss_trigger_event_to_hive
库名：credit_data
表名：isc_trigger_event_info_detail
分区字段：pday
hive配置路径：/etc/hive/conf
版本：3.1.2
数据获取SQL:
    select
           from_unixtime(cast(body_json['receiveTime'] as bigint)/1000,'yyyy-MM-dd HH:mm:ss') as receive_time,
           body_json['eventCode']      as event_code,
           from_unixtime(cast(body_json['triggerEventTime'] as bigint)/1000,'yyyy-MM-dd HH:mm:ss') as trigger_event_time,
           body_json['msgId']               as msg_id,
           body_json['requestNo']               as request_no,
           body_json['body']               as body,
           from_unixtime(cast(body_json['receiveTime'] as bigint)/1000 ,'yyyyMMdd') as pday
    from (select parseJson(body) body_json from source_topic_ipss_trigger_event_to_hive_a
          union all
          select parseJson(body) body_json from source_topic_ipss_trigger_event_to_hive_b
    ) sc
