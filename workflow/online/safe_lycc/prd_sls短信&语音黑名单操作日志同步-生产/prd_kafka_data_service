任务名：prd_kafka_data_service
源头名称：data_service
注册类型：json
schema定义：{"database":{"type":"STRING"},"table":{"type":"STRING"},"data":{"type":"ARRAY","valueType":{"type":"STRING"}},"ts":{"type":"STRING"},"type":{"type":"STRING"},"receive_ts":{"type":"LONG"}}
时间属性：接入时间
Topic：data_service
Group：prd_data_service_yushu_black_record
首次消费：最新数据
服务器地址：dtKafka.daikuan.qihoo.net:9092
其他配置：{"max.partition.fetch.bytes":"10485760","max.poll.records":"5000"}