任务名称：prd_to_sms_kafka
服务器地址：dtKafka.daikuan.qihoo.net:9092
Topic规则：固定topic
Topic：sls_sms_black_operate_record_topic
分区规则：哈希分区
输出格式：JSON
数据获取SQL:
    select
    source_id,
    business_type,
    operate_business,
    mobile_no_md5x,
    operate_user,
    operate_time,
    status,
    sub_list[1] as source_describe,
    black_type_list[1] as black_type,
    black_level
    from(
    select
    source_id,
    business_type,
    operate_business,
    mobile_no_md5x,
    operate_user,
    operate_time,
    status,
    parseJsonArray(black_value['sub']) as sub_list,
    parseJsonArray(black_value['blacktypeList']) as black_type_list,
    black_level
    from (
    select
    data['id'] as source_id,
    data['business_type'] as business_type,
    data['operate_business'] as operate_business,
    data['mobile_md5x'] as mobile_no_md5x,
    data['operate_user'] as operate_user,
    substring(data['operate_time'],1,19) as operate_time,
    data['operate_status'] as status,
    parseJson(data['black_value']) as black_value,
    'BLACK_M' as black_level
    from parse_data_service
    where `table` = 'srds_sms_black_record' and data['black_type'] in ('sms','app_sms')
    )
    )