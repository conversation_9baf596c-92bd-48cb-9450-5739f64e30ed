任务名称：【生产】短信流控日志KAFKA写到hive
库名：credit_data
表名：sls_sms_real_request_log
分区字段：pday
hive配置目录：/etc/hive/conf
版本：3.1.2
数据获取SQL:
    select
        messageNo as message_no,
        requestNo as request_no,
        touchMode as touch_mode,
        businessType as business_type,
        systemId as system_id,
        templateCode as template_code,
        mobile as mobile_no_md5x,
        blackLevel as black_level,
        pushType as push_type,
        configLimitSize as config_limit_size,
        configLimitDays as config_limit_days,
        isLimit as is_limit,
        subStrategyId as sub_strategy_id,
        taskId as task_id,
        messageType as message_type,
        sendTime as send_time,
        planId as plan_id,
        operateCrowd as operate_crowd,
        sendScene as send_scene,
        blackType as black_type,
        userNo as user_no,
        eventId as event_id,
        ruleType as rule_type,
        blackTypeList as black_type_list,
        ruleNo as rule_no,
        salvageSendScene as salvage_send_scene,
        salvageRuleNo as salvage_rule_no,
        salvageEventId as salvage_event_id,
        extPara as ext_para,
        date_format(sendTime,'yyyyMMdd') as pday
    from prd_sls_sms_real_request_log