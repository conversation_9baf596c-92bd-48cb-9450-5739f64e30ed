任务名称：【生产】微信流控日志MQ写到hive
库名：credit_data
表名：sls_weixin_real_request_log
分区字段：pday
hive配置目录：/etc/hive/conf
版本：3.1.2
数据获取SQL:
    select
        data['messageNo'] as message_no,
        data['requestNo']  as request_no,
        data['batchNo'] as batch_no,
        data['sendType'] as send_type,
        data['businessType'] as business_type,
        data['publicAccount'] as public_account,
        data['messageType'] as message_type,
        data['ruleType'] as rule_type,
        data['ruleNo'] as rule_no,
        data['ruleName'] as rule_name,
        data['userNo'] as user_no,
        data['openId'] as open_id,
        data['pushType'] as push_type,
        CAST(data['configLimitSize'] AS INT) as config_limit_size,
        CAST(data['configLimitDays'] AS INT) as config_limit_days,
        data['isLimit'] as is_limit,
        data['systemId'] as system_id,
        data['sendTime'] as send_time,
        data['extParama'] as ext_param,
        date_format(data['sendTime'],'yyyyMMdd') as pday
    from (
        select
            parseJson(payload) data
        from prd_a_sls_weixin_real_request_log
        union all
        select
            parseJson(payload) data
        from prd_b_sls_weixin_real_request_log
        union all
        select
            parseJson(payload) data
        from prd_xd_sls_weixin_real_request_log
        union all
        select
            parseJson(payload) data
        from prd_rd_sls_weixin_real_request_log
    )