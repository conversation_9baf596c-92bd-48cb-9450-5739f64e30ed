-- yy_dept_online_data_app_task->sls_ivr_black_operate_record_sync
-- 数量级:47666854 
-- INSERT overwrite TABLE credit_data.test_sls_ivr_black_operate_record_sync_lx  PARTITION(pday = '${yesterday_p}')
INSERT overwrite TABLE fin_dw_yy.dwd_sls_ivr_black_operate_record_sync
SELECT
    source_id,
    business_type,
    operate_business,
    user_no,
    status,
    operate_user,
    operate_time,
    source_describe,
    black_type_report as black_type
FROM
 credit_data.sls_ivr_black_operate_record_pda
-- credit_data.test_sls_ivr_black_operate_record_pda_lx
where pday='${yesterday_p}'
    and (to_date(operate_time) = '${yesterday}' or date_format(now(), 'yyyy-MM-dd')='2024-10-17')
    and (user_no is not null AND user_no!='')
