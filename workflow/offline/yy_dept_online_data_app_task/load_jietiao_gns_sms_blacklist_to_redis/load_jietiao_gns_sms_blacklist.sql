#!/bin/bash
# file name     : load_jietiao_gns_sms_blacklist.sh
# author        : wangxianqiang
# version       : v1.0
# date          : 2022-11-03
# description   :
scriptDir=.
python=/home/<USER>/python3/bin/python3.6

thedate=$1

if ! test $thedate;then
     thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi

#kinit -kt ./wangxianqiang1-jk.keytab wangxianqiang1-jk

${python} ${scriptDir}/load_jietiao_gns_sms_blacklist.py ${thedate}

if [ $? -eq 0 ];then
    echo "load_jietiao_gns_sms_blacklist同步redis成功！"
else
    echo "load_jietiao_gns_sms_blacklist同步redis失败！"
    exit 1
fi

if [ -f "$scriptDir/$thedate.csv" ]; then
    echo "load_jietiao_gns_sms_blacklist文件存在！"
fi

#delthedate=$(/bin/date -d "-3day" +"%Y%m%d")
rm -f ${scriptDir}/logs/${thedate}_gns.log
rm -f ${scriptDir}/${thedate}_gns.csv
