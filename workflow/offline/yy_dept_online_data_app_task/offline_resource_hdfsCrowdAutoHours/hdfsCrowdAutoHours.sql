#!/bin/bash
##  file name     : hdfsCrowdAutoHours.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2022-11-12
##  copyright     : @qctx
##  description   : hdfs人群包数据点击任务后数据自动上传s3
##  usage         : hdfs人群包数据点击任务后数据自动上传s3
##  function list :
##  history       : 第一版脚本，无历史
# set -e
set -x
unzip offline_label_application
cwd=$(cd $(dirname $0); pwd)
source $cwd/utils/smsUtil.sh
thedate=$1
if ! test $thedate; then
    thedate=$(/bin/date -d-1day "+%Y%m%d")
    today=$(/bin/date -d-0day "+%Y-%m-%d")
    todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
    todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
fi
cwd=$(cd $(dirname $0); pwd)

parentScriptDir=$cwd
parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/temp_resource_crowd_data
localFileDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application
pythonPath=/home/<USER>/anaconda2/bin/python

jobname="yushu_crowd_auto_hourly_$todaymin"

elevenTagPath=hdfs://360jinronglycc//user/hive/warehouse/dp_data_db.db/tag_user_data_mr/v2/custElevenTagSteup_2
input=$elevenTagPath/$thedate/
#依赖11点标签，工作流依赖
inputtag=$elevenTagPath/mrTag/$thedate

output="${parentHdfsDir}/autoHourly/$todaymin/"
configPath="$localFileDir/hdfsCrowdAutoHours/ruleConfig/$todaymin/"
configFile="rules.conf"
exebatch="autoHours${todaymin}"

mysql_host=$(grep -r host ${parentScriptDir}/common/db.conf |awk -F = '{print $2}')
mysql_port=$(grep -r port ${parentScriptDir}/common/db.conf |awk -F = '{print $2}')
mysql_user=$(grep -r user ${parentScriptDir}/common/db.conf |awk -F = '{print $2}')
mysql_pass=$(grep -r pass ${parentScriptDir}/common/db.conf |awk -F = '{print $2}')
mysql_db=$(grep -r db= ${parentScriptDir}/common/db.conf |awk -F = '{print $2}')
dispose_date='ys_'${todaymin}
##记录流程阶段
mysql_excute="mysql -h${mysql_host} -u${mysql_user} -p${mysql_pass} -P${mysql_port}   ${mysql_db} --default-character-set=utf8"
echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_init_config','running')"`
##step1: 执行python脚本生成配置文件
rm -f $configPath$configFile
$pythonPath $parentScriptDir/common/produceConfig.py $configPath $configFile $exebatch
if [ $? -eq 0 ];then
  echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_init_config','success')"`
else
  echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_init_config','failed')"`
fi

##step2:判断文件有没有生成，生成完毕后put hdfs
resultConf=$(ls $configPath$configFile |wc -l)
if [ $resultConf -eq 0 ];then
    echo "配置文件不存在,自动退出"
    exit 0
fi

##step3:put成功后检查源程序文件有没有到位
echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_check_source_data','running')"`
flag_success=$(hadoop fs -ls ${inputtag}_SUCCESS |wc -l)
flag_time=$(hadoop fs -ls ${inputtag}_SUCCESS |awk '{print $6}')
num=0
until [ $flag_success -eq 1 ]
 do
   sleep 1s
   ((num++))
  if [ $num -gt 3 ];then
       subject="${thedate}源数据未生成,使用昨天数据进行合并人群包"
       echo $subject
#       send_sms  $subject caoyanwei
       before_yestoday=$(/bin/date -d-2day "+%Y%m%d")
       input=$elevenTagPath/$before_yestoday/
       flag_time=$today
       flag_success=1
  else
     flag_success=$(hadoop fs -ls ${inputtag}_SUCCESS |wc -l)
     flag_time=$(hadoop fs -ls ${inputtag}_SUCCESS |awk '{print $6}')
  fi
 done

##step4:
if [ $num -le 4 ];then
 echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_check_source_data','success')"`
 echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_hadoop_execute','running')"`
  sleep 1s
hadoopjar=$?
num=0
until [ $hadoopjar -eq 1 ]
do
hadoop fs -rm -r ${parentHdfsDir}/hdfsCrowdAutoHours/${thedate}*/
hadoop fs -rm -r $output
if ! hadoop jar /usr/hdp/*******-315/hadoop-mapreduce/hadoop-streaming.jar \
    -D mapred.job.name="$jobname" \
    -D mapred.min.split.size=128000000 \
    -D mapred.max.split.size=128000000 \
    -D mapred.reduce.tasks=50  \
    -D mapreduce.job.queuename=yushu_offline_high \
    -D mapreduce.job.running.map.limit=500 \
    -jobconf mapred.job.priority='VERY_HIGH' \
    -input $input \
    -output $output \
    -mapper "python mrParserLabelJson.py" \
    -reducer "cat" \
    -file $parentScriptDir/common/mrParserLabelJson.py \
    -file $configPath$configFile ;then
    message="`hostname`:$cwd/`basename $0` $today log parse failed"
    subject="$jobname,一次性小时级人群包规则解析hadoop_job_failed,请检查hdfsjson到hdfs的Hadoop yarn执行日志!"
#    send_sms  $subject caoyanwei
    echo -e "$subject \n  $message"
    ((num++))
    if [ $num -eq 3 ];then
    echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_hadoop_execute','failed')"`
    exit 1
    fi
    sleep 1s
else
echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_hadoop_execute','success')"`
 hadoopjar=1
fi
  done


if [ $? -eq 0 ];then
    ##hdfs数据进行下载,上传s3服务
     resultDataCount=$(hadoop fs -count $output|awk '{print $3}')
    if [ $resultDataCount -gt 0 ];then
       $pythonPath $parentScriptDir/hdfsCrowdAutoHours/hdfsCrowdAutoMerge.py $localFileDir $output $parentHdfsDir/hdfsCrowdAutoHours/$todaymin/ $configPath$configFile$exebatch crowd_auto_$todaymin $parentScriptDir
    else
        subject="$todaymin小时级别客群点击手动后自动上传s3失败，数据量为0,请检查源程序~"
        echo $subject
        #send_sms  $subject caoyanwei
    fi
  fi
else
   subject="$today标签系统hdfsOutPutRedis源程序表未执行完毕，已经重试3次，请检查源程序~"
    echo `$mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','autoHours_check_source_data','failed')"`
    #send_sms  $subject caoyanwei
     echo $subject
fi


