#!/bin/bash
##  file name     : deleteExpiredCrowd.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2023-05-24
##  copyright     : @qctx
##  description   : 一体化人群入资源位数据过期数据删除
##  usage         : 对JSON解析输出到HDFS,形成redis需要得key，value
##  function list :
##  history       : 第一版脚本，无历史
# set -e
set -x
unzip offline_label_application_v3_compare.zip
thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
  todayhour=$(/bin/date -d-0day "+%Y%m%d%H")

  todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
fi
cwd=$(
  cd $(dirname "$0")
  pwd
)
#source ./alarm.sh
local_file_path=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application_doris/resource_crowd_data_v2/expired/
rm -rf $local_file_path"expired.conf"${todayhour}*

file_name="expired.conf"$todaymin
file_path=$local_file_path$file_name
hdfs_path=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/resource_crowd_expired_temp2/pday=${thedate}

hadoop fs -getmerge ${hdfs_path} $file_path
exec=$?
if [ $exec != 0 ]; then
    echo "数据不存在"
    exit 0
fi
if [ ! -e  $file_path ] || [ ! -s  $file_path  ] ; then
 exit 0
fi
pythonPath=python3
parentScriptDir=$cwd
if [ ! -e  $file_path ] && [ ! -s $file_path ] ; then
    echo "文件不存在"
    exit 0
fi
$pythonPath $parentScriptDir/normalCrowdResource/deleteExpired/deleteExpiredCrowd_2.py $file_path
result=$?
if [ $result -eq 0 ]; then
    rm -rf ${local_file_path}/${file_name}
	rm -rf ${file_path}_*
else
    echo "deleteExpiredCrowd run failed"
    exit 1
fi


