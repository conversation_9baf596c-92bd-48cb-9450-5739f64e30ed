insert overwrite credit_data.resource_crowd_expired_temp2 partition(pday = '${yesterday_p}')
select userno,concat_ws(',',sort_array(collect_list(crowdno))) as crowdno
,crowdtype as crowdtype from (
select user_no as userno,t.crowd_no as crowdno,crowd_type as crowdtype from 
(select user_no, crowd_no, crowd_type   from credit_data.resource_crowd_expired_temp  where pday= '${yesterday_p}') t1 lateral view  explode(split(crowd_no,",")) t as crowd_no group by user_no,t.crowd_no,crowd_type
) t
group by t.userno ,t.crowdtype
