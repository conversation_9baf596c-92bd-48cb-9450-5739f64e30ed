#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'luoxin'
'''
-- file name     : label_output_manage.py
-- version       : v1.0
-- date          : 2025/05/06 00:00
-- description   : 标签输出管理结果到hive
'''
import sys
sys.path.append("./")
import subprocess
import traceback
import time
import os
from sendNoticeForSls import SendNoticeForSls
import requests

class LoadCtpAppData:
    def __init__(self, thedate,auth,url,tabelName):
        self.thedate = thedate
        self.auth = auth
        self.url = url
        self.hadoop = "hadoop"
        self.spark = "spark-sql --name 'label_output_manage_to_hive_luoxin' --driver-memory 3g --executor-memory 3g --conf spark.dynamicAllocation.maxExecutors=100 --conf spark.task.maxFailures=10 --conf spark.sql.broadcastTimeout=1800  --conf spark.executor.memoryOverhead=3g --conf spark.driver.maxResultSize=8192M --master=yarn --hiveconf hive.cli.print.header=false --conf spark.yarn.queue=yushu_offline_high"
        self.pday = thedate
        self.table_name = tabelName
        self.table_path = f"/user/hive/warehouse/credit_data.db/{self.table_name}/pday={self.pday}"
        self.file_name = f"./{tabelName}.sql"

    def queryData(self):
        qryCallDetails = []
        try:
            # 1、注册客群
            registerPayload = {
                "bagJson": '{"label":{"condConnect":"and","attribute":{"label":[{"labelKey":"register_time","operate":"gte_lte","labelValue":[0,99999999]}]}},"exclude":{"condConnect":"and"}}',
                "bagClass": 1,
                "ver": "v4"
            }
            registerCount = self.sendReq(registerPayload,"注册客群")
            qryCallDetails.append({"type": "allRegistrations", "count": str(registerCount),
                                   "stat_time": time.strftime("%Y-%m-%d  %H:%M:%S")})

            # 2、授信客群
            creditPayload = {
                "bagJson": "{\"label\":{\"condConnect\":\"and\",\"behavior\":{\"label\":[{\"labelKey\":\"first_apply_action\",\"operate\":\"match\",\"child\":{\"label\":[{\"labelKey\":\"product_code\",\"operate\":\"eq\",\"labelValue\":[\"P5\",\"360API\",\"360PLUS\",\"360SME\"]}]}}]}},\"exclude\":{\"condConnect\":\"and\"}}",
                "bagClass": 1,
                "ver": "v4"
            }
            creditCount=self.sendReq(creditPayload, "授信客群")
            qryCallDetails.append({"type": "allCredit", "count": str(creditCount),
                                "stat_time": time.strftime("%Y-%m-%d  %H:%M:%S")})
        except Exception as e:
            str_e = str(e)
            msg = f"查询P5授信客群或注册客群(请求失败异常)={str_e}"
            print(msg)
            raise Exception(msg)
        return qryCallDetails

    def sendReq(self,payload,reqType):
        headers = {
            "Authorization": self.auth
        }
        response = requests.post(self.url, headers=headers, json=payload,timeout=8)
        print("请求结果response:",response.json())
        result, count = self.process_response(response.json())
        if not result:
            msg=f"对外标签管理,查询{reqType}失败"
            raise Exception(msg)
        return count

    def process_response(self,response):
        if response.get("flag") == "S":
            print("状态: 成功")
            count = response.get("data", {}).get("count")
            return True, count
        else:
            print(f"状态: 失败 (错误码: {response.get('code')},  消息: {response.get('msg')})")
            return False, None

    def writeFile(self,values):
        if len(values) == 0:
            return
        valueList = []
        for v in values:
            value = v['type']+"\001" + v['count']+"\001"+v['stat_time']
            valueList.append(value)
        result = "\n".join(valueList)
        print(f"写入文件{len(values)},{len(valueList)}条")
        with open(self.file_name, "a") as f:
            f.write(f"{result}\n")

    def uploadFile(self):
        print("开始执行uploadFile")
        if os.path.exists(self.file_name):
            print("上传文件存在")
        else:
            print("上传文件不存在！！！")
            return
        start_time = time.time()
        cmd = "%s fs -test -e %s" % (self.hadoop, self.table_path)
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        if status != 0:
            # 路径不存在，创建路径
            print("Hadoop路径不存在，需要创建")
            cmd = "%s fs -mkdir %s" % (self.hadoop, self.table_path)
            print(cmd)
            status, output = subprocess.getstatusoutput(cmd)
            if status != 0:
                raise Exception(f"创建Hadoop路径失败:{status},{output}")
        else:
            print("Hadoop路径已存在")

        cmd = "%s fs -put -f %s %s" % (self.hadoop, self.file_name, self.table_path+"/")
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        #删除文件
        try:
            os.remove(self.file_name)
            print("文件删除成功:%s"%self.file_name)
        except OSError as e:
            traceback.print_exc()
            msg="对外标签输出管理,删除文件 Error: %s : %s" % (self.file_name, e.strerror)
            print(msg)
            raise Exception(msg)
        if status != 0:
            print(f"数据上传失败:{status},{output}")
            raise Exception(f"数据上传失败:{status},{output}")

        cmd_sql = f"{self.spark} -e \"alter table credit_data.{self.table_name} add if not exists partition(pday={self.pday})\""
        print(cmd_sql)
        status, output = subprocess.getstatusoutput(cmd_sql)
        end_time = time.time()
        if status != 0:
            print(f"数据合并失败：{str(status)},{str(output)}")
            raise Exception("数据合并失败")
        print("执行uploadFile结束,耗时: {:.3f}秒".format(end_time - start_time))

    def runTask(self):
        try:
            qryCallDetails = self.queryData()
            self.writeFile(qryCallDetails)
            self.uploadFile()
        except Exception as e:
            traceback.print_exc()
            message = "对外标签管理获取数据失败,Reason:%s,请及时排查问题" % (e)
            print(message)
            SendNoticeForSls(message).notice_teams()
            try:
                os.remove(self.file_name)
                print("对外标签管理文件删除成功:%s"%self.file_name)
            except OSError as e:
                traceback.print_exc()
                msg="对外标签管理文件删除失败Error: %s : %s" % (self.file_name, e.strerror)
                print(msg)
                SendNoticeForSls(msg).notice_teams()

if __name__ == '__main__':
    thedate = sys.argv[1]
    auth = sys.argv[2]
    url = sys.argv[3]
    tabelName = sys.argv[4]
    load = LoadCtpAppData(thedate,auth,url,tabelName)
    load.runTask()