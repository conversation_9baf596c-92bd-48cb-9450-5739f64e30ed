#!/bin/bash
# file name     : batch_query_call_detail_to_hive.sh
# author        : wangxianqiang
# version       : v1.0
# date          : 2024-02-23
# description   :
scriptDir=.
python=/usr/bin/python3.6

thedate=$1

if ! test $thedate;then
     thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi

dateType="yesterday"
${python} ${scriptDir}/batch_query_call_detail_to_hive.py ${thedate} ${dateType}

if [ $? -eq 0 ];then
    echo "查询语音流控详情到hive成功！"
else
    echo "查询语音流控详情到hive失败！"
    exit 1
fi

if [ -f "$scriptDir/data/$thedate$dateType.csv" ]; then
    echo "batch_query_call_detail_to_hive文件存在！"
fi

rm -f ${scriptDir}/logs/${thedate}${dateType}.log
rm -f ${scriptDir}/data/${thedate}${dateType}.csv