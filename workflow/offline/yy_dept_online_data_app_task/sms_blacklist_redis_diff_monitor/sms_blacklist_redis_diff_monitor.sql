#!/bin/bash
# file name     : blacklist_redis_diff_monitor.sh
# author        : wangxianqiang
# version       : v1.0
# date          : 2022-11-03
# description   :
scriptDir=.
python=/usr/bin/python3.6

thedate=$1

if ! test $thedate;then
     thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi

table="credit_data.prd_jietiao_global_sms_blacklist_redis_diff_pdi"
diff="35000"
${python} ${scriptDir}/blacklist_redis_diff_monitor.py ${thedate} ${table} ${diff}

if [ $? -eq 0 ];then
    echo "blacklist_redis_diff_monitor执行成功！"
else
    echo "blacklist_redis_diff_monitor执行失败！"
    exit 1
fi

if [ -f "$scriptDir/$thedate.csv" ]; then
    echo "blacklist_redis_diff_monitor文件存在！"
fi

#delthedate=$(/bin/date -d "-3day" +"%Y%m%d")
rm -f ${scriptDir}/logs/${thedate}_gns.log
rm -f ${scriptDir}/${thedate}_gns.csv