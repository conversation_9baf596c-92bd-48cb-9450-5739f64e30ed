#!/bin/bash
##file name     : main315loadRedis.sh
##author        : cyw
##version       : v1.0
##date          : 2021-02-28
##copyright     : @qct
##description   : 加载数据到redis
##usage         : 加载数据到redis
##function list : 加载数据到redis
##history       : 第一版脚本，无历史

set -x
thedate=$(/bin/date -d-1day "+%Y%m%d")

localpath=./
pythonPath=/usr/bin/python3.6

${pythonPath} ${localpath}temp_315_beijing_loadredis.py

if [ $? -eq 0 ];then
  echo "临时黑名单导出成功!!!"
else
  echo "临时黑名单导出失败!!!"
  rm -f ./data/temp_315_beijing_loadredis_${thedate}.csv
  exit 1
fi

if [ -f "./data/temp_315_beijing_loadredis_${thedate}.csv" ]; then
    echo "temp_315_beijing_loadredis_文件存在！"
fi
rm -f ./data/temp_315_beijing_loadredis_${thedate}.csv
if [ -f "./data/temp_315_beijing_loadredis_${thedate}.csv" ]; then
    echo "temp_315_beijing_loadredis_文件存在,需删除！"
fi