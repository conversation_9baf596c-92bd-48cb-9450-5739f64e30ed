CREATE TABLE IF NOT EXISTS  `credit_data`.`roster_offline_data_sync`(
    `user_no` STRING COMMENT '用户号',
    `date_appl_submit` STRING COMMENT '完件时间',
    `date_credit_succ` STRING COMMENT '授信时间',
    `fqdz_user_cnt_t0` bigint COMMENT '授信T0是否发起动支',
    `dz_user_cnt_t0` bigint COMMENT '授信T0是否动支成功',
    `update_time_stamp` bigint COMMENT '中台推送给零犀数据时间',
    `ctp_day` STRING COMMENT '来自触达表的pday'
    )
COMMENT'名单离线表'
    PARTITIONED BY(pday STRING COMMENT'天分区')
    STORED AS ORC;

-- 根据需要 启用下面删除数据语句
-- offline_50_day_ago_p `date -d "-51day" +"%Y%m%d"`
-- alter table credit_data.roster_offline_data_sync drop if exists partition(pday='${offline_50_day_ago_p}');

-- 添加字段
-- alter table credit_data.roster_offline_data_sync add columns (`dz_amt_t0` STRING COMMENT '授信T0动支金额');
-- alter table credit_data.roster_offline_data_sync add columns (`cwj_grp` STRING COMMENT '1.5归因分类');