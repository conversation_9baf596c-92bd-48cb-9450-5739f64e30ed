#!/bin/bash
# file name     : prd_jietiao_global_sms_blacklist.sh
# author        : wangxianqiang
# version       : v1.0
# date          : 2022-11-03
# description   :
scriptDir=.
python=/home/<USER>/python3/bin/python3.6

thedate=$1

if ! test $thedate;then
     thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi

${python} ${scriptDir}/load_jietiao_global_sms_blacklist.py ${thedate}

if [ $? -eq 0 ];then
    echo "load_jietiao_global_sms_blacklist同步redis成功！"
else
    echo "load_jietiao_global_sms_blacklist同步redis失败！"
    exit 1
fi

if [ -f "$scriptDir/data/$thedate.csv" ]; then
    echo "load_jietiao_global_sms_blacklist文件存在！"
fi

rm -f ${scriptDir}/logs/${thedate}.log
rm -f ${scriptDir}/data/${thedate}.csv
