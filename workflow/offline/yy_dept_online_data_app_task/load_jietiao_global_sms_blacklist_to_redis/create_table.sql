CREATE TABLE IF NOT EXISTS credit_data.prd_jietiao_global_sms_blacklist_merge_sync_pdi (
    mobile_no_md5x string COMMENT 'md5x手机号',
    status string COMMENT '状态',
    date_updated string COMMENT '更新时间',
    business_source string COMMENT '业务线',
    sub string COMMENT '描述',
    type string COMMENT 'black类型',
    sms_type string COMMENT '黑名单类型'
) COMMENT '借条全局短信黑名单增量同步表'
PARTITIONED BY (pday STRING COMMENT '分区日期')
ROW FORMAT DELIMITED
FIELDS TERMINATED BY '\001'
COLLECTION ITEMS TERMINATED BY ','
MAP KEYS TERMINATED BY ':'
STORED AS textfile;