#!/bin/bash
##  file name     : iscHdfsParserHours.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2023-05-24
##  copyright     : @qctx
##  description   : 一体化人群入资源位数据hdfs Out put redis
##  usage         : 对JSON解析输出到HDFS,形成redis需要得key，value
##  function list :
##  history       : 第一版脚本，无历史
# set -e
set -x
unzip offline_label_application_v3_stg_ljh.zip

for i in $(seq 1 800)
do
  echo "这是循环的第 $i 次"
  thedate=$1
  if ! test $thedate; then
    thedate=$(/bin/date -d-1day "+%Y%m%d")
    today=$(/bin/date -d-0day "+%Y-%m-%d")
    todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
    todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
  fi
  cwd=$(
    cd $(dirname "$0")
    pwd
  )
  #source ./alarm.sh
  pythonPath=python3
  parentScriptDir=$cwd

  #物理机地址
  localFileDir=$cwd

  configFile="rules.conf"
  exebatch="hours"


  ##step1: 执行python脚本生成配置文件
  rm -rf $localFileDir/iscHdfsParserHours/ruleConfig/data/rules.*
  $pythonPath $parentScriptDir/iscHdfsParserHours/produceConfig.py $localFileDir/iscHdfsParserHours/ruleConfig/ $configFile $exebatch

  ##step2:判断文件有没有生成，生成完毕后put hdfs
  configFilePath=$localFileDir/iscHdfsParserHours/ruleConfig/data/$configFile
  resultConf=$(ls $configFilePath | wc -l)
  if [ $resultConf -eq 0 ]; then
    echo "配置文件不存在,自动退出"
	sleep 60
	echo "继续"
    continue
  fi

  ##step2:下载文件并上送到hdfs
  $pythonPath $parentScriptDir/iscHdfsParserHours/ischdfsParserHours.py $localFileDir $configFilePath $output

  sleep 60
  echo "继续"
done
