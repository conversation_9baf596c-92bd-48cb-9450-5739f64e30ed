insert overwrite table credit_data.test_sls_sms_history_count_result_sync partition(pday='${yesterday_p}',count_type='business')
select
concat('s:h:r:JIETIAO:',rule_no,':{',mobile_no_md5x,'}') as redis_key,
concat('{\"d7\":',d7_times,',\"d15\":',d15_times,',\"d30\":',d30_times,'}') as redis_value
from
credit_data.test_sls_sms_history_count_result
where pday ='${yesterday_p}' and count_type='business';