insert overwrite table credit_data.test_sls_sms_history_count_request_log1 partition(pday='${yesterday_p}')
select
    distinct
    d.message_no,
    d.request_no,
    d.touch_mode,
    d.business_type,
    d.system_id,
    d.template_code,
    d.mobile_no_md5x,
    d.black_level,
    d.push_type,
    d.config_limit_size,
    d.config_limit_days,
    d.is_limit,
    d.sub_strategy_id,
    d.task_id,
    d.message_type,
    d.send_time,
    d.plan_id,
    d.operate_crowd,
    d.send_scene,
    d.black_type,
    d.user_no,
    d.event_id,
    d.rule_type,
    d.black_type_list,
    d.rule_no,
    d.salvage_send_scene,
    d.salvage_rule_no,
    d.salvage_event_id,
    d.ext_para
from credit_data.test_sls_sms_real_request_log d
where d.pday ='${yesterday_p}'
