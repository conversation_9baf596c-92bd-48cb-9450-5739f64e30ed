##  filename      : labelCrowdResource.sh
##  author        : z<PERSON><PERSON>g
##  version       : v1.0
##  date          : 2023-07-31
##  copyright     : @qctx
##  description   : 借条业务线自定义人群初始化
##  usage         : 借条业务线自定义人群初始化
##  function list :
##  history       : 第一版脚本
# set -e

set -x
echo $(hostname)
yesterday=${system.biz.date}
thedate=`date -d ${yesterday} +%Y%m%d`
todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")

if ! test $thedate; then
   thedate=`date -d-0day +%Y%m%d`
   
fi   
unzip offline_label_application_v3_normal_prd1.zip

cwd=$(
  cd $(dirname $0)
  pwd
)
source  ./alarm.sh
parentScriptDir=$cwd
localFileParentDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application_doris/resource_crowd_data_v2
### 跑数据临时存储目录
tempParentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_offline_resource_crowdrule

inputDorisHdfs=${tempParentHdfsDir}/pday=$thedate
# 有两个值，defined  label
exe_type=label_big1
pagesize=10000
pythonPath=python3

config_file_path=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application_doris/resource_crowd_data_v2/
config_name=conf_${exe_type}_${todaymin}
#config_name=conf_label_202308090954
$pythonPath $parentScriptDir/normalCrowdResource/produceConfig.py $config_file_path $config_name $exe_type
echo $?
if [ ! -e  $config_file_path$config_name ] && [ ! -s  $config_file_path$config_name  ] ; then
    exit 0
else
     $pythonPath $parentScriptDir/normalCrowdResource/normalCrowdResource.py $localFileParentDir $inputDorisHdfs $pagesize $exe_type  $config_file_path$config_name 100000
    result=$?
    if [ $result -eq 0 ]; then
        echo $config_file_path$config_name
        rm -rf $config_file_path$config_name 
    fi
        exit $result
fi
