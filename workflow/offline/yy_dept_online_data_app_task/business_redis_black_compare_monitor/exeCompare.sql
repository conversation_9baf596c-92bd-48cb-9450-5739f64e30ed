#!/bin/bash
# file name     : business_redis_black_compare_monitor.sh
# author        : luoxin
# version       : v1.0
# date          : 2023-04-13
# description   :
scriptDir=.
source /home/<USER>/anaconda_data_ai/venvs/py37_base/bin/activate

thedate=$1

if ! test $thedate;then
     thedate=$(/bin/date -d "-1day" +"%Y%m%d")
fi


echo "thedate="+${thedate}
python ${scriptDir}/business_redis_black_compare_monitor.py ${thedate}

if [ $? -eq 0 ];then
    echo "business_redis_black_compare_monitor,黑名单业务库和redis比较完成！"
else
    echo "business_redis_black_compare_monitor,黑名单业务库和redis比较失败！"
    exit 1
fi

if [ -f "$scriptDir/${thedate}_business_black_compare.csv" ]; then
    echo "business_redis_black_compare_monitor文件存在！"
fi

#delthedate=$(/bin/date -d "-3day" +"%Y%m%d")
rm -f ${scriptDir}/logs/${thedate}_business_black_compare.log
rm -f ${scriptDir}/${thedate}_business_black_compare.csv
