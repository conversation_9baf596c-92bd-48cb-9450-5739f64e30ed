CREATE TABLE IF NOT EXISTS credit_data.sls_global_sms_blacklist_redis (
    cache_key string COMMENT '缓存key',
    cache_value string COMMENT '缓存值',
    date_created string COMMENT '创建时间 yyyy-mm-dd hh:MM:ss'
) COMMENT '短信黑名单redis数据'
PARTITIONED BY (pday STRING COMMENT '分区日期')
ROW FORMAT DELIMITED
FIELDS TERMINATED BY '\001'
COLLECTION ITEMS TERMINATED BY ','
MAP KEYS TERMINATED BY ':'
STORED AS textfile;