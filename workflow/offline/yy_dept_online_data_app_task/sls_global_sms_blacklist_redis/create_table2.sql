CREATE TABLE IF NOT EXISTS credit_data.prd_jietiao_global_sms_blacklist_redis_diff (
  mobile_no_md5x STRING COMMENT '手机号MD5x码',
  status STRING COMMENT '状态',
  black_type_list STRING COMMENT '黑名单类型',
  business_source STRING COMMENT '业务线',
  date_updated STRING COMMENT '更新时间',
  sub STRING COMMENT '来源',
  sms_type_source STRING COMMENT '黑名单类型来源'
)
PARTITIONED BY (pday  STRING COMMENT '分区日期')
COMMENT '短信黑名单与redis差异表'
STORED AS orc;