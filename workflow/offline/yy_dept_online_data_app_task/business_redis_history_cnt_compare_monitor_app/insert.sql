-- 历史计次
-- 还要注意减次
-- 超频流控 和 回捞不做处理 仅仅是监控使用
insert overwrite table credit_data.business_redis_history_cnt_compare_monitor partition(pday='${yesterday_p}')
SELECT
    channel
     ,serial_no
     ,d7_times
     ,d15_times
     ,d30_times
     ,date_created
from (
         SELECT
             "ivr" as channel
              ,userno as serial_no
              ,if(sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,7),'-',''),1,0)) -  sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,7),'-',''),1,0)) <0,0,
                  sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,7),'-',''),1,0)) -  sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,7),'-',''),1,0))) as d7_times
              ,if(sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,15),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,15),'-',''),1,0))<0,0,
                  sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,15),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,15),'-',''),1,0)))as d15_times
              ,if(sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,30),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,30),'-',''),1,0))<0,0,
                  sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,30),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,30),'-',''),1,0)))as d30_times
              ,now() as date_created
         from (
                  SELECT * from
                      ods.prd_kafka_sync_fluidics_voice_flow_control_project_proc_hourly
                  WHERE pday >= date_format(date_sub(current_date,29),'yyyyMMdd')
                    and pday < date_format(date_sub(current_date,0),'yyyyMMdd')
                    and ((pushtype='count' and requesttype='add' and ruletype!='wTemplate') or (requesttype='reduce' and ruletype!='wTemplate'))
                    and userno not in (
                       select serial_no from credit_data.business_redis_history_cnt_compare_exception
                    )
                    and model_result != '1'
              )a
         GROUP by userno
             limit 10000
     )
union all
insert overwrite table credit_data.business_redis_history_cnt_compare_monitor partition(pday='${yesterday_p}')
SELECT
    channel
     ,serial_no
     ,d7_times
     ,d15_times
     ,d30_times
     ,date_created
from (
         SELECT
             "ivrNew" as channel
              ,userno as serial_no
              ,if(sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,7),'-',''),1,0)) -  sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,7),'-',''),1,0)) <0,0,
                  sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,7),'-',''),1,0)) -  sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,7),'-',''),1,0))) as d7_times
              ,if(sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,15),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,15),'-',''),1,0))<0,0,
                  sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,15),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,15),'-',''),1,0)))as d15_times
              ,if(sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,30),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,30),'-',''),1,0))<0,0,
                  sum(if(a.requesttype='add' and a.pday>replace(date_sub(current_date,30),'-',''),1,0)) - sum(if(a.requesttype='reduce' and a.pday>replace(date_sub(current_date,30),'-',''),1,0)))as d30_times
              ,now() as date_created
         from (
                  SELECT * from
                      ods.prd_kafka_sync_fluidics_voice_flow_control_project_proc_hourly
                  WHERE pday >= date_format(date_sub(current_date,29),'yyyyMMdd')
                    and pday < date_format(date_sub(current_date,0),'yyyyMMdd')
                    and ((pushtype='count' and requesttype='add' and ruletype!='wTemplate') or (requesttype='reduce' and ruletype!='wTemplate'))
                    and userno not in (
                       select serial_no from credit_data.business_redis_history_cnt_compare_exception
                    )
              )a
         GROUP by userno
             limit 10000
     )
union all
SELECT
    channel
     ,serial_no
     ,d7_times
     ,d15_times
     ,d30_times
     ,date_created
from (
  SELECT
      "sms" as channel
       ,mobile as serial_no
       ,sum(if(pday>replace(date_sub(current_date,7),'-',''),1,0)) as d7_times
       ,sum(if(pday>replace(date_sub(current_date,15),'-',''),1,0)) as d15_times
       ,sum(if(pday>replace(date_sub(current_date,30),'-',''),1,0)) as d30_times
       ,now() as date_created
  from (
           SELECT mobile,pday
           from ods.prd_kafka_sms_holdup_reason
           where pday >= date_format(date_sub(current_date,29),'yyyyMMdd')
             and pday < date_format(date_sub(current_date,0),'yyyyMMdd')
             and is_limit='0' and push_type='count' and (sendscene is null or sendscene not in ('onHook','whiteTemplate'))
       ) GROUP by mobile
      limit 10000
)
