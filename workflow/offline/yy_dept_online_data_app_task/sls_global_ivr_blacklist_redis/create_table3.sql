CREATE TABLE IF NOT EXISTS credit_data.prd_jietiao_global_ivr_blacklist_redis_diff_pdi (
  user_no STRING COMMENT '用户号',
  black_type_list STRING COMMENT '黑名单类型',
  data_source STRING COMMENT '来源',
  date_updated STRING COMMENT '更新时间',
  add_time STRING COMMENT '加入时间',
  release_time STRING COMMENT '释放时间',
  status STRING COMMENT '状态',
  flag_loss_status STRING COMMENT '流控状态',
  black_type_source  STRING COMMENT '黑名单类型来源'
)
PARTITIONED BY (pday  STRING COMMENT '分区日期')
COMMENT '黑名单与redis差异增量表'
STORED AS orc;