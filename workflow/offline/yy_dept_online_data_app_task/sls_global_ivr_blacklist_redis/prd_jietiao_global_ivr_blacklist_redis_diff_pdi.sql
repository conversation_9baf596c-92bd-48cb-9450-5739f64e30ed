insert overwrite table credit_data.prd_jietiao_global_ivr_blacklist_redis_diff_pdi partition(pday = '${yesterday_p}')
SELECT
    user_no,black_type_list,data_source,date_updated,add_time,release_time,status,flag_loss_status,black_type_source
from (
        SELECT
            a.cache_key as user_no,
            regexp_replace(a.blacktypeList, '["\\[|\\]"]', '') AS black_type_list,
            regexp_replace(a.dataSource, '["\\[|\\]"]', '') AS data_source,
            a.dateUpdated as date_updated,
            a.addTime as add_time,
            a.releaseTime as release_time,
            a.status as status,
            a.flagLossStatus as flag_loss_status,
            a.black_type as black_type_source
        from (
          SELECT cache_key,
                get_json_object(cache_value,'$.flagLossStatus') as flagLossStatus,
                get_json_object(cache_value,'$.blacktypeList') as blacktypeList,
                get_json_object(cache_value,'$.dataSource') as dataSource,
                get_json_object(cache_value,'$.dateUpdated') as dateUpdated,
                get_json_object(cache_value,'$.addTime') as addTime,
                get_json_object(cache_value,'$.releaseTime') as releaseTime,
                get_json_object(cache_value,'$.black_type') as black_type,
                get_json_object(cache_value,'$.status') as status
            from  (
                SELECT DISTINCT cache_key,cache_value from credit_data.sls_global_ivr_blacklist_redis where pday='${yesterday_p}'
            ) d  where get_json_object(cache_value,'$.flagLossStatus')='Y'
            and (get_json_object(cache_value,'$.addTime') is null or get_json_object(cache_value,'$.addTime')='' or get_json_object(cache_value,'$.addTime') < '${today}')
        ) a left join (
        SELECT * from credit_data.prd_jietiao_global_ivr_blacklist_merge where pday='${yesterday_p}' and user_no like 'UR%' AND flag_loss_status = 'Y'
        ) b on a.cache_key = b.user_no where b.user_no is null
)