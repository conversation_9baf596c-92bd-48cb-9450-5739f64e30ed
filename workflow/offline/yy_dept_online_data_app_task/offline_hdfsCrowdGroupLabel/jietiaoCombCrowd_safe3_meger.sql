#!/bin/bash
##  file name     : jietiaoCombCrowd.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2022-12-02
##  copyright     : @qctx
##  description   : 借条业务线人群组合
##  usage         : 借条业务线人群组合
##  function list :
##  history       : 第二版脚本，参考caoyanwei第一版
# set -e

source ~/.bashrc
set -x
unzip offline_label_application_v3

thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todayyyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
  sevenDay=$(/bin/date -d-7day "+%Y%m%d")
  threeDay=$(/bin/date -d-4day "+%Y%m%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M%S%N")
fi

cwd=$(
  cd $(dirname $0)
  pwd
)
source $cwd/utils/smsUtil.sh
source $cwd/utils/checkSourceUtil.sh

parentScriptDir=$cwd
localFileParentDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application/comb_label_crowd
#标签人群
parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/comb_label_crowd

pythonPath=python3

input="/user/hive/warehouse/dp_data_db.db/tag_user_data_mr/v2/custElevenTagSteup_2"
#依赖11点标签，工作流依赖
inputtag="/user/hive/warehouse/dp_data_db.db/tag_user_data_mr/v2/custElevenTagSteup_2/mrTag/$thedate"

inputHdfsPath=$input/$thedate
inputHdfsPath=/user/hive/warehouse/dp_data_db.db/credit_data_tag_jietiao_eleven_label_merge
inputtag=/user/hive/warehouse/dp_data_db.db/credit_data_tag_jietiao_eleven_label_merge/_YUSHU_SUCCESS
##业务线
business_type="jietiao"

##读取业务线
readEvn="online"

##读取处理业务类型
readDisposeType="crowd_composite"

##处理作业人
readDisposeName="caoyanwei"

##处理周期
dispose_rel="hourly"

jobname=${readDisposeName}_${readEvn}_${readDisposeType}_${dispose_rel}

outputParent="$parentHdfsDir/${business_type}_${readEvn}/hdfsCrowdGroupLabel"

configFile="rules.conf"

groupConfigFile="groupRules.conf"

configPath=$localFileParentDir/$business_type/$readDisposeType/ruleConfig/$todaymin/

exebatch=${business_type}_${readDisposeType}_${dispose_rel}

definedHdfs=$outputParent/defined

combOutPutResult=$outputParent/combResultOutPut/
#本地
comParentScript=$parentScriptDir/hdfsCrowdGroupLabel

mergeOutPutResult=$outputParent/mergeResult

mroutput=$outputParent/$readDisposeType/$todaymin

function combCrowdOutPut() {
  hadoop fs -rm -r $combOutPutResult/${thedate}*/
  combResultOutPutData=$combOutPutResult/$todaymin/
  ### 人群组合后输出结果
  $pythonPath "$comParentScript"/model/hdfsCombineRule.py "$parentScriptDir" "$configPath" $groupConfigFile $mergeOutPutResult/"$todaymin" "$combResultOutPutData" "$todaymin" $localFileParentDir

}

function sparkMerge() {
  # 对数据块进行merge，两类数据块 1.规则数据块  2.自定义上传数据块
  hadoop fs -rm -r $mergeOutPutResult/"$thedate"*/
  resultOutputData=$mergeOutPutResult/$todaymin/
  hadoop fs -rm -r "$resultOutputData"

  #如果数据块为spark2 证明存在规则包
  definedOutputResultCount=$(hadoop fs -count $definedHdfs/$business_type/$todaymin/ | awk '{print $3}')
  definedOutputResult=$?
  if [ $definedOutputResult -ne 0 ]; then
    definedOutputResultCount=0
  fi
  mrOutputCount=$(hadoop fs -count $mroutput | awk '{print $3}')
  if [ $definedOutputResultCount -gt 0 ] && [ $mrOutputCount -gt 0 ]; then
    spark-submit --num-executors 400 --executor-memory 4g --queue yushu_offline_high --driver-memory 1g $parentScriptDir/common/sparkJoinData.py $mroutput $definedHdfs/$business_type/$todaymin/*/* $resultOutputData
  elif [ $mrOutputCount -gt 0 ]; then
    spark-submit --num-executors 400 --executor-memory 4g --queue yushu_offline_high --driver-memory 1g $parentScriptDir/common/sparkJoinData.py $mroutput $resultOutputData
  elif [ $definedOutputResultCount -gt 0 ]; then
    spark-submit --num-executors 400 --executor-memory 4g --queue yushu_offline_high --driver-memory 1g $parentScriptDir/common/sparkJoinData.py $definedHdfs/$business_type/$todaymin/*/* $resultOutputData
  fi
  sparkFlag=$?
  if [ $sparkFlag -ne 0 ]; then
    echo "spark submit fail!!!!!"
    exit 1
  fi
  mergeOutputResultCount=$(hadoop fs -count $resultOutputData | awk '{print $3}')

  if [ $? -eq 0 ] && [ $mergeOutputResultCount -gt 0 ]; then
    combCrowdOutPut
  else
    echo "mergeOutputResultCount数据量为0，程序退出！！！"
    exit 0
  fi

}

function parseHdfs() {
  # hdfs文件数据解析

  hadoopjar=$?
  echo -e "start parseHdfs "
  num=0
  until [ $hadoopjar -eq 1 ]; do
    hadoop fs -rm -r "$mroutput"
    hadoop fs -rm -r $outputParent/$readDisposeType/"$thedate"*
    if
      ! hadoop jar /usr/hdp/*******-315/hadoop-mapreduce/hadoop-streaming.jar \
      -archives "hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/j-zhangyong1-jk/python3-3.6.tgz#python" \
      -D mapred.job.name="$jobname"\
      -D mapred.min.split.size=128000000 \
      -D mapred.max.split.size=128000000 \
      -D mapreduce.job.running.map.limit=400 \
      -D mapred.reduce.tasks=50 \
      -D mapred.job.priority='VERY_HIGH' \
      -D mapreduce.job.queuename=yushu_offline_normal \
      -input $inputHdfsPath\
      -output $mroutput \
      -mapper "./python/python3/bin/python3.6 mrParserLabelJson.py" \
      -reducer "cat" \
      -file $parentScriptDir/common/mrParserLabelJson.py \
      -file $configPath$configFile
    then
      subject="$jobname,组合人群hdfs解析规则失败,请检查下mr解析情况!"
      echo -e $subject
      ((num++))
      if [ $num -eq 3 ]; then
        $pythonPath $comParentScript/model/hdfsPostGroupUploadS3.py $configPath$groupConfigFile
        exit 1
      fi
      sleep 1s
    else
      hadoopjar=1
    fi
  done

  sparkMerge spark2

}

function hdfsConfig() {

  resultConf=$(ls $configPath$configFile | wc -l)
  if [ $resultConf -gt 0 ]; then
    flag_success=$(hadoop fs -ls ${inputtag} | wc -l)
    flag_time=$(hadoop fs -ls ${inputtag} | awk '{print $6}')
    echo -e "当日日期：$today"
    num=0
    until [ "$flag_success" -eq 1 ] && [ "$flag_time" == "$today" ]; do
      echo -e "当日safe3拷贝文件未准备好，sleep 30s,当日日期："$today "次数:"$num
      ((num++))
      sleep 30s
      flag_success=$(hadoop fs -ls ${inputtag} | wc -l)
      flag_time=$(hadoop fs -ls ${inputtag} | awk '{print $6}')
    done
    parseHdfs
  else
    sparkMerge spark1
  fi

}

function exeProducer() {
  rm -rf "$configPath"$configFile
  rm -rf "$configPath"$groupConfigFile
  hadoop fs -rm -r $definedHdfs/$business_type/"$thedate"*
  $pythonPath $comParentScript/MainCombCrowd.py $business_type $readEvn "$configPath" $configFile $groupConfigFile $definedHdfs/$business_type/$todaymin $comParentScript $localFileParentDir
  groupRuleConfig=$(ls ${configPath}${groupConfigFile} | wc -l)
  if [ "$groupRuleConfig" -eq 0 ]; then
    echo "配置文件不存在，程序退出"
  else
    hdfsConfig
  fi
}
exeProducer
