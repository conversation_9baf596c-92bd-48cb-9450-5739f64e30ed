#!/bin/bash
##  file name     : iscHdfsParserHours.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2023-05-24
##  copyright     : @qctx
##  description   : 一体化人群入资源位数据hdfs Out put redis
##  usage         : 对JSON解析输出到HDFS,形成redis需要得key，value
##  function list :
##  history       : 第一版脚本，无历史
# set -e
set -x
unzip offline_label_application_isc_prd.zip
thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
  todayyyymmdd=$(/bin/date -d-0day "+%Y%m%d")
fi
cwd=$(
  cd $(dirname "$0")
  pwd
)

exe_type="big1"

## 控制并发的工作流名称，根据具体工作流进行修改
flowName="iscHdfsParserHours"$exe_type

## 并发控制
controlFilePath="/user/hive/warehouse/dp_data_db.db/tag_user_data_mr/resource/control/"
filePath="${controlFilePath}${flowName}"
echo "filePath:${filePath}"
hadoop fs -test -e $controlFilePath
check=$?
echo $check
if [ $check = 1 ]; then
	hadoop fs -mkdir ${controlFilePath}
fi
doingFiles=$(hadoop fs -ls ${filePath} | wc -l)
echo "doingFiles=$doingFiles"
if [ ${doingFiles} = 1 ]; then
	echo "当前${flowName}正在执行，直接结束"
	exit 0
fi
hadoop fs -touchz ${filePath}
echo "开始执行业务逻辑"

function rm_lock_file(){

		echo "执行业务逻辑结束，删除并发控制文件"
		hadoop fs -rm ${filePath}
}
pythonPath=python3
parentScriptDir=$cwd

#物理机地址
localFileDir=$cwd
output=/user/hive/warehouse/credit_data.db/isc_temp_offline_resource_crowdrule

configFile="rules.conf"

config_name=${configFile}_${exe_type}_${todaymin}

##step1: 执行python脚本生成配置文件
# rm -rf $localFileDir/iscHdfsParserHours/ruleConfig/rules.*
$pythonPath $parentScriptDir/iscHdfsParserHours/produceConfig.py $localFileDir/iscHdfsParserHours/ruleConfig/ $config_name $exe_type

##step2:判断文件有没有生成，生成完毕后put hdfs
configFilePath=$localFileDir/iscHdfsParserHours/ruleConfig/data/$config_name
resultConf=$(ls $configFilePath | wc -l)
if [ $resultConf -eq 0 ]; then
  echo "配置文件不存在,自动退出"
  rm_lock_file
  exit 0
fi

##step2:下载文件并上送到hdfs
$pythonPath $parentScriptDir/iscHdfsParserHours/ischdfsParserHours.py $localFileDir $configFilePath $output
status=$?
rm_lock_file
