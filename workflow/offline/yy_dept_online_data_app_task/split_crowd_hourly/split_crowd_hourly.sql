#!/bin/bash
# file name       : splitCrowdDataHourly.sh
# author          : zhangyong
# version         : v1.1
# date            : 2022年12月27日
# description     : 拆分人群包数(小时级)
#
set -x
unzip offline_label_application2.zip
thedate=$1
if ! test $thedate ; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
fi

cwd=$(cd $(dirname $0) ; pwd)

parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/temp_resource_crowd_data
parentScriptDir=$cwd
#localFileDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application
pythonPath=/home/<USER>/anaconda2/bin/python

jobname="yushu_splitCrowdParser_hourly_$todaymin"

elevenTagPath=hdfs://360jinronglycc/user/hive/warehouse/dp_data_db.db/tag_user_data_mr/v2/custElevenTagSteup_2
input=$elevenTagPath/$thedate/
#依赖11点标签，工作流依赖
inputtag=$elevenTagPath/mrTag/$thedate

output=$parentHdfsDir/crowd_split_hourly/$todaymin/
configFile="rules.conf"
configPath=$parentScriptDir/splitCrowdRuleHourly/ruleConfig/$todaymin/
#configPath=$localFileDir/splitCrowdRuleHourly/ruleConfig/$todaymin/
exebatch="splitCrowd_hourly"

mysql_host=$(grep -r host ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_port=$(grep -r port ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_user=$(grep -r user ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_pass=$(grep -r pass ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_db=$(grep -r db= ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')

dispose_date='ys_'${todaymin}

##记录流程阶段
mysql_excute="mysql -h${mysql_host} -u${mysql_user} -p${mysql_pass} -P${mysql_port}   ${mysql_db} --default-character-set=utf8"
echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_init_config','running')")
##step1: 执行python脚本生成配置文件
rm -f $configPath/$configFile
$pythonPath $parentScriptDir/common/produceConfig.py $configPath $configFile $exebatch

if [ $? -eq 0 ];then
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_init_config','success')")
else
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_init_config','failed')")
fi

##step2 判断文件是否存在，生成完毕后put hdfs
resultConf=$(ls $configPath$configFile | wc -l)
if [ $resultConf -eq 0 ];then
  echo "配置文件不存在，程序退出"
  exit 0
fi

## step2 put成功后检查源程序文件有没有到位
echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_check_source_data','running')")
flag_success=$(hadoop fs -ls ${inputtag}_SUCCESS | wc -l)
flag_time=$(hadoop fs -ls ${inputtag}_SUCCESS | awk '{print $6}')
num=0
until [ $flag_success  -eq 1 ] && [ $flag_time=$today ]; do
  sleep 1s
  ((num++))
  if [ $num -gt 3 ];then
    subject="${thedate} 源数据未生成，使用昨天的数据进行合并人群包"
    echo -e $subject
    before_yesterday=$(/bin/date -d-2day "+%Y%m%d")
    input="$elevenTagPath/$before_yesterday/"
    flag_time=$today
    flag_success=1
  else
    flag_success=$(hadoop fs -ls ${inputtag}_SUCCESS | wc -l)
    flag_time=$(hadoop fs -ls ${inputtag}_SUCCESS | awk '{print $6}')
  fi
done

if [ $num -le 4 ]; then
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_check_source_data','success')")
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_hadoop_execute','running')")
  sleep 1s
  hadoopjar=$?
  num=0
  until [ $hadoopjar -eq 1 ]; do
    hadoop fs -rm -r $output
    if ! hadoop jar /usr/hdp/*******-315/hadoop-mapreduce/hadoop-streaming.jar \
      -D mapred.job.name="$jobname" \
      -D mapred.min.split.size=128000000 \
      -D mapred.max.split.size=128000000 \
      -D mapred.reduce.tasks=50 \
      -D mapreduce.job.queuename=yushu_offline_high \
      -D mapreduce.job.running.map.limit=500 \
      -jobconf mapred.job.priority='VERY_HIGH' \
      -input $input \
      -output $output \
      -mapper "python mrParserLabelJson.py" \
      -reducer "cat" \
      -file $parentScriptDir/common/mrParserLabelJson.py \
      -file $configPath$configFile
    then
      message="`hostname`:$cwd/`basename $0` $today log parse failed"
      subject="$jobname,小时级hadoop_job_failed，为redis处理规则出现hdfs问题，请检查parseLabelJson到hdfs的hadoop 执行日志"
      echo -e "$subject \n $message"
      ((num++))
      if [ $num -eq 3 ];then
      #  $pythonPath $parentScriptDir/common/hdfsPostLabelUploadS3.py $configPath$configFile
       echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_hadoop_execute','failed')")
        exit 1
      fi
      sleep 1s
    else
        echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_hadoop_execute','success')")
        hadoopjar=1
    fi
  done


## step 5 对人群包数据进行拆分，先判断人群包有没有生成
echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_spark_split','running')")
hourDataCount=$(hadoop fs -count ${output} | awk '{print $3}')
split_result_output=$parentHdfsDir/split_result_output/$todaymin
localpath=$parentScriptDir/splitCrowdRuleHourly/downloadpath/
splitCycle="hourly"
if [ $hourDataCount -gt 0 ]; then
   $pythonPath $parentScriptDir/common/readMysqlSplit.py $configPath $configFile $todaymin $output $split_result_output $localpath $parentScriptDir $splitCycle
else
  subject="数智平台数据程序拆分异常，请检查执行执行$todaymin"
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_spark_split','failed')")
      #  $pythonPath $parentScriptDir/common/hdfsPostLabelUploadS3.py $configPath$configFile
  exit 1
fi
else
  subject="数智平台数据程序拆分异常,当前执行失败大于4次，请检查执行执行$todaymin"
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_hourly_check_source_data','failed')")
  exit 1
fi