#!/bin/bash
# file name       : splitCrowdDataDailyDoris.sh
# author          : zhangyong
# version         : v1.1
# date            : 2022年12月27日
# description     : 拆分人群包数(天级)
#

set -x
unzip offline_label_application_doris.zip
thedate=$1
if ! test $thedate; then
  thedate=$(/bin/date -d-1day "+%Y%m%d")
  today=$(/bin/date -d-0day "+%Y-%m-%d")
  todaymin=$(/bin/date -d-0day "+%Y%m%d%H%M")
fi
cwd=$(
  cd $(dirname $0)
  pwd
)

parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/temp_resource_crowd_data
parentScriptDir=$cwd
localFileDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application
pythonPath=/home/<USER>/anaconda2/bin/python

jobname="yushu_splitCrowdParser_hourly_$todaymin"


output=$parentHdfsDir/crowd_split_daily/$todaymin/

configFile="rules.conf"
#configPath=$localFileDir/splitCrowdRuleDaily/ruleConfig/$todaymin/
configPath=$parentScriptDir/splitCrowdRuleDaily/ruleConfig/$todaymin/
exebatch="jietiao_splitCrowd_daily"

mysql_host=$(grep -r host ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_port=$(grep -r port ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_user=$(grep -r user ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_pass=$(grep -r pass ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')
mysql_db=$(grep -r db= ${parentScriptDir}/common/db.conf | awk -F = '{print $2}')

dispose_date='ys_'${todaymin}

##记录流程阶段
mysql_excute="mysql -h${mysql_host} -u${mysql_user} -p${mysql_pass} -P${mysql_port}   ${mysql_db} --default-character-set=utf8"
echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_daily_init_config','running')")
##step1: 执行python脚本生成配置文件
rm -f $configPath/$configFile
$pythonPath $parentScriptDir/common/produceConfig.py $configPath $configFile $exebatch

if [ $? -eq 0 ]; then
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_daily_init_config','success')")
else
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_daily_init_config','failed')")
fi

##step2 判断文件是否存在，生成完毕后put hdfs
resultConf=$(ls $configPath$configFile | wc -l)
if [ $resultConf -eq 0 ]; then
  echo "配置文件不存在，程序退出"
  exit 0
fi

## step2 put成功后检查源程序文件有没有到位
echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_daily_check_source_data','running')")
$pythonPath $cwd/splitCrowdRule/splitCrowdDailyDownLoad.py $parentScriptDir $output $configPath$configFile $exebatch 10000
split_download_result=$?

if [ $split_download_result -ne 0 ] ; then
    echo "split_daily  failed "
    exit 1
fi

## step 5 对人群包数据进行拆分，先判断人群包有没有生成
echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_daily_spark_split','running')")
hourDataCount=$(hadoop fs -count ${output} | awk '{print $3}')
split_result_output=$parentHdfsDir/split_result_output_daily/$todaymin/
localpath=$parentScriptDir/splitCrowdRuleDaily/downloadpath/
splitCycle="daily"
if [ $hourDataCount -gt 0 ]; then
  echo "readMysqlSplit======"
  $pythonPath $parentScriptDir/common/readMysqlSplit.py $configPath $configFile $todaymin $output $split_result_output $localpath $parentScriptDir $splitCycle
else
  subject="数智平台数据程序拆分异常，请检查执行执行$todaymin"
  echo $($mysql_excute -e "insert into jt_label_hdfs2redis_flow_record (dispose_date,hdfs2redis_flow_stage,hdfs2redis_flow_status) values ('${dispose_date}','splitCrowd_daily_spark_split','failed')")
  exit 1
fi
