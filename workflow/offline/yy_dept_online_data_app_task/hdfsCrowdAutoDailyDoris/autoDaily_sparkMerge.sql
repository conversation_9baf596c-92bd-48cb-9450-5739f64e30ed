#!/bin/bash
##  file name     : autoDaily_sparkMerge.sh
##  author        : zhangyong
##  version       : v1.0
##  date          : 2023-05-18
##  copyright     : @qctx
##  description   :
##  usage         : hdfs数据自动保存日更新
##  function list :
##  history       : 第二版脚本，无历史
# set -e
source ~/.bashrc
set -x
unzip offline_label_application_doris


#使用工作流的调度时间
todaymin=$(/bin/date -d "1day ${system.biz.time}" "+%Y%m%d%H%M")


cwd=$(
  cd $(dirname $0)
  pwd
)
source ./alarm.sh

parentScriptDir=$cwd

localFileParentDir=/hdpData1/hdp-jinrong-loan/workdir/offline_label_application_doris/resource_crowd_data
### 跑数据临时存储目录
parentHdfsDir=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data/

inputDorisHdfs=${parentHdfsDir}autoDaily/$todaymin/

pythonPath=/home/<USER>/anaconda2/bin/python

output=$inputDorisHdfs
flag_success=$(hadoop fs -ls ${inputDorisHdfs}_SUCCESS | wc -l)
if [ $flag_success -gt 0 ]; then
  ##hdfs数据进行下载,上传s3服务
  resultDataCount=$(hadoop fs -count $output | awk '{print $3}')
  if [ $resultDataCount -gt 0 ]; then
    $pythonPath $parentScriptDir/hdfsCrowdAutoDaily/hdfsCrowdAutoDailyMerge.py $parentScriptDir $output ${parentHdfsDir}hdfsCrowdAutoDaily/$todaymin/ crowd_auto_$todaymin
  else
    subject="$todaymin例行任务自动保存上传s3失败，数据量为0,请检查源程序~"
    echo $subject
    send_notice "资源位-"${subject}
    exit 0
    #             send_sms  $subject caoyanwei
  fi
fi