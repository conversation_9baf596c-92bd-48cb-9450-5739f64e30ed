CREATE TABLE IF NOT EXISTS credit_data.prd_jietiao_global_ivr_blacklist_merge_sync_pdi (
    user_no string COMMENT '用户号',
    flag string COMMENT '标识',
    flag_loss_status string COMMENT '是否黑名单',
    data_source string COMMENT '数据来源方',
    date_updated string COMMENT '黑名单数据更新日期',
    add_time string COMMENT '黑名单添加时间',
    release_time string COMMENT '黑名单解除时间',
    status string COMMENT '黑名单状态',
    black_type string COMMENT '黑名单类型'
) COMMENT '借条全局语音黑名单增量同步表'
PARTITIONED BY (pday STRING COMMENT '分区日期')
ROW FORMAT DELIMITED
FIELDS TERMINATED BY '\001'
COLLECTION ITEMS TERMINATED BY ','
MAP KEYS TERMINATED BY ':'
STORED AS textfile;