INSERT
overwrite TABLE credit_data.ivr_flow_exption_summary_monitor_sync
select
 rule_no as ivr_type,
 interval_size as call_range,
 user_count  as call_range_count,
     CASE 
        WHEN LENGTH(pday) = 8 THEN 
            CONCAT(SUBSTRING(pday, 1, 4), '-', SUBSTRING(pday, 5, 2), '-', SUBSTRING(pday, 7, 2)) -- 完整日期格式：20250601 转为 2025-06-01
        WHEN LENGTH(pday) = 6 THEN 
            CONCAT(SUBSTRING(pday, 1, 4), '-', SUBSTRING(pday, 5, 2)) -- 年月格式：202506 转为 2025-06
        ELSE pday -- 如果不是符合格式的日期，保持原值
    END AS stat_day
from credit_data.ivr_flow_exption_summary_monitor  
where pday >=subString('${yesterday_p}',1,6)
-- where pday>''