-- Spark SQL 查询流控与拦截统计
INSERT
overwrite TABLE credit_data.ivr_flow_summary_monitor PARTITION(pday,type)
SELECT    
    pday as stat_date,
    ivrtype as ivr_type,
    'day' as date_type,
    period_id as period_id,
    '' as period_name,
    gray_level as gray_level,
    '' as white_template_code,
    SUM(CASE WHEN a.reduce_type = 'reduce' THEN 2 ELSE 1 END) AS call_count,                                -- 流控调用次数
    COUNT(DISTINCT a.user_no) AS user_count,                       -- 调用用户数
    
    SUM(CASE WHEN a.is_limit = 0 THEN 1 WHEN a.request_type='reduce' THEN -1 ELSE 0  END) AS real_call_count,                      -- 实际外呼次数
    COUNT(DISTINCT CASE WHEN a.is_limit = 0 THEN a.user_no END) AS real_call_user_count,     -- 实际外呼用户数
  
    SUM(CASE WHEN a.request_type = 'reduce' THEN 1 ELSE 0 END) AS reduce_count,              -- 减次次数
    COUNT(DISTINCT CASE WHEN a.request_type = 'reduce' THEN a.user_no END) AS reduce_user_count, -- 减次人数

    SUM(CASE WHEN a.push_type = 'limit' THEN 1 ELSE 0 END) AS flow_intercept_count,          -- 流控拦截次数
    COUNT(DISTINCT CASE WHEN a.push_type = 'limit' THEN a.user_no END) AS flow_intercept_user_count, -- 流控拦截人数	

    SUM(CASE WHEN a.push_type = 'black' THEN 1 ELSE 0 END) AS black_intercept_count,         -- 黑名单拦截次数		
    COUNT(DISTINCT CASE WHEN a.push_type = 'black' THEN a.user_no END) AS black_intercept_user_count, -- 黑名单拦截人数		

    SUM(CASE WHEN a.gray_rule_no IS NOT NULL THEN 1 ELSE 0 END) AS gray_intercept_call_count, -- 灰名单调用次数			
    SUM(CASE WHEN a.gray_rule_no IS NOT NULL AND a.is_limit = 1 THEN 1 ELSE 0 END) AS gray_intercept_count, -- 灰名单拦截次数				

    SUM(CASE WHEN a.overclock_result IN (1, 2) OR a.model_result IN (1, 2) THEN 1 ELSE 0 END) AS overclock_call_count, -- 超频加次调用次数（含模型加次）
    SUM(CASE WHEN (a.overclock_result IN (1, 2) OR a.model_result IN (1, 2)) AND a.is_limit = 1 THEN 1 ELSE 0 END) AS overclock_intercept_count, -- 超频加次拦截次数（含模型）

    SUM(CASE WHEN a.overclock_result IN (1, 2) THEN 1 ELSE 0 END) AS overclock_req_call_count, -- 超频申请调用次数（不含模型）
    SUM(CASE WHEN a.overclock_result IN (1, 2) AND a.is_limit = 1 THEN 1 ELSE 0 END) AS overclock_req_intercept_count, -- 超频申请拦截次数（不含模型）

    SUM(CASE WHEN a.model_result IN (1, 2) AND a.is_limit = 1 THEN 1 ELSE 0 END) AS model_add_call_count, -- 模型加次调用次数		
    
    SUM(CASE WHEN a.rule_type = 'global' AND a.is_limit = 1 THEN 1 ELSE 0 END) AS global_intercept_count, -- 全局流控拦截次数（兜底）		
    pday,
    '2' as type
FROM (
    SELECT 
        pday,
        ruleno as ivrtype,
        businessruleid AS period_id,
        userno AS user_no,
        islimit AS is_limit,
        pushtype AS push_type,
        requesttype AS request_type,
        ruletype AS rule_type,
        gray_rule_no,
        gray_level,
        overclock_result,                            -- 0 未使用 1 成功 2 加次失败
        model_result,             -- model_result 暂时用 overclock_result 替代
        reduce_type
    FROM credit_data.yushu_fluidics_voice_flow_control_project_proc_hourly  
    WHERE pday >= '${yesterday_p}'  and requesttype !='reduce'  and gray_level is not null
) a
group by pday,ivr_type,period_id,gray_level
