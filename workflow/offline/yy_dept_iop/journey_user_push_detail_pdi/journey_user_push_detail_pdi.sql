INSERT overwrite TABLE credit_data.journey_user_push_detail_pdi PARTITION(pday = '${yesterday_p}')
select
    x2.batch_number as batch_number,
    x.user_no,
    x.event_time,
    split(x2.batch_number,'_')[0] as strategy_id,
    x.template_name,
    case when y.msg_id != '' then 1 else 2 end as reach_status
from (
         select
             user_no,
             msg_id,
             registration_id,
             result,
             pn_no,
             'Y' as cal_frequency,
             date_gen as event_time,
             template_name
         from ods.prd_gns_push_push_notice
         where pday='${yesterday_p}'

         union all

         -- 20220126添加
         select
             user_no,
             msg_id,
             registration_id,
             result,
             pn_no,
             'N' as cal_frequency,
             date_gen as event_time,
             template_name
         from ods.prd_gns_push_push_notice_today
         where pday='${yesterday_p}'
) x
join (
    select
        task_id,
        user_no,
        callback_status,
        uuid
    FROM ods.prd_ots_c_task_data_push
) x1 on x1.uuid = x.pn_no
join (
    select
        distinct id,batch_number,channel
    from ods.prd_ots_ots_task
    where pday='${yesterday_p}'
      and channel = 'push'
      and batch_number like '%__%'
) x2 on x2.id = x1.task_id
left join(
    select
        distinct msg_id, registration_id, callback_type, push_status_time as reach_time
    from ods.prd_gns_push_push_notice_callback
    where pday='${yesterday_p}'
) y on x.msg_id = y.msg_id and x.registration_id = y.registration_id