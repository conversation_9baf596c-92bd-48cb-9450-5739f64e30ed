INSERT
overwrite TABLE credit_data.ivr_black_release_summary_monthly_sync
SELECT 
   CONCAT(SUBSTRING(pmonth, 1, 4), '-', SUBSTRING(pmonth, 5, 2)) AS stat_day,
    release_rule_name,
    release_count,
    call_person_count,
    call_answer_person_count,
    call_answer_person_items,
    call_duration,
    call_person_items,
    call_complaints,
    loan_person_count,
    loan_amount
FROM 
    credit_data.ivr_black_release_summary_monthly 
WHERE pmonth  >=subString('${yesterday_p}',1,6)
-- where  pmonth>''