INSERT
overwrite TABLE credit_data.ivr_black_release_summary_monthly PARTITION(pmonth =  '${yestermonth_p}')
SELECT 
    r_user.release_rule_name as release_rule_name,
    COUNT(DISTINCT r_user.user_no) AS release_count,
    COUNT(DISTINCT call_record.userno) AS call_person_count,
    COUNT(call_du.user_no) AS call_answer_person_count,
    COUNT(DISTINCT call_du.user_no) AS call_answer_person_items,
    COALESCE(SUM(call_du.call_duration), 0) AS call_duration,
    COUNT(call_record.userno) AS call_person_items,
    COUNT(DISTINCT complain.user_no) AS call_complaints,
    COUNT(DISTINCT loan.user_no) AS loan_person_count,
    COALESCE(SUM(loan.loan_amt), 0)  AS loan_amount
FROM 
    credit_data.ivr_batch_release_rule_user_month r_user
LEFT JOIN (
    SELECT * 
    FROM ods.prd_kafka_sync_fluidics_voice_flow_control_project_proc_hourly 
    WHERE pday >= '${yestermonth_p}'
) call_record
    ON r_user.user_no = call_record.userno 
    AND r_user.pmonth = SUBSTRING(call_record.pday, 1, 6)
LEFT JOIN (
    SELECT user_no, dt, loan_amt 
    FROM safe3_copy.yunying_dz_all_detail_coe_pda 
    WHERE dt >= '${yesterday0_p}'
) loan
    ON r_user.user_no = loan.user_no 
    AND r_user.pmonth = DATE_FORMAT(loan.dt, 'yyyyMM')
LEFT JOIN (
select call_date, user_no from fin_ads.ads_o_vos_supplier_complaint_a  where call_date >='2025-06-01'  
) complain
    ON r_user.user_no = complain.user_no 
    AND r_user.pmonth = DATE_FORMAT(complain.call_date, 'yyyyMM')
LEFT JOIN (
    SELECT userno as user_no, variableduration as call_duration, pday
    FROM fin_dw.dwd_coll_outbound_call_result_pdi
    WHERE time_through IS NOT NULL
      AND time_through != ''
      AND pday >= '${yestermonth_p}'

    UNION ALL

    SELECT userno as user_no, calledvariableduration as call_duration, pday
    FROM hdp_credit.prd_mongo_admin_traffic_call_seats_detail
    WHERE calldirection = 'SEATS_OUTBOUND'
      AND calledthroughtime IS NOT NULL
      AND calledthroughtime != ''
      AND pday >= '${yestermonth_p}'
) call_du
    ON call_du.user_no = r_user.user_no 
    AND r_user.pmonth = SUBSTRING(call_du.pday, 1, 6)
WHERE r_user.pmonth = '${yestermonth_p}'
GROUP BY r_user.pmonth, r_user.release_rule_name
