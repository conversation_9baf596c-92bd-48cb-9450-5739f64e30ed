
-- 打标
-- 由于gns/ctp,等外部存在物理删数据的问题,只能全量打标
INSERT overwrite TABLE credit_data.prd_level_black_sms_tag_data PARTITION(pday = '${yesterday_p}')

-- gns 这个是状态表,mobile分组大于1的就只有2条
select a.level,
    b.user_no,
    substring(a.operate_time,0,19) as operate_time,
    'GNS' as system_code,
    "Y" as operate_status,
    'black_010' as black_type,
    '' as user_attitude_type,
    'jietiao' as business_source,
    '借条GNS短信' as data_source
    from (
    select 
        case when date_updated < '2024-09-06 00:00:00' then 'P0' -- 历史数据默认为P0 
             when type in ('1','2','3','7','9') then 'P2'
             when type in ('5') then 'P0'
             else 'P2'
        end as level,
        mobile_no_md5x,
        date_updated as operate_time -- 这里精确到年月日,之前的是只取年月日
        from(                    
            select * ,
                row_number() over(partition by mobile_no_md5x order by date_updated desc) as rn
            from ods.prd_gns_im_sms_blacklist
            having rn=1
        )
        where (
            (type is not NULL and (type IN ('7','5') OR (type in ('2', '3', '9', '1') AND date_updated >= '2024-04-01 00:00:00')))
            OR (type is NULL and date_updated >= '2023-01-01 00:00:00')
        )
        and status='1'
    )a
inner join(
    select * from ods.prd_cis_u_user
    where user_state='1'
)b
on a.mobile_no_md5x=b.mobile_no_md5x

union all

-- ctp 这个表都是加黑数据
select level,
    b.user_no,
    substring(a.operate_time,0,19) as operate_time,
    'CTP' as system_code,
    "Y" as operate_status,
    'black_009' as black_type,
    '' as user_attitude_type,
    'jietiao' as business_source,
    '上海触达回复T' as data_source
    from (
    select 
        case when update_time_stamp < 1725552000000 then 'P0'
        else "P2" 
        end as level,
            mobile_no as mobile_no_md5x,-- 以这个为准
            from_unixtime(update_time_stamp / 1000,'yyyy-MM-dd HH:mm:ss') as operate_time
    from ods.prd_ctp_app_c_task_black_list
    where channel = 'sms' 
        and subject in ('JIETIAO','')
        -- 这里变更为,update_time_stamp
        and from_unixtime(update_time_stamp / 1000, 'yyyy-MM-dd HH:mm:ss') >= '2024-04-01 00:00:00'
)a
inner join(
        select * from ods.prd_cis_u_user
        where user_state='1'
)b
on a.mobile_no_md5x=b.mobile_no_md5x
where a.operate_time is not null
    

