
-- 历史差异打标
INSERT overwrite TABLE credit_data.prd_level_black_his_dif_tag_data PARTITION(pday = '${yesterday_p}')

select 
    a.level,
    a.user_no,
    substring(a.operate_time,0,19) as operate_time,
    a.system_code,
    a.operate_status,
    replace(a.black_type,'"','') as black_type,
    a.user_attitude_type,
    a.business_source,
    a.data_source
 from (
       -- redis 差异,增量部分
       -- credit_data.sls_global_ivr_blacklist_redis
       select 
            level,
            a.user_no,
            operate_time,
            'HIS_DIFF_INC' as system_code,
            "Y" as operate_status,
            blacktypeList as black_type,
            user_attitude_type,
            'jietiao' as business_source,
            dataSource as data_source
        from (
            select 
                case when slevel is null or slevel='' then 'P0'
                else slevel
                end as level,
                user_no,
                dateUpdated as operate_time,
                replace(blacktypeList,']','') as blacktypeList,
                dataSource,
                user_attitude_type
                from (
                    select 
                            slevel
                            , user_no
                            , flagLossStatus
                            , dateUpdated
                            ,replace(blacktypeList,'[','') as blacktypeList
                            ,dataSource
                            ,user_attitude_type
                            , row_number() over(partition by user_no order by dateUpdated desc) as rn
                        from (
                            select 
                                    cache_key as user_no,
                                    get_json_object(cache_value,'$.flagLossStatus') as flagLossStatus,
                                    get_json_object(cache_value,'$.dateUpdated') as dateUpdated,
                                     get_json_object(cache_value,'$.blacktypeList') as blacktypeList,
                                     get_json_object(cache_value,'$.user_attitude_type') as user_attitude_type,
                                     get_json_object(cache_value,'$.dataSource') as dataSource,
                                    get_json_object(cache_value,'$.level') as slevel
                            -- scan 扫描可能导致重复数据
                            from (
                                SELECT DISTINCT cache_key,cache_value 
                                from credit_data.sls_global_ivr_blacklist_redis 
                                where pday='${last2Days_p}'  -- T-2分区
                            )
                            where get_json_object(cache_value,'$.dateUpdated') is null 
                                    or get_json_object(cache_value,'$.dateUpdated') < '${today}'
                        )
                        having rn=1
            ) 
            where flagLossStatus='Y'
       )a
       left join (
            SELECT * from credit_data.prd_jietiao_global_ivr_blacklist_merge
            where pday='${last2Days_p}'  -- T-2分区 
            and user_no like 'UR%' AND flag_loss_status = 'Y'
       )b 
       on a.user_no = b.user_no where b.user_no is null

    union all 

    select level ,
        user_no ,
        operate_time ,
        system_code ,
        operate_status,
        black_type,
        user_attitude_type,
        business_source,
        data_source
        from credit_data.prd_level_black_his_dif_tag_data
    where pday='${last2Days_p}'
)a ;