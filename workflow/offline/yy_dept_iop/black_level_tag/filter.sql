
-- 真正需要用来计算的标签数据
INSERT overwrite TABLE credit_data.prd_level_black_tag_filter PARTITION(pday = '${yesterday_p}')

select 
    a.level,
    a.user_no,
    a.operate_time,
    a.system_code,
    a.operate_status,
    a.black_type,
    a.user_attitude_type,
    a.business_source,
    a.data_source
    from (
    -- 计算全量
    select * from credit_data.prd_level_black_ivr_tag_data
    where pday ='${yesterday_p}'

    union all 

    select * from credit_data.prd_level_black_sms_tag_data
    where pday = '${yesterday_p}'

    union all 

    select * from credit_data.prd_level_black_his_dif_tag_data
    where pday = '${yesterday_p}'
)a 
left join(
    -- 每天分区是全量
    select *,
        row_number() over(partition by user_no order by operate_time desc) as rn
        from credit_data.prd_ivr_blacklist_removal_record 
    where pday='${yesterday_p}'
    having rn =1
) b 
on a.user_no=b.user_no
where b.user_no is null or (a.operate_time>b.operate_time)
