
INSERT overwrite TABLE credit_data.prd_level_black_calc_result_inc PARTITION(pday = '${yesterday_p}')

select
    a.level,
    a.user_no,
    a.date_updated,
    a.system_code,
    a.operate_status,
    a.black_type,
    a.user_attitude_type,
    a.business_source,
    a.data_source,
    a.release_time,
   'OFFLINE_ADD' as operate_type
 from (
       select * from credit_data.prd_level_black_calc_result
    where pday = '${yesterday_p}'
)a
left join(
        select * from credit_data.prd_level_black_calc_result
    where pday = '${last2Days_p}'
)b 
on a.user_no=b.user_no
where b.user_no is null or (a.release_time!=b.release_time or a.level!=b.level)