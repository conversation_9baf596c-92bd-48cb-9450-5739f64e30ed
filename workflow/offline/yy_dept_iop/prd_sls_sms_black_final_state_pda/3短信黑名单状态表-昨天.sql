-- 依赖 yy_dept_online_data_app_task->sls_global_sms_blacklist_redis
-- dataSource 需要处理,因为0906上线导致的
-- 总领提纲:和redis保持一致,需要做的是,做好映射
-- dataSource格式统一一下
-- dateupdated 格式统一一下 --OK
-- redis中没有 systemCode 字段。
-- 黑名单释放时间要大于等于当天才行
-- INSERT overwrite TABLE credit_data.test_sls_sms_black_final_state_last_pda PARTITION(pday = '${yesterday_p}')
INSERT overwrite TABLE credit_data.prd_sls_sms_black_final_state_last_pda PARTITION(pday = '${yesterday_p}')

-- 以 14号为例
-- 1、下面是 11-12号(含)之前的所有数据,完全不含11-13号的任何一条数据,因为用了date_updated或release_time进行限制
select
    mobile_no_md5x,
    status,
    case WHEN length(dateUpdated)=10 then
        concat(dateUpdated,' 00:00:00')
    else dateUpdated
    end as date_updated,
    sms_type,
    'JIETIAO' as business_source
from (
    select
    split(cache_key,'_')[1] as mobile_no_md5x,
    get_json_object(cache_value, '$.status')                        AS status,
    get_json_object(cache_value, '$.date_updated')                   AS dateUpdated,
    get_json_object(cache_value, '$.sms_type')                      AS sms_type
    from(
        SELECT DISTINCT cache_key,cache_value
        from credit_data.sls_global_sms_blacklist_redis
        where pday='${last2Days_p}'
    )
     where (get_json_object(cache_value,'$.date_updated') is null or get_json_object(cache_value,'$.date_updated') < '${last2Days}')
)

UNION all

-- 当天的7点扫描,能获取到redis的全量数据。
-- 2、下面这个是14号造上7点扫描得到,排除完14号后，13号(含)的所有数据。
select 
    mobile_no_md5x,
    status,
    date_updated,
    sms_type,
    business_source
 from credit_data.prd_sls_sms_black_final_state_full_pda
 where pday = '${yesterday_p}'
  and (date_updated is not null and date_updated < '${today}' and date_updated > '${last2Days}')