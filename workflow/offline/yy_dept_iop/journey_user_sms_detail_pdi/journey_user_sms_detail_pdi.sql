INSERT overwrite TABLE credit_data.journey_user_sms_detail_pdi PARTITION(pday = '${yesterday_p}')
SELECT
    COALESCE(reh.batch_number, '') AS batch_number
     ,reh.user_no
     ,reh.send_time
     ,if((length(split(reh.batch_number,'_')[0]) <= 7 and RLIKE (split(reh.batch_number,'_')[0],'^[0-9]+') = 1) or RLIKE (split(reh.batch_number,'_')[0],'^[S]+') = 1
    ,split(reh.batch_number,'_')[0]
    ,''
    ) as strategy_id
     ,reh.sms_template
     ,IF(reh.reach_status = 1, 1, 2) as reach_status
     ,reh.send_channel
     ,case when reh.reach_status=1 then ''
           when reh.has_reback=1 and reh.reach_status=0 then 'sendFail'
           when reh.has_reback=0 and xwd_report.is_limit='1' then xwd_report.push_type
           when reh.has_reback=0 and reh.reach_filter_type<>'' then reh.reach_filter_type
           else 'other'
    end as fail_reason
FROM
    (
        SELECT send.sms_template AS sms_template
             ,send.send_time AS send_time
             ,send.mobile_no_md5x AS mobile_no_md5x
             ,user.user_no AS user_no
             ,IF(t.batch_number != '', t.batch_number, task.batch_number1) AS batch_number
             ,COALESCE(back.reach_status, 2) AS reach_status
             ,back.reach_time AS reach_time
             ,IF(back.mobile_no_md5x != '', 1, 0) AS has_reback
             ,task.task_id as task_id
             ,'gns' AS send_channel
             ,'' as reach_filter_type
        FROM
            (
                SELECT
                      biz_no
                     ,biz_type
                     ,sms_template
                     ,third_no
                     ,state
                     ,result
                     ,send_time
                     ,sms_type
                     ,mobile_no_md5x
                FROM (
                    SELECT  *, row_number() over(PARTITION BY id ORDER BY date_updated DESC) as row_num
                    FROM ods.prd_gns_ng_sms_info
                    WHERE pday='${yesterday_p}'
                    ) gns_sms_info
                WHERE row_num = 1
            ) AS send
             LEFT JOIN
            (
                SELECT batch_number
                     ,IF(batch_number like 'S%__TRR%', concat(split(batch_number,'_')[0], '_1', date_format(current_timestamp(), 'yyyyMMdd')), regexp_extract(batch_number, '([^_]+\\_[^_]+)', 1) ) AS batch_number1
                     ,uuid,task_id
                FROM (
                    SELECT  *, row_number() over(PARTITION BY batch_number, user_no, uuid ORDER BY update_time_stamp DESC) as row_num
                    FROM ods.prd_ctp_app_c_task_data_sms
                    WHERE pday='${yesterday_p}'
                ) task_data_sms
                WHERE row_num = 1
            ) AS task ON send.biz_no = task.uuid
             LEFT JOIN
            (
                SELECT
                    third_no
                     ,mobile_no_md5x
                     ,reach_status
                     ,reach_time
                FROM (
                    SELECT
                    *
                    ,row_number() over(PARTITION BY third_no, mobile_no_md5x ORDER BY reach_status DESC, notify_time DESC) as row_num
                    from(
                            select
                            *
                           ,IF(report IN ('DELIVRD', 'SUCCESS', '0', ':DELIVRD'), 1, 0) AS reach_status
                           ,COALESCE(notify_time, date_created) AS reach_time
                            FROM ods.prd_gns_im_sms_report
                            WHERE pday='${yesterday_p}'
                        )
                    ) im_sms_report
                WHERE row_num = 1
            ) AS back ON send.third_no = back.third_no AND send.mobile_no_md5x = back.mobile_no_md5x
                LEFT JOIN
            (
                SELECT DISTINCT sub_batch_no as child_batch_number, batch_number from fin_dw.dwd_opr_strategy_restrict_pdi where pday='${yesterday_p}'
            ) AS t ON task.batch_number = t.child_batch_number
             left join ods.prd_cis_u_user user on send.mobile_no_md5x = user.mobile_no_md5x

        UNION ALL

        SELECT send.sms_template AS sms_template
             ,send.send_time AS send_time
             ,send.mobile_no_md5x AS mobile_no_md5x
             ,send.user_no AS user_no
             ,send.batch_number AS batch_number
             ,COALESCE(back.reach_status, 2) AS reach_status
             ,back.reach_time AS reach_time
             ,IF(back.mobile_no_md5x != '', 1, 0) AS has_reback
             ,send.task_id as task_id
             ,'ctp' AS send_channel
             ,send.reach_filter_type as reach_filter_type
        FROM
            (
                select
                    *
                from
                    (
                        select
                            distinct
                            id
                                   ,template_code AS sms_template
                                   ,batch_number
                        from ods.prd_ctp_app_c_task
                        where pday='${yesterday_p}'
                    ) a
                        inner join
                    (
                        SELECT DISTINCT
                            user_no,
                            mobile_no_md5x,
                            task_id,
                            sub_batch_number,
                            send_time
                                      ,reach_filter_type
                        from (
                                 select
                                     user_no,
                                     mobile_no_md5x,
                                     task_id,
                                     batch_number as sub_batch_number,
                                     from_unixtime(cast(create_time_stamp / 1000 as bigint)) AS send_time
                                      ,'' AS reach_filter_type
                                 from ods.prd_ctp_app_c_task_data_sms
                                 where pday='${yesterday_p}'

                                 union all

                                 select
                                     user_no,
                                     mobile_no_md5x,
                                     task_id,
                                     '' as sub_batch_number,
                                     from_unixtime(cast(create_time_stamp / 1000 as bigint)) AS send_time
                                      ,'400' AS reach_filter_type
                                 from ods.prd_ctp_app_c_task_variable_unlegal
                                 where pday='${yesterday_p}'

                                 union all

                                 select
                                     user_no,
                                     mobile_no_md5x,
                                     task_id,
                                     batch_number as sub_batch_number,
                                     from_unixtime(cast(create_time_stamp / 1000 as bigint)) AS send_time
                                      ,cast(type AS String) as reach_filter_type
                                 from ods.prd_ctp_app_c_task_unlegal
                                 where pday='${yesterday_p}'

                                 union all

                                 select
                                     user_no,
                                     mobile_md5x as mobile_no_md5x,
                                     task_id,
                                     '' as sub_batch_number,
                                     from_unixtime(cast(create_time_stamp / 1000 as bigint)) AS send_time
                                      ,IF(status = '2', '300', '') AS reach_filter_type
                                 from ods.prd_ctp_app_c_task_data_sms_black
                                 where pday='${yesterday_p}'
                             )

                    ) b on a.id = b.task_id
            ) AS send
            LEFT JOIN
            (
                select
                      mobile_no_md5x
                     ,batch_number as sub_batch_number
                     ,reach_status
                     ,from_unixtime(cast(create_time_stamp / 1000 as bigint)) AS reach_time
                FROM (
                    SELECT  *, row_number() over(PARTITION BY mobile_no_md5x, batch_number ORDER BY reach_status DESC, create_time_stamp DESC) as row_num
                        from(
                            select *,mobile as mobile_no_md5x,IF(status = 1, 1, 0) AS reach_status
                            FROM ods.prd_ctp_app_c_task_data_call_back_sms
                            WHERE pday='${yesterday_p}'
                        )
                    ) call_back_sms
                WHERE row_num = 1
            ) AS back ON send.sub_batch_number = back.sub_batch_number AND send.mobile_no_md5x = back.mobile_no_md5x
    ) AS reh
    LEFT JOIN (
        select is_limit,push_type,mobile,task_id from ods.prd_kafka_sms_holdup_reason where pday='${yesterday_p}'
    ) xwd_report
ON reh.mobile_no_md5x = xwd_report.mobile AND cast(reh.task_id AS String) = xwd_report.task_id