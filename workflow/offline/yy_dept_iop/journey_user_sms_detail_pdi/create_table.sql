CREATE TABLE IF NOT EXISTS credit_data.journey_user_sms_detail_pdi (
   `batch_number` STRING COMMENT '批次号'
    ,`user_no` STRING COMMENT '用户号'
    ,`event_time` STRING COMMENT '行为时间'
    ,`strategy_id` STRING COMMENT '策略id'
    ,`template_id` STRING COMMENT '模板id'
    ,`reach_status` BIGINT COMMENT '触达状态'
    ,`send_channel` STRING COMMENT '发送平台'
    ,`fail_reason` STRING COMMENT '失败原因'
)COMMENT '用户旅程短信明细表'
PARTITIONED BY (pday STRING COMMENT '按日分区')
STORED AS ORC;