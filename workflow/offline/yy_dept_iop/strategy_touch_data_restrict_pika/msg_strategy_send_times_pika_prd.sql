alter table credit_data.msg_strategy_send_times_pika_prd drop  if exists partition(pday='${yesterday_p}');

insert overwrite table credit_data.msg_strategy_send_times_pika_prd partition(pday='${yesterday_p}')
select 
tab30.user_no,
tab30.strategy_no,
tab30.channel_type,
COALESCE(tab3.send_times_3,0) as send_times_3,
COALESCE(tab5.send_times_5,0) as send_times_5,
COALESCE(tab7.send_times_7,0) as send_times_7,
COALESCE(tab15.send_times_15,0) as send_times_15,
COALESCE(tab30.send_times_30,0) as send_times_30,
(case 
    when tab3.max_reach_fish_time is not null then tab3.max_reach_fish_time
	when tab5.max_reach_fish_time is not null then tab5.max_reach_fish_time
	when tab7.max_reach_fish_time is not null then tab7.max_reach_fish_time
	when tab15.max_reach_fish_time is not null then tab15.max_reach_fish_time
	when tab30.max_reach_fish_time is not null then tab30.max_reach_fish_time
end) as max_reach_fish_time
FROM 
(
select 
t.user_no,
t.strategy_no,
t.channel_type,
count(t.user_no) as send_times_30,
max(t.reach_fish_time) as max_reach_fish_time
FROM credit_data.msg_strategy_send_times_data_prd t
join (select strategy_no from ods.prd_ipss_isc_strategy where status in (5,7) union select distinct strategy_no from ods.prd_ipss_isc_strategy_sync where sync_date >=  date_sub(current_date(),1)) d ON t.strategy_no = d.strategy_no
where pday > replace(date_sub(current_date,30),'-','') group by t.user_no,t.strategy_no,t.channel_type
) tab30 
left join (
select 
t.user_no,
t.strategy_no,
t.channel_type,
count(t.user_no) as send_times_15,
max(t.reach_fish_time) as max_reach_fish_time
FROM credit_data.msg_strategy_send_times_data_prd t
join (select strategy_no from ods.prd_ipss_isc_strategy where status in (5,7) union select distinct strategy_no from ods.prd_ipss_isc_strategy_sync where sync_date >=  date_sub(current_date(),1)) d ON t.strategy_no = d.strategy_no
where pday > replace(date_sub(current_date,15),'-','') group by t.user_no,t.strategy_no,t.channel_type
) tab15 ON tab15.user_no = tab30.user_no AND tab15.strategy_no = tab30.strategy_no AND tab15.channel_type = tab30.channel_type
left join ( 
select 
t.user_no,
t.strategy_no,
t.channel_type,
count(t.user_no) as send_times_7,
max(t.reach_fish_time) as max_reach_fish_time
FROM credit_data.msg_strategy_send_times_data_prd t
join (select strategy_no from ods.prd_ipss_isc_strategy where status in (5,7) union select distinct strategy_no from ods.prd_ipss_isc_strategy_sync where sync_date >=  date_sub(current_date(),1)) d ON t.strategy_no = d.strategy_no
where pday > replace(date_sub(current_date,7),'-','') group by t.user_no,t.strategy_no,t.channel_type
) tab7 ON tab7.user_no = tab30.user_no AND tab7.strategy_no = tab30.strategy_no AND tab7.channel_type = tab30.channel_type
left join (
select 
t.user_no,
t.strategy_no,
t.channel_type,
count(t.user_no) as send_times_5,
max(t.reach_fish_time) as max_reach_fish_time
FROM credit_data.msg_strategy_send_times_data_prd t
join (select strategy_no from ods.prd_ipss_isc_strategy where status in (5,7) union select distinct strategy_no from ods.prd_ipss_isc_strategy_sync where sync_date >=  date_sub(current_date(),1)) d ON t.strategy_no = d.strategy_no
where pday > replace(date_sub(current_date,5),'-','') group by t.user_no,t.strategy_no,t.channel_type
) tab5 ON tab5.user_no = tab30.user_no AND tab5.strategy_no = tab30.strategy_no AND tab5.channel_type = tab30.channel_type
left join (
select 
t.user_no,
t.strategy_no,
t.channel_type,
count(t.user_no) as send_times_3,
max(t.reach_fish_time) as max_reach_fish_time
FROM credit_data.msg_strategy_send_times_data_prd t
join (select strategy_no from ods.prd_ipss_isc_strategy where status in (5,7) union select distinct strategy_no from ods.prd_ipss_isc_strategy_sync where sync_date >=  date_sub(current_date(),1)) d ON t.strategy_no = d.strategy_no
where pday > replace(date_sub(current_date,3),'-','') group by t.user_no,t.strategy_no,t.channel_type
) tab3 ON tab3.user_no = tab30.user_no AND tab3.strategy_no = tab30.strategy_no AND tab3.channel_type = tab30.channel_type;