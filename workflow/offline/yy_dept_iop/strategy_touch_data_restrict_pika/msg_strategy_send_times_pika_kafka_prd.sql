drop table if exists  credit_data.msg_strategy_send_times_pika_kafka_prd;
create table credit_data.msg_strategy_send_times_pika_kafka_prd ROW FORMAT DELIMITED FIELDS TERMINATED BY '\t' COLLECTION ITEMS TERMINATED BY '\t'
 stored as textfile as select 
concat('IPSS:STR_LIMIT',strategy_no,channel_type,user_no) as pika_key,
concat('{\"max_reach_fish_time\":',max_reach_fish_time,',\"send_times_3\":',send_times_3,',\"send_times_5\":',send_times_5,',\"send_times_7\":',send_times_7,',\"send_times_15\":',send_times_15,',\"send_times_30\":',send_times_30,'}') as pika_value
from 
credit_data.msg_strategy_send_times_pika_prd where pday ='${yesterday_p}';