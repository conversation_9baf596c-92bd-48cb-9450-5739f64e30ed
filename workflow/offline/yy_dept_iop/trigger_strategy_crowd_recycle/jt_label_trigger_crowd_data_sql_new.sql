insert overwrite table credit_data.jt_label_trigger_crowd_data partition(pday = '${yesterday_p}')
select
    user_no,
    concat_ws(',',collect_set(new_strategy_no)) as strategy_no,
    concat_ws(',',collect_set(new_batch_number)) as batch_number
from (
        select
        user_no,new_strategy_no,new_batch_number
    from (
        select user_no,strategy_no,batch_number
        from credit_data.jt_label_trigger_crowd_data
        where pday = '${yesterday_p}'
    )lateral view explode(split(strategy_no,',')) strategy_no as new_strategy_no
    lateral view explode(split(batch_number,',')) batch_number as new_batch_number
    union all
    select
        user_no,new_strategy_no,new_batch_number
    from (
        select user_no,strategy_no,batch_number
        from credit_data.jt_label_trigger_crowd_data
        where pday = '${yesterday_p}'
    )lateral view explode(split(strategy_no,',')) strategy_no as new_strategy_no
    lateral view explode(split(batch_number,',')) batch_number as new_batch_number
    union all
        	select user_no , strategy_no,batch_number from (
			select
                json_tuple(replace(replace(replace(content,'\\"','"'),'"{','{'),'}"','}'),'body') as body,
				json_tuple(body,'conditionType') as conditionType
                from ods.prd_kafka_sync_digital_strategy_touch_result_hourly
                where pday = '${yesterday_p}'
) t
				lateral view outer json_tuple(body,'userList','strategyNo','batchNumber','conditionType') t2 as userList,strategy_no,batch_number,conditonType
				LATERAL view explode(json_array(userList)) t3 as userNoList
          		lateral view outer json_tuple(userNoList,'userNoList','userType') t4 as userno_list,userType
				lateral view explode(split(replace(substring(userno_list,2,length(userno_list)-2),'"',''),',')) userlist as user_no
        where conditionType=3
    union all
    select  user_no ,strategy_no,batch_no as batch_number
    from fin_dw.dwd_strategy_channel_filter cf ,fin_dim.dim_o_opr_isc_canvas_jw_a ja
    where ja.person_num = cf.crowd_no and ja.condition_type = 3   and cf.pday = '${yesterday_p}'

) group by user_no
