#!/bin/bash
# file name     : deleteLogs.sh
# author        : lijiacheng
# version       : v1.0
# date          : 2020-09-24
# description   : 项目名称: trigger_strategy_crowd_recycle 触发式策略回收
set -x
scriptDir=/hdpData1/hdp-jinrong-loan/workdir/j-wangshaowu-jk/_360_jinrong_realtime_label
triggered_strategy_path=/hdpData1/hdp-jinrong-loan/workdir/j-wangshaowu-jk/_360_jinrong_triggered_strategy
monitor_path=/hdpData1/hdp-jinrong-loan/workdir/j-wangshaowu-jk/_360_jinrong_realtime_label/monitor
remain_amount_to_redis_path=/hdpData1/hdp-jinrong-loan/workdir/j-wangshaowu-jk/_360_jinrong_realtime_label/remain_amount_to_redis

datadate=$1

if ! test $datadate;then
     datadate=$(/bin/date -d "-3day" +"%Y%m%d")
fi


rm -f ${scriptDir}/logs/${datadate}*.log
rm -f ${triggered_strategy_path}/logs/${datadate}*.log
rm -f ${monitor_path}/logs/${datadate}*.log
rm -f ${remain_amount_to_redis_path}/logs/${datadate}*.log
