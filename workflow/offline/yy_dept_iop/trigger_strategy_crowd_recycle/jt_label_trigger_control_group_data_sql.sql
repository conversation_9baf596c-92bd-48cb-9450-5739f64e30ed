insert overwrite table credit_data.jt_label_trigger_control_group_data partition(pday = '${yesterday_p}')
select
    user_no,
    concat_ws(',',collect_set(new_strategy_no)) as strategy_no,
    concat_ws(',',collect_set(new_batch_number)) as batch_number,
    concat_ws(',',collect_set(batch_number_type)) as batch_number_type
from (
    select
        user_no,new_strategy_no,new_batch_number,concat(new_batch_number,'#','留白') as batch_number_type
    from (
        select user_no,strategy_no,batch_number
        from credit_data.jt_label_trigger_control_group_data
        where pday = '${yesterday_p}'
    )lateral view explode(split(strategy_no,',')) strategy_no as new_strategy_no
    lateral view explode(split(batch_number,',')) batch_number as new_batch_number

    union all

    select
        user_no,
        strategy_no,
        batch_number,
        batch_number_type
    from (
        select
            x.user_no,
            x.strategy_no,
            concat(x.strategy_no,'_',task_id) as batch_number,
            concat(x.strategy_no,'_',task_id,'#',if(x.white_flag = 'true','留白',if(group_white_flag='true','组内对照','客群'))) as batch_number_type
        from (
            select
                DISTINCT concat(get_json_object(body, '$.channelType'),pday) as task_id,
                get_json_object(body, '$.userNo') as user_no,
                get_json_object(body, '$.strategyNo') as strategy_no,
                get_json_object(body, '$.compCrowdNo') as compCrowd_no,
                get_json_object(body, '$.whiteFlag') as white_flag,
                get_json_object(body, '$.groupWhiteFlag') as group_white_flag,
		  		get_json_object(body, '$.compCrowdFlag') as compCrowdFlag
            from (
                select
					pday,
                    get_json_object(replace(replace(replace(content,'\\"','"'),'"{','{'),'}"','}'),'$.body') as body
                from ods.prd_digital_trigger_strategy_comp_crowd_info_hourly
                where pday = '${yesterday_p}' and content like '%UR%'
            )
        ) x
        left join (
            select
                strategy_no,
                crowd_no,
                1 as flag
            from ods.prd_ipss_isc_strategy_crowd
            where comp_crowd_type in ('0','3','4')
            union all

            select
                strategy_no,
		      -- 选择事件作为对比组时，x.compCrowdNo 使用当前节点人群包编号
              --  comp_crowd_no,
		      --  crowd_no,
			    if(comp_sub_type='1',comp_crowd_no,crowd_no),
                2 as flag
            from ods.prd_ipss_isc_strategy_crowd
            where comp_crowd_type in ('1','4')
             union all

            select
                strategy_no,
                crowd_no,
                3 as flag
            from ods.prd_ipss_isc_strategy_crowd
            where comp_crowd_type ='4'
        ) y
        on x.strategy_no = y.strategy_no and x.compCrowd_no = y.crowd_no
        where case when y.flag = 1  and ((x.white_flag = 'true' or x.group_white_flag = 'true') and x.compCrowdFlag <>'true')  then 'Y'
                 when y.flag = 2 and ((x.white_flag = 'false' and x.group_white_flag = 'false') and x.compCrowdFlag='true') then 'Y'
                 when y.flag = 3 and (x.white_flag ='false' and x.group_white_flag = 'false' and x.compCrowdFlag='true') then 'Y' else null
                 end  = 'Y'
    )
) group by user_no