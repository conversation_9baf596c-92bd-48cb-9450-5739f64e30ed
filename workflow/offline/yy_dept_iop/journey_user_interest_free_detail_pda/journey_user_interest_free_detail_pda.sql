INSERT overwrite TABLE credit_data.journey_user_interest_free_detail_pda PARTITION(pday = '${yesterday_p}')
select
    DISTINCT
    a.busi_code
    ,a.user_no
    ,substring(a.date_updated,1,19) as event_time
    ,if((length(split(a.busi_code,'_')[0]) <= 7 and rlike (split(a.busi_code,'_')[0],'^[0-9]+') = 1) or rlike (split(a.busi_code,'_')[0],'^[S]+') = 1
    ,split(a.busi_code,'_')[0]
    ,''
    ) as strategy_id
   ,a.quota_code as template_id
   ,case when b.id is not null then 1
    else 0 end as interest_free_state
   ,substring(a.end_date,1,10) as interest_free_valid_date
from  ods.prd_mms_user_quota_record a
          left join ods.prd_mms_user_quota_use_record b on a.quota_no=b.quota_no
where a.is_delete = '0'
  and a.busi_code not like 'ACT%'