INSERT
overwrite TABLE credit_data.yushu_fluidics_voice_flow_control_project_proc_hourly PARTITION(pday =  '${yesterday_p}')
select a.userno,
       a.touchmode,
       a.sendtime,
       a.businesstype,
       a.systemid,
       a.templatecode,
       a.requestno,
       a.ruleno,
       a.islimit,
       a.pushtype,
       a.configlimitsize,
       a.configlimitday,
       a.nolimitreason,
       a.batchnumber,
       a.callid,
       a.requesttype,
       a.salvagesendscene,
       a.blacktype,
       a.blacktypelist,
       a.salvageeventid,
       a.ruletype,
       a.salvageruleid,
       a.ivrtype,
       a.businesseventid,
       a.businesssendscene,
       a.businessruleid,
       a.globalruleid,
       a.overclock_result,
       a.overclock_event_id,
       a.overclock_rule_id,
       a.ext_para,
       a.gray_level,
       a.gray_rule_no,
       a.isprequery,
       a.white_template_rule_id,  
       b.requesttype  as b_request_type,
	   a.model_result as model_result
	   from
    (SELECT
         *
     FROM ods.prd_kafka_sync_fluidics_voice_flow_control_project_proc_hourly
     WHERE pday = '${yesterday_p}' ) a
        left join (SELECT
                       *
                   FROM ods.prd_kafka_sync_fluidics_voice_flow_control_project_proc_hourly
                   WHERE pday = '${yesterday_p}'  and requesttype ='reduce') b
                  on a.userno =b.userno and a.callid=b.callid and a.requesttype = 'add'