drop table if exists `credit_data`.`s3_temp_offline_resource_crowdrule`;
create table if not exists `credit_data`.`s3_temp_offline_resource_crowdrule`(
  `user_no` string comment '用户编号',
  `rule_no` string comment '人群编号'
)
ROW FORMAT DELIMITED
FIELDS TERMINATED BY '\t'
COLLECTION ITEMS TERMINATED BY '\t'
MAP KEYS TERMINATED BY ':'
STORED AS textfile
LOCATION 'hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/doris_temp_resource_crowd_data/defined_v2/${today_p}';


-- drop table if exists `credit_data`.`isc_temp_offline_resource_crowdrule`;
-- create table if not exists `credit_data`.`isc_temp_offline_resource_crowdrule`(
--   `user_no` string comment '用户编号',
--   `rule_no` string comment '人群编号'
-- )
-- ROW FORMAT DELIMITED
-- FIELDS TERMINATED BY '\t'
-- COLLECTION ITEMS TERMINATED BY '\t'
-- MAP KEYS TERMINATED BY ':'
-- STORED AS textfile;


drop table if exists `credit_data`.`resource_crowd_data_check`;
create table if not exists `credit_data`.`resource_crowd_data_check`(
  `user_no` string comment '用户编号'
)
PARTITIONED BY (
  `pday` string COMMENT '分区', `rule_no` string comment '人群编号')
STORED AS textfile
;
