-- drop table if  exists `credit_data`.`sls_ivr_level_blacklist_release_pdi`;
CREATE TABLE IF NOT EXISTS `credit_data`.`sls_ivr_level_blacklist_release_pdi` (
  `black_no` string COMMENT '黑名单唯一键',
  `user_no` string COMMENT '用户号',
  `level` string COMMENT '黑名单等级',
  `level_add_time` string COMMENT '黑名单等级加黑时间',
  `business_source` string COMMENT '业务类型',
  `system_code` string COMMENT '系统code',
  `black_period` string COMMENT '黑名单有效期',
  `gray_period` string COMMENT '灰名单有效期',
  `expected_release_time` string COMMENT '期望释放时间',
  `release_time` string COMMENT '释放时间',
  `user_attitude_type` string COMMENT '用户态度',
  `date_updated` string COMMENT '操作时间'
) PARTITIONED BY (`pday` string, `operate_type` string) STORED AS ORC;