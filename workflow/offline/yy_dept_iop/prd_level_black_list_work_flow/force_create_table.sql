-- drop table if  exists `credit_data`.`sls_ivr_level_blacklist_add_pdi`;
CREATE TABLE IF NOT EXISTS `credit_data`.`sls_ivr_level_blacklist_add_pdi` (
  `user_no` string COMMENT '用户号',
  `black_type` string COMMENT '黑名单类型',
  `data_source` string COMMENT '数据来源',
  `operate_user` string COMMENT '操作人',
  `business_source` string COMMENT '业务类型',
  `system_code` string COMMENT '系统code',
  `user_attitude_type` string COMMENT '用户态度',
  `level` string COMMENT '黑名单等级',
  `release_time` string COMMENT '黑名单释放时间',
  `date_updated` string COMMENT '操作时间'
) PARTITIONED BY (`pday` string, `operate_type` string) STORED AS ORC;