INSERT overwrite TABLE credit_data.strategy_expt_person_data_clean partition(pday, crowd_no)
SELECT
	t3.user_no,
	'${yesterday_p}' as pday,
	t3.crowd_no
FROM (
	SELECT
		t1.user_no,
		t1.crowd_no
	FROM (
            SELECT
                a.task_no,
                a.crowd_no
            FROM
                ods.ipss_isc_resource_bit_record a
                JOIN ods.ipss_isc_expt_crowd b ON a.crowd_no = b.crowd_no
                JOIN ods.ipss_isc_experiment c ON c.experiment_no = b.experiment_no
            WHERE
                a.STATUS = 1
                AND c.STATUS = 6
                AND c.scene = 'LARGE_COUPON'
                AND c.end_time >= date_sub( CURRENT_DATE,7)
                AND c.end_time < date_sub( CURRENT_DATE,6)
		) t2 
		JOIN credit_data.strategy_expt_person_data t1
		ON t1.batch_no = t2.task_no AND t1.crowd_no = t2.crowd_no
	WHERE t1.pday >= date_format(date_add(current_date, -35),'yyyyMMdd')
	GROUP BY t1.user_no, t1.crowd_no
) t3
;