set spark.sql.adaptive.advisoryPartitionSizeInBytes=67108864;
set spark.sql.adaptive.enabled =false;
set spark.shuffle.sql.partitions=200;

insert overwrite table credit_data.resource_crowd_data_check partition(pday, rule_no)
select user_no, '${yesterday_p}' as pday, rule_no  from (
  select
    user_no, rule_no
  from
    (
      select
        rule_no, user_no,
        row_number() over(partition by rule_no ORDER BY RAND()) seq
      from (
      	select rule_no, user_no from (
	      	select rule_no, user_no, row_number() over(partition by rule_no_key ORDER BY RAND()) r from (
	      		select 
	      			user_no, rule_no, concat(rule_no, cast(rand() * 5 as int)) as rule_no_key 
	      		from credit_data.doris_temp_offline_resource_crowdrule 
	      		where pday = ${yesterday_p}
	      	)
	    ) where r <= 500  
      )
    )
  where
    seq <= 500
) t distribute by pday, rule_no
;