echo $(hostname)

unzip offline_label_application_isc_resource_prd.zip

cwd=$(
  cd $(dirname $0)
  pwd
)

yesterday=${system.biz.date}
yesterday_date=`date -d ${yesterday} +%Y%m%d`
today_date=$(/bin/date -d-0day "+%Y%m%d")

pythonPath=python3
parentScriptDir=$cwd
hdfs_file_path=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/resource_crowd_data_check/pday=${yesterday_date}

source /home/<USER>/anaconda_data_ai/venvs/py37_base/bin/activate
$pythonPath $parentScriptDir/iscHdfsParserHours/iscCrowdDataCheck.py $hdfs_file_path
