#!/bin/bash
export LC_ALL="en_US.utf8"

# 这行必须配置，不然会 module 找不到
source /home/<USER>/anaconda_yushu_jds/bin/activate py36

if [ ! -d "./_360_jinrong_journey_realtimeData" ];then
  echo "========== unzip yhlc23.zip begin =========="
  unzip yhlc_23.zip
  echo "========== unzip yhlc23.zip end =========="
  echo "========== Start executing result table: 20.fin_jt_dm_journey_user_control_record =========="
fi


echo "========== exec shell daily begin ========== " $(date "+%Y-%m-%d %H:%M:%S")
python ./_360_jinrong_journey_realtimeData/main/scriptMainExe.py
echo "========== exec shell daily end ========== " $(date "+%Y-%m-%d %H:%M:%S")