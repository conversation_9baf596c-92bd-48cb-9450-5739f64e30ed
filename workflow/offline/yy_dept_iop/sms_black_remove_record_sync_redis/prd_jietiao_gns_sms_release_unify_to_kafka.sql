-- 4、短信gns解黑名单到kafka
--INSERT overwrite TABLE credit_data.test_jietiao_gns_sms_release_unify_to_kafka_lx PARTITION(pday = '${yesterday_p}')
 INSERT overwrite TABLE credit_data.prd_jietiao_gns_sms_release_unify_to_kafka PARTITION(pday = '${yesterday_p}')
select
    mobile_no_md5x,
    '1' as status,
    remove_time as date_updated,
    'jietiao' as business_source,
    data_source as sub,
    black_type as  type,
    '' as sms_type,
    'sys_off_release_gns' operate_user,
    'BLACK_TO_NORMAL' as operate_type,
    true as  update_db4_flag,
    false as update_db0_flag,
    '' as gnsType
from credit_data.prd_sms_black_remove_record_pda
where pday='${yesterday_p}'
and (sms_source = 'gns' or sms_source = 'control-gns')
and remove_date='${today}'