#!/bin/bash
# file name     : ysCK2journeyCK.sh
# author        : luoxin
# version       : v1.0
# date          : 2022-11-21
# copyright     : @360shuke
# description   : ys库下离线实时表加工成用户旅程表
# usage         : ys库下离线实时表加工成用户旅程表
# function list : 每个用户旅程结果表对应一个工作方法
# history       : 第一版脚本，无历史

export LC_ALL="en_US.utf8"

if [ ! -d "./_360_jinrong_journey_realtimeData" ];then
  echo "========== unzip yhlc05.zip begin =========="
  unzip yhlc05.zip
  echo "========== unzip yhlc05.zip end =========="
  echo "========== Start executing result table: 05.fin_jt_dm_journey_user_coupon_detail =========="
fi

#py 先注释起来,方便本地测试
python3=/usr/bin/python3

declare -A hisMap
hisMap["fin_jt_dm_journey_business_quota"]="false"
hisMap["fin_jt_dm_journey_user_app_login_detail"]="false"
hisMap["fin_jt_dm_journey_user_artificial_detail"]="false"
hisMap["fin_jt_dm_journey_user_base_info"]="false"
hisMap["fin_jt_dm_journey_user_coupon_detail"]="false"
hisMap["fin_jt_dm_journey_user_interest_free_detail"]="false"
hisMap["fin_jt_dm_journey_user_ivr_detail"]="false"
hisMap["fin_jt_dm_journey_user_loan_detail"]="false"
hisMap["fin_jt_dm_journey_user_overdue_detail"]="false"
hisMap["fin_jt_dm_journey_user_push_detail"]="false"
hisMap["fin_jt_dm_journey_user_repay_detail"]="false"
hisMap["fin_jt_dm_journey_user_robot_detail"]="false"
hisMap["fin_jt_dm_journey_user_sms_detail"]="false"
hisMap["fin_jt_dm_journey_user_wjsx_detail"]="false"
hisMap["fin_jt_ods_merge_prd_cis_u_user"]="false"


declare -A todMap
todMap["fin_jt_dm_journey_business_quota"]="false"
todMap["fin_jt_dm_journey_user_app_login_detail"]="false"
todMap["fin_jt_dm_journey_user_artificial_detail"]="false"
todMap["fin_jt_dm_journey_user_base_info"]="false"
todMap["fin_jt_dm_journey_user_coupon_detail"]="false"
todMap["fin_jt_dm_journey_user_interest_free_detail"]="false"
todMap["fin_jt_dm_journey_user_ivr_detail"]="false"
todMap["fin_jt_dm_journey_user_loan_detail"]="false"
todMap["fin_jt_dm_journey_user_overdue_detail"]="false"
todMap["fin_jt_dm_journey_user_push_detail"]="false"
todMap["fin_jt_dm_journey_user_repay_detail"]="false"
todMap["fin_jt_dm_journey_user_robot_detail"]="false"
todMap["fin_jt_dm_journey_user_sms_detail"]="false"
todMap["fin_jt_dm_journey_user_wjsx_detail"]="false"
todMap["fin_jt_ods_merge_prd_cis_u_user"]="false"
todMap["fin_jt_ods_merge_prd_cis_u_user_ext"]="false"


# 脚本执行
operate_py(){
    echo "  == exec $1  $2 begin time= "$(date "+%Y-%m-%d %H:%M:%S")
    ${python3} ./_360_jinrong_journey_realtimeData/analysis_$2/$1.py
    echo "  == exec $1  $2 end time="$(date "+%Y-%m-%d %H:%M:%S")
}

# 执行具体到每个 py,只执行一次
echo "========== exec history begin ========== "+$(date "+%Y-%m-%d %H:%M:%S")
for key in ${!hisMap[*]};do
    # echo "his="+$key
    if [ ${hisMap[$key]} = "true" ];then
        operate_py $key history
    fi
done
echo "========== exec history end ========== "$(date "+%Y-%m-%d %H:%M:%S")

# 执行具体到每个 py,每15分钟执行一次,交由毓数job配置去执行
echo "========== exec daily begin ========== " $(date "+%Y-%m-%d %H:%M:%S")
for key in ${!todMap[*]};do
    # echo "daily="+$key
    if [ ${todMap[$key]} = "true" ];then 
        operate_py $key daily
    fi
done
echo "========== exec daily end ========== " $(date "+%Y-%m-%d %H:%M:%S")


echo "========== exec shell daily begin ========== " $(date "+%Y-%m-%d %H:%M:%S")
${python3} ./_360_jinrong_journey_realtimeData/main/scriptMainExe.py
echo "========== exec shell daily end ========== " $(date "+%Y-%m-%d %H:%M:%S")