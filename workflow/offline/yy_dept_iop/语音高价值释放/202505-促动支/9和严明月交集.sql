--spark
create table dp_data_db.temp_intersection_yanminyue_release_credid_high_value stored as orc as

select  
  a.user_no,
  a.system_code,
  a.flag_loss_status,
  a.status,
  a.black_type,
  a.user_attitude_type,
  a.business_source,
  a.data_source,
  a.release_time,
  a.date_updated,
  a.operate_type,
  a.level,
  a.gray_status  
from dp_data_db.temp_finnal_release_credid_high_value a 
inner join (
    select distinct user_no from dp_data_db.ymy_ivr_blacklist_detail_20250519 
    where is_remove_final=1 AND sx_type2<>'未知'
)b 
on a.user_no=b.user_no