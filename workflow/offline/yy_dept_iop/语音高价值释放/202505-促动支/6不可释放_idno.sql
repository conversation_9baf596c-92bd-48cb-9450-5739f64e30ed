create table dp_data_db.temp_forbit_release_source_with_id_no stored as orc as

select 
    b.user_no,
    'black_000' AS black_key,
    'Y' AS flag_loss_status,
    '相同身份证' AS data_source,
    a.date_updated
from (
    select b.user_no,
        b.id_no_md5x,
        a.date_updated
    from (
        select  user_no,
        date_updated,
         row_number() over(PARTITION BY user_no ORDER BY date_updated DESC) AS rn
         from dp_data_db.temp_forbit_release_source
         having rn =1
    ) a
    inner join (
        select * from credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_userno
        where pday='${yesterday_p}'
    )b 
    on a.user_no=b.user_no
)a 
inner join (
    select * from credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_userno
    where pday='${yesterday_p}'
)b 
on a.id_no_md5x=b.id_no_md5x
where a.user_no!=b.user_no

union all

SELECT
    user_no,
    black_key,
    flag_loss_status,
    data_source,
    date_updated
from dp_data_db.temp_forbit_release_source