select * form dp_data_db.temp_allow_release_credit_black_list a 
inner join (
    SELECT cache_key,
                get_json_object(cache_value,'$.flagLossStatus') as flagLossStatus,
                get_json_object(cache_value,'$.blacktypeList') as blacktypeList,
                get_json_object(cache_value,'$.dataSource') as dataSource,
                get_json_object(cache_value,'$.dateUpdated') as dateUpdated,
                get_json_object(cache_value,'$.addTime') as addTime,
                get_json_object(cache_value,'$.releaseTime') as releaseTime,
                get_json_object(cache_value,'$.black_type') as black_type,
                get_json_object(cache_value,'$.status') as status
            from  (
                SELECT DISTINCT cache_key,cache_value from credit_data.sls_global_ivr_blacklist_redis where pday='${yesterday_p}'
            ) d  where get_json_object(cache_value,'$.flagLossStatus')='Y'
            and (get_json_object(cache_value,'$.addTime') is null or get_json_object(cache_value,'$.addTime')='' or get_json_object(cache_value,'$.addTime') < '${today}')
)b 
on 