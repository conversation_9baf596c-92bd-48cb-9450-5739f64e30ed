--spark
create table dp_data_db.temp_allow_release_source stored as orc as

-- black_002 全量释放
-- 29124 2w
-- 主要是yihui
SELECT
  user_no,
  'yihui_dirty_words'  as system_code,
  'Y' AS flag_loss_status,
  'black_002' as black_type,
  '' as user_attitude_type,
  'jietiao' as business_source,
  'out_yihui' as data_source
  FROM credit_data.jt_label_user_black_list_data_parse_new_a
WHERE user_no IS NOT NULL
    and type = 'dirty_words'

union all

-- black_003 全量释放
-- 主要是yihui
-- 92589 9w
SELECT user_no,
  'yihui_mannul_upload'  as system_code,
  'Y' AS flag_loss_status,
  'black_003' as black_type,
  '' as user_attitude_type,
  'jietiao' as business_source,
  'out_yihui' as data_source
FROM credit_data.jt_label_user_black_list_data_parse_new_a
WHERE (type = 'mannul_upload' or type = 'mysql_mannul_upload')
    AND user_no IS NOT NULL


union all

-- black_009 2025年3月1号前可放开
-- 2227248 222w 
SELECT 
  y.user_no,
  'CTP'  as system_code,
  'Y' AS flag_loss_status,
  'black_009'     AS black_type,
  '' as user_attitude_type,
  'jietiao' as business_source,
  '上海触达回复T' as data_source
FROM (
    SELECT
        mobile_no,
        from_unixtime(update_time_stamp / 1000,'yyyy-MM-dd HH:mm:ss') as operate_time
    FROM ods.prd_ctp_app_c_task_black_list
    WHERE channel = 'sms'
          AND subject IN ('JIETIAO', '')
          AND from_unixtime(create_time_stamp / 1000, 'yyyy-MM-dd HH:mm:ss') < '2025-03-01 00:00:00'
) x
JOIN ods.prd_cis_u_user y ON x.mobile_no = y.mobile_no_md5x
where x.operate_time is not null

union all

-- black_010 2025年3月1号前可放开
-- 19828244 1982w
SELECT y.user_no,
      'GNS'  as system_code,
      'Y' AS flag_loss_status,
      x.type as black_type,
      '' as user_attitude_type,
      'jietiao' as business_source,
      x.sub as data_source
FROM (
  SELECT mobile_no_md5x,
        -- IF(status = 1, 0, 1)  AS status,-- 和我们的是反的
        '借条GNS短信'         AS sub,
        'black_010'           AS type,
        row_number() over(PARTITION BY mobile_no_md5x ORDER BY date_updated DESC) AS rn
  FROM ods.prd_gns_im_sms_blacklist
  WHERE (type IS NOT NULL AND type !='6'  AND date_updated < '2025-03-01 00:00:00')
  OR (type IS NULL AND date_updated < '2025-03-01 00:00:00')
  having rn=1 and (status = 1 or status is null)
) x
LEFT JOIN ods.prd_cis_u_user y
ON x.mobile_no_md5x = y.mobile_no_md5x
where y.user_no is not null

union all

-- black_016、 black_017、 black_021 可全部解除
-- black_016 825111 82w
-- black_017 923899 92w
-- black_021 86622 8w
SELECT mobile_md5x as user_no,
    if(system_code is null or system_code='','SLS',system_code) as system_code,
    flag_loss_status ,
    black_key as black_type,
    user_attitude_type,
    business_type as business_source,
    data_source     
FROM (
  SELECT mobile_md5x,
        update_time,
        operate_status,
        black_value,
        business_type,
        get_json_object(black_value, '$.systemCode')                                                   AS system_code,
        get_json_object(black_value, '$.user_attitude_type')                                           AS user_attitude_type,
        nvl(get_json_object(black_value, '$.dataSource'),'')                                           AS data_source,
        split(regexp_replace(get_json_object(black_value, '$.blacktypeList'), '\\[|\\]|\\"', ''),',')  AS black_type_list,
        get_json_object(black_value, '$.flagLossStatus')                                               AS flag_loss_status,
        row_number() over(PARTITION BY mobile_md5x ORDER BY update_time DESC) AS rn
  FROM ods.prd_data_service_srds_sms_black_record
  WHERE business_type = 'jietiao'
  AND (black_type IS NULL OR black_type NOT IN ('app_sms'))
  AND mobile_md5x LIKE 'UR%'
  HAVING rn = 1 and operate_status = '1'
  ) LATERAL VIEW explode(black_type_list) temptable AS black_key
WHERE black_key in ('black_016','black_017','black_021')


union all

-- 030 电销坐席手动加黑
-- 2024年11月1号前可放开
-- 3388426 338w
SELECT 
  user_no,
  if(system_code is null or system_code='','SLS',system_code) as system_code,
  flag_loss_status as flag_loss_status ,
  'black_030' as black_type,
  user_attitude_type,
  business_type,
  data_source
FROM (
        SELECT
          business_type                                                             AS business_type,
          mobile_md5x                                                               AS user_no,
          split(regexp_replace(get_json_object(black_value, '$.dataSource'), '\\[|\\]|\\"', ''),',')  AS data_source_list,
          get_json_object(black_value, '$.systemCode')                                                   AS system_code,
          get_json_object(black_value, '$.flagLossStatus')                                               AS flag_loss_status,
          get_json_object(black_value, '$.user_attitude_type')                                           AS user_attitude_type,
          get_json_object(black_value, '$.systemCode')                              AS systemCode,
          black_type                                                                AS black_type_record,
          update_time as operate_time,
          row_number() over(PARTITION BY mobile_md5x ORDER BY update_time DESC) AS rn
        FROM ods.prd_data_service_srds_sms_black_record
        WHERE black_type IN ('app_voice', 'voice')
        and mobile_md5x like 'UR%'
        and pday<'20241101'
        having rn=1 and flag_loss_status='Y'
)
LATERAL VIEW explode(data_source_list) temptable AS data_source
where operate_time<'2024-11-01 00:00:00' and (data_source = '电销系统'or systemCode='电销坐席手动')

-- union all

-- 是redis，和hive merge数据表保持一致 产生的
-- black_999 历史差异
-- 仅仅是redis的差异可以全部解除，操作记录映射的时候，有的不好映射的也归为black_999
-- 4029990 402w  
-- 扫描的时候就是 取的 Y,因此这里不需要进行额外条件是否为黑名单
-- select 
--     a.user_no,
--     '历史差异' as system_code,
--     flag_loss_status,
--     'black_999' as black_type,
--     '' as user_attitude_type, 
--     'jietiao' as business_type,
--      '历史差异数据' as data_source
-- from (
--   select *     
--   from credit_data.prd_jietiao_global_ivr_blacklist_redis_diff
--   where pday= '${yesterday_p}'
-- )a
-- inner join (
--   select user_no
--   ,black_type
--   ,date_updated
--   ,dataSource
--   ,systemCode
--   from (
--       SELECT cache_key as user_no,
--           get_json_object(cache_value,'$.dateUpdated') as date_updated,
--           get_json_object(cache_value,'$.dataSource') as dataSource,
--           get_json_object(cache_value,'$.systemCode') as systemCode,
--           split(regexp_replace(get_json_object(cache_value,'$.blacktypeList'), '\\[|\\]|\\"', ''),',')  AS black_type_list
--       from (
--           SELECT DISTINCT cache_key,cache_value from credit_data.sls_global_ivr_blacklist_redis where pday='${yesterday_p}'
--       ) d  
--       where get_json_object(cache_value,'$.flagLossStatus')='Y'
--           and (get_json_object(cache_value,'$.addTime') is null or get_json_object(cache_value,'$.addTime')='' or get_json_object(cache_value,'$.addTime') < '${today}')
--   )LATERAL VIEW explode(black_type_list) temptable AS black_type
-- )b 
-- on a.user_no=b.user_no
-- where (b.black_type in ('black_002','black_003','black_016','black_017','black_021')
--     or (b.black_type = 'black_009' and b.date_updated<'2025-03-01 00:00:00')
--     or (b.black_type = 'black_001' and  (data_source = '电销系统'or systemCode='电销坐席手动') and b.date_updated<'2024-11-01 00:00:00')
--   )

-- union all

  
-- select 
--     *
-- from (
--   select *     
--   from credit_data.prd_jietiao_global_ivr_blacklist_redis_diff
--   where pday= '${yesterday_p}'
-- )a
-- inner join (
--   select user_no
--   ,black_type
--   ,date_updated
--   ,dataSource
--   ,systemCode
--   from (
--       SELECT cache_key as user_no,
--           get_json_object(cache_value,'$.dateUpdated') as date_updated,
--           get_json_object(cache_value,'$.dataSource') as dataSource,
--           get_json_object(cache_value,'$.systemCode') as systemCode,
--           split(regexp_replace(get_json_object(cache_value,'$.blacktypeList'), '\\[|\\]|\\"', ''),',')  AS black_type_list
--       from (
--           SELECT DISTINCT cache_key,cache_value from credit_data.sls_global_ivr_blacklist_redis where pday='${yesterday_p}'
--       ) d  
--       where get_json_object(cache_value,'$.flagLossStatus')='Y'
--           and (get_json_object(cache_value,'$.addTime') is null or get_json_object(cache_value,'$.addTime')='' or get_json_object(cache_value,'$.addTime') < '${today}')
--   )LATERAL VIEW explode(black_type_list) temptable AS black_type
-- )b 
-- on a.user_no=b.user_no
-- left join (
--   select * from (
--     SELECT mobile_no_md5x,
--           -- IF(status = 1, 0, 1)  AS status,-- 和我们的是反的
--           '借条GNS短信'         AS sub,
--           'black_010'           AS black_type,
--       type,
--       status,
--           row_number() over(PARTITION BY mobile_no_md5x ORDER BY date_updated DESC) AS rn
--     FROM ods.prd_gns_im_sms_blacklist
--     having rn=1 and (status = 1 or status is null) and type ='6'  
--   )x
--   inner join  ods.prd_cis_u_user y
--   ON x.mobile_no_md5x = y.mobile_no_md5x
-- )c 
-- on  a.user_no=c.user_no
-- where  (b.black_type = 'black_010' and b.date_updated<'2025-03-01 00:00:00') and c.user_no is null