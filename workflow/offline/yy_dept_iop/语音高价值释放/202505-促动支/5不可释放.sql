-- 65242632 6500w
--spark
create table dp_data_db.temp_forbit_release_source stored as orc as

-- 005 注销  全量不可解除
-- 33690313 3300w
SELECT
    user_no,
    'black_005' AS black_key,
    'Y' AS flag_loss_status,
    '注销用户' AS data_source,
    date_updated
FROM ods.prd_cis_u_user
WHERE user_state != 1

union all

-- 006 管制用户 全量不可解除
-- 26042550 2600w
SELECT
    user_no,
    'black_006' AS black_key,
    'Y' AS flag_loss_status,
    '管制用户' AS data_source,
    date_updated
FROM ods.prd_cis_u_user_control
WHERE SUBSTRING (control_code, 1, 1) IN ('A', 'B', 'C', 'E', 'G', 'H', 'I', 'Z', 'K', 'L', 'M')

union all

-- 007 高敏
-- 71
select 
    b.user_no,
    'black_007'  AS black_key,
    'Y' AS flag_loss_status,
    '高敏用户' AS data_source,
    a.date_updated
 from (
 select
        mobile_no_md5x,
        0 as status,
        '2023-02-06 20:37:42' as date_updated,
        '高敏用户' as sub,
        'black_007' as type,
        'gaomin' as systemCode,
        '' as assist_type
        from credit_data.credit_data_prd_jietiao_gaomin_user
)a
inner join(
    select * from ods.prd_cis_u_user
    where user_state='1'
)b
on a.mobile_no_md5x=b.mobile_no_md5x

union all

-- black_018 外部投诉用户 不可以做解除
-- 33654 3w
select user_no,
black_key,
flag_loss_status,
data_source,
date_updated 
from (
    select 
        user_no,
        'black_018' as black_key,
        'Y' AS flag_loss_status,
        '外部投诉用户' AS data_source,
        date_updated,
        row_number() over(PARTITION BY mobile_no_md5x ORDER BY date_updated DESC) AS rn
    from ods.prd_sls_sls_black_list_marketing_manager
    where user_no !='' and  user_no is not null
    having rn=1 and operate_status=0
)

union all 

-- black_031 不做解除
-- black_032 不手动解除，自动流转
-- black_033 不做解除
-- black_101 不做解除
SELECT 
    user_no,
    case when data_source = 'CRM系统' or systemCode='客服CRM系统' then
        'black_031'
        when data_source = '上海语音外呼' or systemCode='模型质检2.0' then
        'black_032'
        when systemCode ='质检员手动添加' then
        'black_033'
        when data_source IN ('消保页', '首页限频降噪弹窗', '首页营销通知弹窗','用户') then
        'black_101'
     else '未知'
     end as black_key,
      'Y' AS flag_loss_status,
    data_source,
    operate_time as date_updated
FROM (
        SELECT 
        mobile_md5x                                                               AS user_no,
        SUBSTRING(operate_time, 1, 19)                                            AS operate_time,
        operate_status                                                            AS status,
        split(regexp_replace(get_json_object(black_value, '$.dataSource'), '\\[|\\]|\\"', ''),',')  AS data_source_list,
        get_json_object(black_value, '$.systemCode')                              AS systemCode,
        row_number() over(PARTITION BY mobile_md5x ORDER BY update_time DESC) AS rn
        FROM ods.prd_data_service_srds_sms_black_record
        WHERE black_type IN ('app_voice', 'voice')
        and mobile_md5x like 'UR%'
        and business_type='jietiao'
        having rn=1 and status=1
    )
LATERAL VIEW explode(data_source_list) temptable AS data_source
where (data_source = 'CRM系统' or systemCode='客服CRM系统') -- black_031
        or (data_source = '上海语音外呼' or systemCode='模型质检2.0') -- black_032
        or (systemCode ='质检员手动添加') -- black_033
        or (data_source IN ('消保页', '首页限频降噪弹窗', '首页营销通知弹窗','用户')) -- black_101 和操作记录看,这里更严格一点
