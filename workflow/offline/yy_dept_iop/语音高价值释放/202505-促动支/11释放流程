1(改动)、把dp_data_db.temp_finnal_release_credid_high_value 
    吐到kafka sls.ivr.black.deal.offline.msf.topic:sls_level_black_list_topic
    kafka-pro-a.daikuan.qihoo.net:9092
    kafka-pro-b.daikuan.qihoo.net:9092
2(改动)、等步骤1结束,释放成功数据入prd_ivr_blacklist_release_detail_pdi表
    验证一下表数据
3(无需改动)、work_flow ,credit_data.prd_ivr_black_remove_record_pda(where pday>'')  黑名单工作流，自动包含步骤2的结果，进而影响到语音merge表的合成。
    验证一下表数据
4(无需改动)、prd_level_black_list_work_flow->gray_to_normal ,自动扫描(ods.prd_sls_sls_ivr_blacklist, operate_status = '1' and gray_status = 'Y')状态灰到正常
    验证一下表数据
5(改动)、修改操作记录代码
    操作记录是 去年11月左右，基于ods加工的，所以没有，这里需要处理.