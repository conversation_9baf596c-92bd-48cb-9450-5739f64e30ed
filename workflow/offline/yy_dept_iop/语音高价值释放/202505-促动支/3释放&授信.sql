--spark
create table dp_data_db.temp_allow_release_credit_black_list stored as orc as
select 
  a.user_no,
  a.system_code,
  a.flag_loss_status,
  a.black_type,
  a.user_attitude_type,
  a.business_source,
  a.data_source
 from dp_data_db.temp_allow_release_source_with_id_no a 
inner join(
  SELECT * FROM safe3_copy.lhj_yunying_sx_user_base_pdi 
  WHERE status_month='2025-05-01'
)b 
on a.user_no=b.user_no