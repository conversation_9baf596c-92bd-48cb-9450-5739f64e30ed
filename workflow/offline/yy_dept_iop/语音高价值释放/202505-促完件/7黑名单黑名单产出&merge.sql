-- 3839844
--spark
create table dp_data_db.temp_final_release_register_no_credit_with_merge stored as orc as

-- 允许授信黑名单
select    
  a.user_no,
  a.system_code,
  a.flag_loss_status,
  a.black_type,
  a.user_attitude_type,
  a.business_source,
  a.data_source,
  b.flag
  from dp_data_db.temp_final_release_register_no_credit_high_value a
inner join (
	select
	      *
	    from
	      credit_data.prd_jietiao_global_ivr_blacklist_merge
	    where
	      pday = '${yesterday_p}'
	      and flag_loss_status = 'Y'
	      and user_no is not null
)b
on a.user_no=b.user_no
where b.flag NOT RLIKE 'black_001|black_005|black_006|black_007|black_018|black_101'
