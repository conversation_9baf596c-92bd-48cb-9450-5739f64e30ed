--spark
create table dp_data_db.temp_allow_release_register_no_credit_existence_black_list stored as orc as
select a.user_no,
  a.system_code,
  a.flag_loss_status,
  a.black_type,
  a.user_attitude_type,
  a.business_source,
  a.data_source
from dp_data_db.temp_allow_release_register_no_credit_black_list a 
inner join (
    SELECT cache_key from  (
                SELECT DISTINCT cache_key,cache_value from credit_data.sls_global_ivr_blacklist_redis where pday='${yesterday_p}'
    ) d  where get_json_object(cache_value,'$.flagLossStatus')='Y'
    and (get_json_object(cache_value,'$.addTime') is null or get_json_object(cache_value,'$.addTime')='' or get_json_object(cache_value,'$.addTime') < '${today}')
)b 
on a.user_no=b.cache_key