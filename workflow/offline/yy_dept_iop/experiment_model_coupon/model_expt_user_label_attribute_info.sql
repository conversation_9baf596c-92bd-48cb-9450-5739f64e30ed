insert overwrite table `credit_data`.`model_expt_user_label_attribute_info` partition(pday='${yesterday_p}')
select
  t1.user_no,
  t1.k827,
  t1.k703,
  t1.k671,
  t1.k641,
  t1.k636,
  t1.k631,
  t1.krr300,
  t1.krr624,
  t1.krr614,
  t1.krr613,
  t1.krr335,
  t1.krr268,
  t2.utas_tag_ice36_details_days,
  t2.is_sd_zx_falloff_user,
  t2.qw_status,
  t2.is_36tr_tran_proc,
  t2.is_36app_install,
  t2.risk_layer,
  t2.remain_available_marketing_amt,
  t2.risk_r_tag_v2,
  t2.overdue_risk_dc_val,
  t2.is_subscribe_wxac
from
  (
    select
      *
    from
      credit_data.model_expt_user_label_offline_attribute_info
    where
      pday = '${yesterday_p}'
  ) t1
  join (
    select
      *
    from
      credit_data.model_expt_user_label_online_attribute_info
    where
      pday = '${yesterday_p}'
  ) t2 on t1.user_no = t2.user_no;

alter table `credit_data`.`model_expt_user_label_attribute_info` drop if exists partition(pday='${7days_ago}');
