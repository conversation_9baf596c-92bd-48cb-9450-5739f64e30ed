#!/bin/bash
##  file name     : iscExptModelCouponDataToPika.sh
##  author        : l<PERSON><PERSON><PERSON>
##  version       : v1.0
##  date          : 2024-06-10
##  copyright     : @qctx
##  description   : 一体化实验模型领券数据refresh到pika
##  usage         : 一体化实验模型领券数据refresh到pika
##  function list :
##  history       : 第一版脚本，无历史
# set -e

# set -x
unzip offline_label_application_isc_resource_prd.zip

cwd=$(
  cd $(dirname "$0")
  pwd
)

yesterday=${system.biz.date}
yesterday_date=`date -d ${yesterday} +%Y%m%d`

#pythonPath=python3
pythonPath=/home/<USER>/anaconda_yushu_jds/envs/py36/bin/python

parentScriptDir=$cwd
hdfs_file_path=hdfs://360jinronglycc/user/hive/warehouse/credit_data.db/model_expt_user_coupon_suggest_info/pday=${yesterday_date}

$pythonPath $parentScriptDir/iscHdfsParserHours/iscExptModelCouponDataToPika.py $hdfs_file_path