insert overwrite table `credit_data`.`model_experiment_info` partition(pday='${yesterday_p}')
select
  experiment_no,
  experiment_name,
  receive_type,
  model_code,
  activity_code,
  activity_time,
  crowd_no,
  group_type,
  case when coupon_package = 'null' then '' else coupon_package end as coupon_package,
  date_created
from
  (
    select
      a.experiment_no,
      a.name as experiment_name,
      b.activity_code,
      concat(date_format(b.activity_start_time, 'yyyyMMdd'), '~', date_format(b.activity_end_time, 'yyyyMMdd')) as activity_time,
      c.crowd_no,
      c.group_type,
      ifnull(d.coupon_packages,'null') as coupon_packages,
      ifnull(d.receive_type,'') as receive_type,
      ifnull(d.model_code,'') as model_code,
      a.date_created
    from
      ods.prd_ipss_isc_experiment a
      left join ods.prd_ipss_isc_expt_promotion b on a.experiment_no = b.experiment_no
      left join ods.prd_ipss_isc_expt_crowd c on a.experiment_no = c.experiment_no
      left join ods.prd_ipss_isc_expt_crowd_channel_coupon_package d on d.experiment_no = a.experiment_no 
          and d.crowd_no = c.crowd_no and d.is_deleted = 0
    where
      a.is_deleted = 0
      and b.is_deleted = 0
	  and c.is_deleted = 0
      and a.scene = 'LARGE_COUPON'
      and a.status in (5, 7) --  5-运行中 7-暂停
  ) t1 lateral VIEW explode(split(coupon_packages, ',')) temp AS coupon_package
;

alter table `credit_data`.`model_experiment_info` drop if exists partition(pday='${7days_ago}');
