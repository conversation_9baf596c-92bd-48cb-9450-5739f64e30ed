-- 全量表
-- yy_dept_offline_dt->black_list_operate
-- 验证一下应该没有001 了,可能 blackvalue中是list中的，会含有001
-- dp_data_db.test_ivr_black_operate_record_lx20240919
--INSERT overwrite TABLE credit_data.test_sls_ivr_black_operate_record_pda_lx PARTITION(pday = '${yesterday_p}')
INSERT overwrite TABLE credit_data.test_sls_ivr_black_operate_record_temp_pda PARTITION(pday = '20241117')
-- INSERT overwrite TABLE credit_data.sls_ivr_black_operate_record_pda PARTITION(pday = '${yesterday_p}')
select 
       source_id,
       business_type,
       operate_business,
       user_no,
       status,
       operate_user,
       case when length(operate_time)>19 then
                     substring(operate_time,0,19)
              when length(operate_time)=10 then
                     CONCAT(operate_time,' 00:00:00')
              else operate_time
       end as operate_time,
       source_describe,
       black_type,
       source_table_name,
       black_type_report,
       category_one,
       category_two
from credit_data.test_sls_ivr_black_operate_idno_supplement_pda
-- from credit_data.sls_ivr_black_operate_idno_supplement_pda
where pday='20241117'

UNION all

-- 历史差异(是不是可以只用初始化一次?)
SELECT
      '' as source_id,
      'jietiao' as business_type,
      '' as operate_business,
       x.user_no as user_no,
      if(flagLossStatus='Y',1,0)      as status,
      'sys' as operate_user,
       case when length(dateUpdated)>19 then
                     substring(dateUpdated,0,19)
              when length(dateUpdated)=10 then
                     CONCAT(dateUpdated,' 00:00:00')
              else dateUpdated
       end as operate_time,
       '历史差异' as source_describe,
       'black_999' as black_type,
       'sls_global_ivr_blacklist_redis' as source_table_name,
       'black_999' as black_type_report,
       '' as category_one,
       '' as category_two
from (
       select 
              cache_key as user_no,
              get_json_object(cache_value,'$.flagLossStatus') as flagLossStatus,
              get_json_object(cache_value,'$.dateUpdated') as dateUpdated,
              get_json_object(cache_value,'$.blacktypeList') as blacktypeList,
              get_json_object(cache_value,'$.user_attitude_type') as user_attitude_type,
              get_json_object(cache_value,'$.dataSource') as dataSource,
              get_json_object(cache_value,'$.level') as slevel
       from (
              SELECT DISTINCT cache_key,cache_value 
              from credit_data.sls_global_ivr_blacklist_redis 
              where pday='20241106' 
              and length(cache_key)>2
       )
       where get_json_object(cache_value,'$.dateUpdated') is null 
                     or get_json_object(cache_value,'$.dateUpdated') < '2024-11-07'
) x
left join (
       select 
              source_id,
              business_type,
              operate_business,
              user_no,
              status,
              operate_user,
              operate_time,
              source_describe,
              black_type,
              source_table_name,
              black_type_report,
              category_one,
              category_two,
              row_number() over(partition by user_no order by operate_time desc) as rn
       from credit_data.test_sls_ivr_black_operate_idno_supplement_pda
       -- from credit_data.sls_ivr_black_operate_idno_supplement_pda
       where pday='20241117'
       having rn =1
)y
on x.user_no = y.user_no
where y.user_no is null 
-- or x.dateUpdated > y.operate_time


UNION all

select source_id,
       business_type,
       operate_business,
       user_no,
       status,
       operate_user,
       case when length(operate_time)>19 then
                     substring(operate_time,0,19)
              when length(operate_time)=10 then
                     CONCAT(operate_time,' 00:00:00')
              else operate_time
       end as operate_time,
       source_describe,
       black_type,
       source_table_name,
       black_type as black_type_report,
       category_one,
       category_two
from (
       select operate_type, 
       data_source,
       blacktype_list,
       '' as source_id,
       business_source as business_type,
       '' as operate_business,
       mobile_no_md5x,
       user_no,
       if(flag_loss_status='Y',1,0)      as status ,
       operate_user,
       event_time as operate_time,
       system_code as source_describe,
       case when black_type='black_001' then
              CASE  WHEN  system_code ='电销坐席手动' THEN
                     'black_030'
                     WHEN  system_code ='客服CRM系统' THEN
                     'black_031'
                     WHEN system_code ='模型质检2.0' THEN
                     'black_032'
                     WHEN  system_code ='质检员手动添加'  then
                     'black_033'
                     END
              when black_type is null then
              CASE  WHEN system_code='二次注册' or data_source='注销用户' then 
                            'black_005'
                     WHEN operate_type='OFFLINE_ADD' and (system_code='ID_NO' or data_source = 'id_no撞库' or data_source = 'id_no撞库,') then 
                            'black_000'
                     WHEN operate_type='OFFLINE_ADD' and system_code='CTP' then 
                            'black_009'
                     WHEN operate_type='OFFLINE_ADD' and system_code='GNS' then 
                            'black_010'
                     WHEN operate_type='OFFLINE_ADD' and system_code='USER-CONTROL' then 
                            'black_006'
                     WHEN operate_type='OFFLINE_ADD' and system_code='USER-CANCEL' then 
                            'black_005'
                     when system_code ='模型质检2.0' then
                            'black_032'
                     when system_code ='电销坐席手动' then
                            'black_030'
                     when system_code ='客服CRM系统' then
                            'black_031'
                     when system_code ='质检员手动添加' then
                            'black_033'
                     when data_source ='["消保页"]' or data_source ='消保页' then
                            'black_101'
                     when data_source='高敏感客户' then
                            'black_007'
                     WHEN blacktype_list is not null and blacktype_list!='' and blacktype_list LIKE '%black_005%' THEN
                            'black_005'
                     WHEN blacktype_list is not null and blacktype_list!='' and blacktype_list LIKE '%black_101%' 
                                   and (data_source like '%消保页%' or  data_source like '%首页限频降噪弹窗%' or data_source like '%首页营销通知弹窗%' or data_source like '%用户%') THEN
                            'black_101'
                     WHEN blacktype_list is not null and blacktype_list!='' and blacktype_list LIKE '%black_010%' and data_source like '%短信黑名单%' THEN
                            'black_010'
                            WHEN blacktype_list is not null and blacktype_list!='' and blacktype_list LIKE '%black_009%' and data_source like '%短信黑名单%' THEN
                            'black_009'
                     else 'black_999'
              END
       else black_type
       end as black_type,
       '' as black_level,
       'prd_black_ivr_operate_end_req_pdi' as source_table_name,
       '' as category_one,
       '' as category_two 
       from credit_data.prd_black_ivr_operate_end_req_pdi
       where ((pday>='20241107' and pday<'20241118') 
              -- GRAY_TO_NORMAL,FORCE_BLACK_TO_GRAY 这个 release_time 是今天,状态表的被排除
                     or (pday='20241118' and operate_type in('OFFLINE_ADD') and date_updated<'2024-11-18'))
       and operate_status='SUCCESS'
       -- 黑名单操作记录不包含灰名单
       and operate_type not in ('FORCE_NORMAL_TO_GRAY','GRAY_TO_NORMAL')
)