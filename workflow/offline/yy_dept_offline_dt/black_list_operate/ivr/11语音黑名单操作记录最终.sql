-- 全量表
-- yy_dept_offline_dt->black_list_operate
-- 验证一下应该没有001 了,可能 blackvalue中是list中的，会含有001
-- dp_data_db.test_ivr_black_operate_record_lx20240919
--INSERT overwrite TABLE credit_data.test_sls_ivr_black_operate_record_pda_lx PARTITION(pday = '${yesterday_p}')
INSERT overwrite TABLE credit_data.test_sls_ivr_black_operate_record_pda PARTITION(pday = '20241117')
-- INSERT overwrite TABLE credit_data.sls_ivr_black_operate_record_pda PARTITION(pday = '${yesterday_p}')

select 
       source_id,
       business_type,
       operate_business,
       user_no,
       status,
       operate_user,
       operate_time,
       source_describe,
       black_type,
       source_table_name,
       black_type_report,
       category_one,
       category_two
from credit_data.test_sls_ivr_black_operate_record_post_pda
-- from credit_data.sls_ivr_black_operate_idno_supplement_pda
where pday='20241117'

UNION all

-- 历史差异(是不是可以只用初始化一次?)
SELECT
      '' as source_id,
      'jietiao' as business_type,
      '' as operate_business,
       x.user_no as user_no,
      if(flagLossStatus='Y',1,0)      as status,
      'sys' as operate_user,
       case when length(dateUpdated)>19 then
                     substring(dateUpdated,0,19)
              when length(dateUpdated)=10 then
                     CONCAT(dateUpdated,' 00:00:00')
              else dateUpdated
       end as operate_time,
       '历史差异' as source_describe,
       'black_999' as black_type,
       'sls_global_ivr_blacklist_redis' as source_table_name,
       'black_999' as black_type_report,
       '' as category_one,
       '' as category_two
from (
       select 
              cache_key as user_no,
              get_json_object(cache_value,'$.flagLossStatus') as flagLossStatus,
              get_json_object(cache_value,'$.dateUpdated') as dateUpdated,
              get_json_object(cache_value,'$.blacktypeList') as blacktypeList,
              get_json_object(cache_value,'$.user_attitude_type') as user_attitude_type,
              get_json_object(cache_value,'$.dataSource') as dataSource,
              get_json_object(cache_value,'$.level') as slevel
       from (
              SELECT DISTINCT cache_key,cache_value 
              from credit_data.sls_global_ivr_blacklist_redis 
              where pday='20241106' 
              and length(cache_key)>2
       )
       where get_json_object(cache_value,'$.dateUpdated') is null 
                     or get_json_object(cache_value,'$.dateUpdated') <= '2024-11-07'
) x
left join (
       select 
              source_id,
              business_type,
              operate_business,
              user_no,
              status,
              operate_user,
              operate_time,
              source_describe,
              black_type,
              source_table_name,
              black_type_report,
              category_one,
              category_two,
              row_number() over(partition by user_no order by operate_time desc) as rn
       from credit_data.test_sls_ivr_black_operate_record_post_pda
       -- from credit_data.sls_ivr_black_operate_idno_supplement_pda
       where pday='20241117'
       having rn =1
)y
on x.user_no = y.user_no
where y.user_no is null 