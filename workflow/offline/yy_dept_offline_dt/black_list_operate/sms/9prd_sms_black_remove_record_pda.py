#!/usr/bin/python
# -*- coding: utf-8 -*-
__author__ = 'wangxia<PERSON><PERSON><PERSON>'
'''
-- file name     : sms_black_remove_record_pda.py
-- version       : v1.0
-- date          : 2022/11/02 00:00
-- description   : 
'''
import sys
import subprocess
import traceback
import redis
import json
import uuid
import os

from os import path
from datetime import datetime
sys.path.append("./")

class LoadCtpAppData:
    def __init__(self, the_date):
        self.current_yyyymmdd = datetime.now().strftime("%Y%m%d")
        # 格式化为yyyy-MM-dd
        self.current_yyyy_mm_dd = datetime.now().strftime("%Y-%m-%d")
        self.the_date = the_date
        self.hadoop = "hadoop"
        self.spark = "spark-sql --name 'LoadCtpAppData_tianrongji' --driver-memory 3g --executor-memory 3g --conf spark.dynamicAllocation.maxExecutors=100 --conf spark.task.maxFailures=10 --conf spark.sql.broadcastTimeout=1800  --conf spark.executor.memoryOverhead=3g --conf spark.driver.maxResultSize=8192M --master=yarn --hiveconf hive.cli.print.header=false --queue yushu_offline_normal"
        self.table_path = f"/user/hive/warehouse/credit_data.db/prd_sms_black_remove_record_pda/pday={the_date}"
        self.local_read_file = self.create_local_file("prd_sms_black_remove_record_pda", self.the_date)
        self.local_write_file = self.create_local_file("prd_black_remove_record_detail_pdi", self.the_date)
        self.upload_table_path = f"/user/hive/warehouse/credit_data.db/prd_black_remove_record_detail_pdi/pday={the_date}/source=sms"
        self.upload_table_path_1 = f"/user/hive/warehouse/credit_data.db/prd_black_remove_record_detail_pdi/pday={the_date}/source=sms/"
    def create_local_file(self, table, the_date):
        local_directory = os.path.join(path.join(path.join(path.dirname(path.realpath(__file__)), "data"), table),
                                       the_date)
        filename = f"{uuid.uuid4().hex}.txt"
        os.makedirs(local_directory, exist_ok=True)
        file_path = os.path.join(local_directory, filename)
        return file_path

    @staticmethod
    def pika_init():
        #host = "*************"
        #port = 5987
        #psw = "051c5f5a4c05f7ad"
        #pool = redis.ConnectionPool(host=host, port=port, db=15, password=psw)
        #host = "*************"
        host = "pro-26466-rw.redis.daikuan.qihoo.net"
        port = 26466
        psw = "0ab78dbd199900e8360"
        pool = redis.ConnectionPool(host=host, port=port, db=0, password=psw)
        connect = redis.StrictRedis(connection_pool=pool)
        return connect

    def data_landing(self, table_path, local_file):
        cmd = f"{self.hadoop} fs -getmerge {table_path} {local_file}"
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        if status != 0:
            print(f"落地失败：{str(status)},{str(output)}")
            raise Exception("落地失败")

    def load_pika(self):

        conn = self.pika_init()
        count = 0
        all_count = 0
        record_black_count = 0
        gns_record_black_count = 0
        with conn.pipeline(transaction=False) as p:
            # 导入数据
            with open(self.local_read_file) as file:
                lst = []
                gns_lst = []
                lstRemoveTimeMap = {}
                gnsLstRemoveTimeMap = {}
                for line in file:
                    if line.strip() == "":
                        continue
                    business_type, mobile_no_md5x, data_source, sms_source, black_type, add_time, behavior_time, \
                    remove_date, remove_time = line.split("\001")
                    all_count = all_count + 1
                    count = count + 1
                    if remove_date == self.current_yyyy_mm_dd:
                        if sms_source != 'control-gns':
                            lst.append(mobile_no_md5x)
                            lstRemoveTimeMap[mobile_no_md5x] = remove_time
                        if sms_source == 'gns' or sms_source == 'control-gns':
                            gns_lst.append(mobile_no_md5x)
                            gnsLstRemoveTimeMap[mobile_no_md5x] = remove_time
                    if not count % 1000:
                        keys_to_get = list(set(lst))
                        gns_keys_to_get = list(set(gns_lst))
                        modify_black_count = self.release_ivr_black_list(self, keys_to_get, 'db0_', p,lstRemoveTimeMap)
                        gns_modify_black_count = self.release_ivr_black_list(self, gns_keys_to_get, 'db4_', p,gnsLstRemoveTimeMap)
                        print(f"解除黑名单的数量1:{str(all_count)},{str(modify_black_count)},{str(gns_modify_black_count)}")
                        record_black_count = record_black_count + modify_black_count
                        gns_record_black_count = gns_record_black_count + gns_modify_black_count
                        count = 0
                        # 把数组置为空
                        lst = []
                        gns_lst = []
                        lstRemoveTimeMap = {}
                        gnsLstRemoveTimeMap = {}
                if count > 0:
                    keys_to_get = list(set(lst))
                    gns_keys_to_get = list(set(gns_lst))
                    modify_black_count = self.release_ivr_black_list(self, keys_to_get, 'db0_', p,lstRemoveTimeMap)
                    gns_modify_black_count = self.release_ivr_black_list(self, gns_keys_to_get, 'db4_', p,gnsLstRemoveTimeMap)
                    print(f"解除黑名单的数量2:{str(all_count)},{str(modify_black_count)},{str(gns_modify_black_count)}")
                    record_black_count = record_black_count + modify_black_count
                    gns_record_black_count = gns_record_black_count + gns_modify_black_count
                print(f"解除黑名单总共数量：{str(all_count)},{str(record_black_count)},{str(gns_record_black_count)}")
                file.close()

    # 释放ivr黑名单的数据
    @staticmethod
    def release_ivr_black_list(self, keys_to_get, key_pre, p,removeTimeMap):
        modify_count = 0
        if len(keys_to_get) == 0:
            print("keys_to_get 列表为空")
            return modify_count
            # 退出程序或者进行相应的处理
        # 批量获取 key 对应的 value
        for key in keys_to_get:
            p.get(key_pre + key)
        # 执行管道操作
        values = p.execute()
        # 将需要更新的 key 和 value 进行拼接
        write_line = []
        # 遍历每个 value，进行拼接并存回 redis 有value并且flagLossStatus的用户号取出
        for i in range(len(keys_to_get)):
            value = values[i]
            if value is not None:
                decoded_value = value.decode('utf-8')
                json_value = json.loads(decoded_value)
                if json_value['status'] == "0":
                    removeTime = removeTimeMap[keys_to_get[i]]
                    if json_value.get('date_updated') is not None and removeTime >= json_value['date_updated']:
                        json_value['status'] = "1"
                        new_value = json.dumps(json_value, ensure_ascii=False)
                        line = "jietiao" + "\001" + "" + "\001" + keys_to_get[i] + "\001" + key_pre + "\001" + decoded_value + "\001" + new_value
                        write_line.append(line)
                        # 对 value 进行拼接并存回 redis
                        p.set(key_pre + keys_to_get[i], new_value)
                        modify_count = modify_count + 1
                    else:
                        print(f"no need release key={keys_to_get[i]},removeTime={removeTime},json_value={json_value.get('date_updated')}")

        # 存在黑名单用户执行，并记录进本地写入文件中
        if len(write_line) == 0:
            return modify_count
        result = "\n".join(write_line)
        with open(self.local_write_file, "a") as f:
            f.write(f"{result}\n")
        p.execute()
        return modify_count

    def upload_data(self, table_path, local_file):
        cmd = "%s fs -mkdir -p %s" % (self.hadoop, table_path)
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        cmd = "%s fs -put -f %s %s" % (self.hadoop, local_file, self.upload_table_path_1)
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        cmd = f"{self.spark} -e \"alter table credit_data.prd_black_remove_record_detail_pdi add if not exists partition(pday={the_date},source=\'sms\')\""
        print(cmd)
        status, output = subprocess.getstatusoutput(cmd)
        if status != 0:
            print(f"数据上传失败：{str(status)},{str(output)}")
            raise Exception("数据上传失败")

    def run_task(self):
        try:
            self.data_landing(self.table_path, self.local_read_file)
            self.load_pika()
            self.upload_data(self.upload_table_path, self.local_write_file)
        except Exception as e:
            traceback.print_exc()
            message = f"【数智平台】借条短信黑名单执行失败,原因:{e},请及时排查问题"
            # self.alarm(message)
            sys.exit(1)


if __name__ == '__main__':
    the_date = sys.argv[1]
    load = LoadCtpAppData(the_date)
    load.run_task()