-- 初始化
INSERT overwrite TABLE credit_data.prd_global_sms_control_black_remove_real_record_pda PARTITION(pday = '20241016')
select  business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
from credit_data.prd_global_sms_control_black_remove_record_pda
where pday = '20241016';

-- 每天例行
INSERT overwrite TABLE credit_data.prd_global_sms_control_black_remove_real_record_pda PARTITION(pday = '${yesterday_p}')

select business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
from credit_data.prd_global_sms_control_black_remove_real_record_pda
where pday= '${last2Days_p}'

union all

select a.business_type,
    a.mobile_no_md5x,
    a.data_source,
    a.sms_source,
    a.black_type,
    a.add_time,
    a.behavior_time,
    a.remove_date,
    a.remove_time
from (
	select business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
	from credit_data.prd_global_sms_control_black_remove_record_pda
	where pday= '${yesterday_p}'
)a
left join (
	select business_type,
	mobile_no_md5x,
	data_source,
	add_time from credit_data.prd_global_sms_control_black_remove_real_record_pda
	where pday= '${last2Days_p}'
)b
on a.mobile_no_md5x=b.mobile_no_md5x and a.business_type=b.business_type  and a.data_source=b.data_source and a.add_time=b.add_time
where b.mobile_no_md5x is null;