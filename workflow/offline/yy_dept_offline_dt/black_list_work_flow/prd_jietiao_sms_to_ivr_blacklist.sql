INSERT OVERWRITE TABLE credit_data.prd_jietiao_sms_to_ivr_blacklist PARTITION (pday = '${yesterday_p}')
SELECT /*+ COALESCE(100) */
    t1.user_no,
    mobile_no_md5x,
    status,
    t1.date_updated,
    'jietiao' AS business_source,
    sub,
    type,
    level
FROM (
    SELECT y.user_no,
             x.mobile_no_md5x,
             status,
             x.date_updated,
             sub,
             type,
             level
      FROM (SELECT mobile_no_md5x,
                   IF(status = 1, 0, 1)  AS status,
                   TO_DATE(date_updated) AS date_updated,
                   '借条GNS短信'         AS sub,
                   'black_010'           AS type,
                   case
                     when date_updated < '2024-09-05 00:00:00' then 'P0' -- 历史数据默认为P0
                     when type in ('1', '2', '3', '7', '9') then 'P2'
                     when type = '5' then 'P0'
                   end as level
            FROM ods.prd_gns_im_sms_blacklist
            WHERE (type IS NOT NULL AND (
                        type IN ('7','5')
                    OR (type IN ('2', '3', '9', '1') AND date_updated >= '2024-04-01 00:00:00'))
                )
               OR (type IS NULL AND date_updated >= '2023-01-01 00:00:00')) x
               LEFT JOIN ods.prd_cis_u_user y
                         ON x.mobile_no_md5x = y.mobile_no_md5x
      WHERE status = 0

      UNION ALL

      SELECT y.user_no,
             y.mobile_no_md5x,
             0               AS status,
             x.date_updated,
             '上海触达回复T' AS sub,
             'black_009'     AS type,
             case when x.date_updated < '2024-09-05 00:00:00' then 'P0' else 'P1' end as level -- 历史数据默认为P0
      FROM (SELECT to_date(from_unixtime(create_time_stamp / 1000, 'yyyy-MM-dd HH:mm:ss')) AS date_updated,
                   mobile_no
            FROM ods.prd_ctp_app_c_task_black_list
            WHERE channel = 'sms'
              AND subject IN ('JIETIAO', '')
              AND from_unixtime(create_time_stamp / 1000, 'yyyy-MM-dd HH:mm:ss') >= '2024-04-01 00:00:00') x
               JOIN ods.prd_cis_u_user y ON x.mobile_no = y.mobile_no_md5x
) t1;


--INSERT OVERWRITE TABLE credit_data.prd_jietiao_sms_to_ivr_blacklist_supplement PARTITION (pday = '${yesterday_p}')
--SELECT t2.user_no,
--       t2.mobile_no_md5x,
--       0            AS status,
--       CURRENT_DATE AS date_updated,
--       'jietiao'    AS business_source,
--       'id_no撞库'  AS sub,
--       type
--FROM (SELECT x.user_no,
--             type,
--             y.id_no_md5x
--      FROM (SELECT user_no, type
--            FROM credit_data.prd_jietiao_sms_to_ivr_blacklist
--            WHERE pday = '${yesterday_p}'
--              AND user_no LIKE 'UR%'
--              AND level = 'P0'
--            GROUP BY user_no, type) x
--               JOIN (SELECT *
--                     FROM credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_userno
--                     WHERE pday = '${yesterday_p}') y
--                    ON x.user_no = y.user_no) t1
--         JOIN (SELECT *
--               FROM credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_userno
--               WHERE pday = '${yesterday_p}') t2
--              ON t1.id_no_md5x = t2.id_no_md5x
--WHERE t1.user_no != t2.user_no;

INSERT OVERWRITE TABLE credit_data.prd_jietiao_sms_to_ivr_blacklist_supplement PARTITION (pday = '${yesterday_p}')
SELECT t2.user_no,
    t2.mobile_no_md5x,
    0            AS status,
    CURRENT_DATE AS date_updated,
    'jietiao'    AS business_source,
    'id_no撞库'  AS sub,
    type
FROM (
    SELECT x.user_no,
          type,
          y.id_no_md5x
      FROM (
            SELECT user_no, type
            FROM credit_data.prd_jietiao_sms_to_ivr_blacklist
            WHERE pday = '${yesterday_p}'
              AND user_no LIKE 'UR%'
            GROUP BY user_no, type
          ) x
          JOIN (
            SELECT *
                 FROM credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_userno
                 WHERE pday = '${yesterday_p}'
             ) y
             ON x.user_no = y.user_no
       ) t1
       JOIN (
        SELECT *
               FROM credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_userno
               WHERE pday = '${yesterday_p}'
        ) t2
        ON t1.id_no_md5x = t2.id_no_md5x
        join(
         SELECT *,
               row_number() over(partition by user_no order by operate_time desc) as rn
               FROM credit_data.prd_level_black_tag_id_no
               WHERE pday = '${yesterday_p}'
               having rn =1
        )xx
    on xx.user_no=t2.user_no
    WHERE t1.user_no != t2.user_no;