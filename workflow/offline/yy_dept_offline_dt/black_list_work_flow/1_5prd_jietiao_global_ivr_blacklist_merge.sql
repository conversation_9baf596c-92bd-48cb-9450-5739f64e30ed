--INSERT overwrite TABLE credit_data.test_jietiao_global_ivr_blacklist_merge_lx PARTITION(pday = '${yesterday_p}')
INSERT overwrite TABLE credit_data.prd_jietiao_global_ivr_blacklist_merge PARTITION(pday = '${yesterday_p}')
SELECT ibm.user_no,
       concat_ws(',', collect_set(ibm.type))        AS flag,
       MAX(ibm.flag_loss_status)                    AS flag_loss_status,
       concat_ws(',', collect_set(ibm.data_source)) AS data_source,
       MAX(ibm.date_updated)                        AS date_updated,
       MIN(ibm.add_time)                            AS add_time,
       MIN(ibm.release_time)                        AS release_time,
       MIN(ibm.status)                              AS status,
       concat_ws(',', collect_set(ibm.black_type))  AS black_type
FROM (
    SELECT t.user_no,
             t.type,
             t.flag_loss_status,
             t.data_source,
             t.date_updated,
             t.add_time,
             t.release_time,
             t.status,
             t.black_type
      FROM (
        SELECT user_no,
                   type,
                   flag_loss_status,
                   data_source,
                   date_updated,
                   add_time,
                   release_time,
                   status,
                   'from_ivr_blacklist' AS black_type
            FROM (
                SELECT *
--                  FROM credit_data.test_jt_label_user_black_list_data_merge_supplement_lx
                   FROM credit_data.jt_label_user_black_list_data_merge_supplement
                  WHERE pday = '${yesterday_p}'
           ) lateral VIEW explode(split(flag,'\\|')) temp AS TYPE

            UNION ALL

            SELECT
                user_no,
                TYPE,
                'Y' AS flag_loss_status,
                '短信黑名单' AS data_source,
                date_updated AS date_updated,
                date_updated AS add_time,
                -1 AS release_time,
                0 AS status,
                'from_sms_blacklist' AS black_type
            FROM credit_data.prd_jietiao_sms_to_ivr_blacklist
            WHERE pday = '${yesterday_p}' AND user_no LIKE 'UR%'

            UNION ALL

           SELECT
               x.user_no,
               x.key,
               x.flag_loss_status,
               x.data_source,
               x.date_updated,
               x.add_time,
               x.release_time,
               x.status,
               '历史差异数据' AS black_type
           from (
             select
                user_no,
                black_type_list as key,
                'Y' as flag_loss_status,
                '历史差异数据' as data_source,
                date_updated,
                add_time,
                release_time,
                0 as status
            from credit_data.prd_jietiao_global_ivr_blacklist_redis_diff
            where pday= (SELECT max(pday) from credit_data.prd_jietiao_global_ivr_blacklist_redis_diff where pday>'')
           ) x
           left join (
            select
                user_no,case when add_time is null then update_time else add_time end as add_time,update_time
            from (
                select
                    mobile_md5x as user_no,
                    operate_status,
                    update_time,
                    get_json_object(black_value,'$.addTime') as add_time,
                    row_number() over(partition by mobile_md5x order by update_time desc) as rn
                from ods.prd_data_service_srds_sms_black_record
                where  business_type = 'jietiao' and black_type in ('app_voice','voice') and mobile_md5x like 'UR%'
                having rn = 1 and operate_status = '0'
            )
           )y
            on x.user_no = y.user_no
            -- 解决白天跑工作流覆盖导致无法解除黑名单问题，2023-08-18同步被改为增量修复了该问题
            where y.user_no is null or (y.update_time>='2023-08-18' and  x.date_updated > y.update_time) or y.update_time<'2023-08-18'

            UNION ALL

            SELECT
                user_no,
                TYPE,
                'Y' AS flag_loss_status,
                '短信黑名单id_no撞库' AS data_source,
                '2025-07-17 03:38:11' AS date_updated,
                date_updated AS add_time,
                -1 AS release_time,
                0 AS status,
                'from_sms_blacklist' AS black_type
            FROM credit_data.prd_jietiao_sms_to_ivr_blacklist_supplement
            -- 20250717 上线之后,相同身份证加黑不需要 gns(black_010)、ctp(black_009)
--            WHERE pday = '20250624'
             WHERE pday = '20250716'
            AND user_no LIKE 'UR%'
      ) t
      LEFT JOIN (
        SELECT user_no,MAX(remove_time) AS remove_time
        FROM credit_data.prd_ivr_black_remove_record_pda
        WHERE pday = '${yesterday_p}' GROUP BY user_no
      ) irp
      ON t.user_no = irp.user_no
      WHERE irp.user_no IS NULL OR t.add_time > irp.remove_time
 ) ibm
GROUP BY user_no