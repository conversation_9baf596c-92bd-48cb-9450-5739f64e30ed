ALTER TABLE credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_mobilemd5x DROP IF EXISTS PARTITION (pday = '${last2Days_p}');

insert overwrite table credit_data.temp_jietiao_global_sms_blacklist_multiple_idno_mobilemd5x partition(pday = '${yesterday_p}')
select
    user_no,id_no_md5x,mobile_no_md5x
from (
    select
        user_no,id_no_md5x,mobile_no_md5x,
        row_number() over(partition by id_no_md5x,mobile_no_md5x order by date_finished desc) as rn
    from ods.prd_apv_ap_apply
    where id_no_md5x in (
        select id_no_md5x
        from ods.prd_apv_ap_apply
        where id_no_md5x is not null and id_no_md5x != 'null'
        group by id_no_md5x
        having count(distinct mobile_no_md5x) > 1
    )
    having rn = 1
);
