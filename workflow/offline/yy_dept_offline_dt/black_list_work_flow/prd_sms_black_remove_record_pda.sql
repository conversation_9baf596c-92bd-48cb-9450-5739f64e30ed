-- yy_dept_offline_dt -> black_list_work_flow
-- 预约注销用户取消注销后，短信黑明单管制自动取消
-- 每天跑一次
-- 1、合在表 prd_sms_black_remove_record_pda 还是 放在生成的地方直接排除掉?
-- 2、要释放的话,还真的只能放在 prd_sms_black_remove_record_pda 表中
-- 使用到的地方
-- credit_data.prd_jietiao_gns_sms_blacklist_merge
-- credit_data.prd_jietiao_global_sms_blacklist_merge

-- INSERT overwrite TABLE credit_data.test_sms_black_remove_record_pda_lx PARTITION(pday = '${yesterday_p}')
INSERT overwrite TABLE credit_data.prd_sms_black_remove_record_pda PARTITION(pday = '${yesterday_p}')

-- 原始移除表
select
    business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
from credit_data.prd_sms_black_remove_record_pda where pday='20230801'

union all 

-- 全局短信管控移除表
select
    business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
-- from credit_data.test_global_sms_control_black_remove_record_pda_lx where pday= '${yesterday_p}'
from credit_data.prd_global_sms_control_black_remove_real_record_pda where pday= '${yesterday_p}'

union all 

-- gns短信管控移除表
select
    business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
-- from credit_data.test_gns_sms_control_black_remove_record_pda_lx where pday= '${yesterday_p}'
from credit_data.prd_gns_sms_control_black_remove_real_record_pda where pday= '${yesterday_p}'

union all

-- 新增手动移除表
select
    business_type,
    mobile_no_md5x,
    data_source,
    sms_source,
    black_type,
    add_time,
    behavior_time,
    remove_date,
    remove_time
-- from credit_data.test_gns_sms_control_black_remove_record_pda_lx where pday= '${yesterday_p}'
from credit_data.prd_sms_blacklist_release_detail_pdi where pday>''
and mobile_no_md5x is not null

