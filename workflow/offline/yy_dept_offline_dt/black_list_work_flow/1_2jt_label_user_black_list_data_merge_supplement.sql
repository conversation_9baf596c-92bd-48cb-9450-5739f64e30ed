--insert overwrite table credit_data.test_jt_label_user_black_list_data_merge_supplement_lx partition(pday = '${yesterday_p}')
insert overwrite table credit_data.jt_label_user_black_list_data_merge_supplement partition(pday = '${yesterday_p}')
select
    user_no,
    flag,
    flag_loss_status,
    data_source,
    date_updated,
    add_time,
    release_time,
    status,
    system_code
--from credit_data.test_jt_label_user_black_list_data_merge_lx
 from credit_data.jt_label_user_black_list_data_merge
where pday = '${yesterday_p}'

union all

-- 20250717 之前按照p0加黑 这里要改一下,老逻辑会把除 P0 以外的身份证相同的都加黑

select user_no,
    flag,
    flag_loss_status,
    data_source,
    date_updated,
    add_time,
    release_time,
    status,
    system_code
from
--credit_data.test_jt_label_user_black_list_data_merge_supplement_lx
--where pday='20250624' and system_code='id_no_add_black'
 credit_data.jt_label_user_black_list_data_merge_supplement
 where pday='20250716' and system_code='id_no_add_black'

union all

-- 20250717 之后相同身份证按照新逻辑关联加黑
-- jt_label_user_black_list_data_merge 这里不能用进行判断,add_time取的是max,flag是一个组合,不准确
select distinct
    m.user_no,
    flag,
    'Y' as flag_loss_status,
    'id_no撞库' as data_source,
    date_updated,
    add_time,
    release_time,
    0 as status,
   'id_no_add_black' as system_code
from (
    select
        x.user_no,
        id_no_md5x,
        black_key as flag,
        flag_loss_status,
        data_source,
        date_updated,
        date_updated as add_time,
        -1 as release_time,
        0 as status
    from (
        select *,
            row_number () over(partition by user_no order by date_updated desc) as rn
        from credit_data.prd_jietiao_ivr_id_no_source_blacklist
        where pday = '${yesterday_p}'
        having rn=1
    )x
    join (
        select
            user_no,id_no_md5x,
            row_number() over(partition by user_no order by date_finished desc) as rn
        from ods.prd_apv_ap_apply
        where id_no_md5x is not null  and id_no_md5x !='null'
        having rn = 1
    ) y
    on x.user_no = y.user_no
)z
join (
    select
        user_no,id_no_md5x,
        row_number() over(partition by user_no order by date_finished desc) as rn
    from ods.prd_apv_ap_apply
    having rn = 1
)m
on z.id_no_md5x = m.id_no_md5x
where z.user_no != m.user_no