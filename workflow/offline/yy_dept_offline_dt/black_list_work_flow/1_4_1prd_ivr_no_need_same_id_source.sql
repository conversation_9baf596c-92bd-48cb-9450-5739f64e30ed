-- 相同身份证加黑升级,之前黑名单不需要再关联加黑原始数据
INSERT OVERWRITE TABLE credit_data.prd_ivr_no_need_same_id_source PARTITION (pday = '${yesterday_p}')

select
    a.user_no as no_need_add_userno,
	e.user_no as no_need_add_relate_idno_userno,
	a.add_time as date_updated,
	a.flag as black_type_list
 from (
    -- 以前是全量添加,5100w+
    select
        user_no,flag,flag_loss_status,data_source,date_updated,add_time,release_time,status
    from credit_data.jt_label_user_black_list_data_merge
    where pday = '${yesterday_p}'
)a
left join (
    -- c 表 16w+(主要原因还是c表,排除了Z,Z就占了 1967w+)
    select * from credit_data.prd_jietiao_ivr_id_no_source_blacklist
    where pday= '${yesterday_p}'
)c
on a.user_no=c.user_no
join (
    select
        user_no,id_no_md5x,
        row_number() over(partition by user_no order by date_finished desc) as rn
    from ods.prd_apv_ap_apply
    where id_no_md5x is not null  and id_no_md5x !='null'
    having rn = 1
) d
on a.user_no=d.user_no
join (
    select
        user_no,id_no_md5x,
        row_number() over(partition by user_no order by date_finished desc) as rn
    from ods.prd_apv_ap_apply
    having rn = 1
)e
on e.id_no_md5x=d.id_no_md5x
join(
    -- P0 打标等级
    -- a b 交集 1055w+
    -- 以前只加黑 黑名单等级P0的数据
    select
        user_no,id_no_md5x,
        row_number() over(partition by user_no order by operate_time desc) as rn
    -- 这个地方还是需要继续使用老的
    from credit_data.prd_level_black_tag_id_no_cal_diff_use
    where pday= '${yesterday_p}'
    having rn = 1
)b
on e.user_no=b.user_no
where c.user_no is null
and a.user_no!=e.user_no