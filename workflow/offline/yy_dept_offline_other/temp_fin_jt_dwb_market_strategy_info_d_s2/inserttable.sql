-------------------------------------------------------------------------------
-- file name     : temp_fin_jt_dwb_market_strategy_info_d_s2.sql
-- author        : luoxin
-- version       : v1.0
-- date          : 2022-08-19
-- copyright     : @360shuke
-- description   : 迁移safe3授信数仓表见：temp_fin_jt_dwb_market_strategy_info_d_s2
-- usage         : 迁移safe3授信数仓表见：temp_fin_jt_dwb_market_strategy_info_d_s2
-- function list : 数仓表
-- history       : 第一版脚本，无历史
-- 依赖工作流     : jt_dim.fin_jt_dim_market_strategy_info


/***
   0、表命名按照数仓建设命名
 * 1、数仓表中变量替换为毓数工作流支持变量
 * 2、表名替换为毓数标签棫统一数仓规范.
 * 3、标签依赖写入依赖表，填写对应得ods表或者dwd表工作流依赖
  */
-------------------------------------------------------------------------------


INSERT OVERWRITE TABLE credit_data.temp_fin_jt_dwb_market_strategy_info_d_s2 PARTITION (pday='${yesterday_p}')
SELECT plan_id,
    plan_name,
    strategy_id,
    strategy_name,
    comp_person_num,
    task_status,
    person_num,
    person_name,
    bag_class
 FROM fin_dim.dim_cdplabel_td_market_strategy_info_pda
 where pday='${yesterday_p}'
 GROUP BY plan_id,
    plan_name,
    strategy_id,
    strategy_name,
    comp_person_num,
    task_status,
    person_num,
    person_name,
    bag_class;