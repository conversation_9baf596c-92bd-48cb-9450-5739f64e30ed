-- create_table 创建表
CREATE TABLE IF NOT EXISTS credit_data.prd_telemkt_tmk_call_record_180day(
    jid bigint,
    id string COMMENT '编号',
    user_id string COMMENT '处理坐席',
    roster_cust_id string COMMENT '外呼名单任务客户id',
    job_no string COMMENT '处理坐席的工号',
    call_no string COMMENT '主叫号码',
    called_no string COMMENT '被叫号码',
    call_sheet_id string COMMENT '通话ID',
    call_type string COMMENT '通话类型：dialout外呼通话，normal普通来电，transfer转接电话，dialtransfer外呼转接',
    record_file string COMMENT '通话录音文件名',
    file_server string COMMENT '录音文件服务其地址',
    call_state string COMMENT '事件状态：Ring, Ringing, Link, Hangup(Unlink也当成Hangup处理)',
    talk_time bigint COMMENT '通话时长 秒',
    ring_time bigint COMMENT '振铃时长 秒',
    param string COMMENT '参数',
    local_file string COMMENT '本地文件存储路径',
    state string COMMENT '通话记录状态 13.已接听[dealing]，振铃未接听[notDeal]，已留言[voicemail]，黑名单[blackList]，排队放弃[queueLeak]，ivr [leak]' ,
    ring string COMMENT '通话振铃时间',
    begin string COMMENT '通话接通时间',
    end string COMMENT '通话结束时间',
    company_name string COMMENT '外呼厂商公司名',
    create_by string COMMENT '创建者',
    create_date string COMMENT '创建时间',
    update_by string COMMENT '更新者',
    update_date string COMMENT '更新时间',
    remarks string COMMENT '备注信息',
    del_flag string COMMENT '删除标记',
    product_name string COMMENT '授信产品类型语音质检用',
    called_no_md5x string,
    called_no_encryptx string,
    call_id string COMMENT '坐席外呼ID',
    cross_sale_flag string COMMENT '是否协销',
    project_id string COMMENT '项目ID',
    emotion_label string COMMENT '情绪标签：1中性-沟通意愿度、2中性-稍后去电、3负向-抱怨投诉、4负向-不再致电'
)
COMMENT '通话记录180天数据'
STORED AS textfile;