with
last_login as(
    -- 新增T-1最近一次登录时间
    select user_no,time_login,row_number()over(partition by user_no order by time_login desc)as rn
    from fin_dw.dwd_user_login_operate_pdi
    where pday>=''
    having rn=1
),
last_45_login as(
    -- 新增T-1最近一次登录时间
    select user_no,time_login,row_number()over(partition by user_no order by time_login desc)as rn
    from fin_dw.dwd_user_login_operate_pdi
    where pday>='20240301'
      and time_login >= date_format(date_sub(current_date,45),'yyyy-MM-dd HH:mm:ss')
    having rn=1
),
push_partner_data_t30add as(
        -- 推送用户数据,最近45天
        SELECT batch_no,
               sub_batch_no,
               user_no,
               phone_no_encryptx,
               channel_no,
               serial_no,
               date_created,
               row_number()over(partition by channel_no,user_no order by date_created DESC) as rn
        from ods.prd_ots_ots_robot_artificial_call_reach_record
        where pday >= date_format(date_sub(current_date,45),'yyyyMMdd')
          and is_delete=0
          and status=1
          and operate_scene not in ('highQualityCus','mediumQualityCus','lowQualityCus')
        having rn=1
),
push_partner_data_t30subtract as(
        -- 推送用户数据,最近45天
        SELECT batch_no,
               sub_batch_no,
               user_no,
               phone_no_encryptx,
               channel_no,
               serial_no,
               date_created,
               row_number()over(partition by channel_no,user_no order by date_created DESC) as rn
        from ods.prd_ots_ots_robot_artificial_call_reach_record
        where pday >= date_format(date_sub(current_date,45),'yyyyMMdd')
          and is_delete=0
          and status=1
          and operate_scene in ('highQualityCus','mediumQualityCus','lowQualityCus')
        having rn=1
),
t30add as(
        -- 业务sql
        select
            main.user_no
             ,substring(appl_finished_time,0,19)  as date_appl_submit -- 完件时间
             ,date_credit_succ -- 授信时间
             ,fqdz_user_cnt_t0
             ,dz_user_cnt_t0
             ,dz_amt_t0
             ,case when cbd.user_no is not null then "1" else "0" end as cwj_grp
        from (
                 select main.user_no
                      ,appl_finished_time-- 完件时间
                      ,case when risk_price_class in ('P18','P24','新P36') then date_credit_succ else null end as date_credit_succ-- 授信时间
                      ,case when risk_price_class in ('P18','P24','新P36') then fqdz_user_cnt_t0 else 0 end as fqdz_user_cnt_t0
                      ,case when risk_price_class in ('P18','P24','新P36') then dz_user_cnt_t0 else 0 end as dz_user_cnt_t0
                      ,case when risk_price_class in ('P18','P24','新P36') then dz_amt_t0 else null end as dz_amt_t0
                 from(
                         select main.user_no,
                                register_date,
                                mobile_no,
                                mobile_no_md5x,
                                case when mobile_no_md5x is not null then mobile_no_md5x else mobile_no end mobile_no_fix
                         from (
                                  select user_no,
                                      date(register_time) as register_date,mobile_no
                             from fin_dw_zz.dwt_user_no_p5_reg_to_wj_sx_a
                         where reg_channel_class in ('APP','H5')-- 排除地推api注册用户
                     )main
                         left join (
                     select user_no,
                            mobile_no_md5x
                     from fin_dim.dim_m_user_no_mobile_abandon_log_jwm_pdi
                     group by 1,2
                 )t9
                on main.user_no=t9.user_no
             )main -- 含易户逻辑的数据
            inner join (
            select mobile_no_md5x
                 ,appl_no
                 ,product_code
                 ,appl_finished_time-- 完件时间
                 ,credit_channel_class-- 完件端
                 ,date_credit_succ-- 授信时间
                 ,sx_amt_1st_prd-- 首授信额度
                 ,fqdz_user_cnt_t0
                 ,fqdz_amt_t0
                 ,dz_user_cnt_t0
                 ,dz_amt_t0
                 ,fqdz_user_cnt_m0
                 ,fqdz_amt_m0
                 ,dz_user_cnt_m0
                 ,dz_amt_m0
                 ,risk_price_class
                 ,row_number()over(partition by mobile_no_md5x order by appl_finished_time asc) as rn
            from fin_dw_zz.dwt_user_no_p5_funnel_login_to_draw_data_1_4_pdi
            where if_first_wj=1 and pday>='20160101' and credit_channel_class in ('APP','H5')
        )wj
        on main.mobile_no_fix=wj.mobile_no_md5x and wj.rn=1
    )main
    left join (
        select user_no,time_submit from (
            select user_no,time_submit
            from fin_dim.dim_m_appl_req_no_cwj_tag_jwm_pdi
            where pday >='20240301' and u_linked+u_long_called+u_call_app_login>0
            group by 1,2
        union all
            select user_no_original as user_no,time_submit
            from fin_dim.dim_m_appl_req_no_cwj_tag_jwm_pdi
            where pday >='20240301' and u_linked+u_long_called+u_call_app_login>0
            group by 1,2
        )t1
        group by 1,2
    )cbd
    on main.user_no=cbd.user_no and date(main.appl_finished_time)=date(cbd.time_submit)
),
t30subtract(
        select
            distinct t1.user_no
            ,t1.first_wj_time -- 首完件时间
            ,t1.credit_time -- 授信时间
        from (
            select distinct user_no
            ,first_wj_time
            ,case when first_wj_time is not null then credit_time else null end as credit_time
            from fin_dw_zz.dwt_user_no_p5_reg_to_wj_sx_a -- P5首授信逻辑
            where reg_channel_class in ('APP','H5')
            and  date(register_time)>='2024-03-31'
        ) t1
        where (t1.first_wj_time is not null or t1.first_wj_time!='') or (t1.credit_time is not null or t1.credit_time!='')
)


-- INSERT INTO TABLE credit_data.test_ots_man_machine_effect_sync_pdi partition(pday='${yesterday_p}')
INSERT INTO TABLE credit_data.ots_man_machine_effect_sync_pdi partition(pday='${yesterday_p}')

select
    tj1.batch_no,
    tj1.sub_batch_no,
    tj1.user_no,
    tj1.phone_no_encryptx,
    tj1.channel_no,
    tj1.unique_no,
    tj1.date_appl_submit,
    tj1.date_credit_succ,
    tj1.fqdz_user_cnt_t0,
    tj1.dz_user_cnt_t0,
    tj1.dz_amt_t0,
    tj1.cwj_grp,
    tj1.reach_time,
    tj1.date_created,
    tj1.cluster_type,
    tj1.time_login
from(
        select  batch_no,
                sub_batch_no,
                user_no,
                phone_no_encryptx,
                channel_no,
                unique_no,
                substring(date_appl_submit,0,19) as date_appl_submit,
                substring(date_credit_succ,0,10) as date_credit_succ,
                fqdz_user_cnt_t0,
                dz_user_cnt_t0,
                dz_amt_t0,
                cwj_grp,
                reach_time,
                date_created,
                cluster_type,
                time_login,
                row_number()over(partition by unique_no order by time_login desc,date_appl_submit desc) as rn
        from (
                 -- t30+ 登录,完件数据
                 select udata.batch_no,
                        udata.sub_batch_no,
                        udata.user_no,
                        udata.phone_no_encryptx,
                        udata.channel_no,
                        udata.serial_no as unique_no,
                        if(business.date_appl_submit is not null and business.date_appl_submit!='',business.date_appl_submit,'') as date_appl_submit,
                        if(business.date_credit_succ is not null and business.date_credit_succ!='',business.date_credit_succ,'') as date_credit_succ,
                        if(business.fqdz_user_cnt_t0 is not null and business.fqdz_user_cnt_t0 > '0', 'Y','N') as fqdz_user_cnt_t0,
                        if(business.dz_user_cnt_t0 is not null and business.dz_user_cnt_t0 > '0', 'Y','N') as dz_user_cnt_t0,
                        if(business.dz_amt_t0 is not null, business.dz_amt_t0, '0') as dz_amt_t0,
                        if(business.cwj_grp is not null and business.cwj_grp!='', business.cwj_grp,'') as cwj_grp,
                        udata.date_created as reach_time,
                        now() as date_created,
                        case when hash(udata.phone_no_encryptx) %2 ==0 then 'a' else 'b' end as cluster_type,
                        substring(ulogin.time_login,0,19) as time_login
                 from t30add business
                          inner join push_partner_data_t30add udata
                                     on udata.user_no = business.user_no
                          left join last_login ulogin
                                    on business.user_no = ulogin.user_no
                 where (substring(business.date_appl_submit,0,10) >= substring(udata.date_created,0,10)) or (substring(business.date_credit_succ,0,10) >=  substring(udata.date_created,0,10))

                 union all

                 -- t30+ 最近45天登录未完件
                 select udata.batch_no,
                        udata.sub_batch_no,
                        udata.user_no,
                        udata.phone_no_encryptx,
                        udata.channel_no,
                        udata.serial_no as unique_no,
                        '' as date_appl_submit,
                        '' as date_credit_succ,
                        'N' as fqdz_user_cnt_t0,
                        'N' as dz_user_cnt_t0,
                        '0' as dz_amt_t0,
                        '0' as cwj_grp,
                        udata.date_created as reach_time,
                        now() as date_created,
                        case when hash(udata.phone_no_encryptx) %2 ==0 then 'a' else 'b' end as cluster_type,
                        substring(ulogin.time_login,0,19) as time_login
                 from last_45_login ulogin
                          inner join push_partner_data_t30add udata
                                     on udata.user_no = ulogin.user_no
                          left join t30add business
                                    on ulogin.user_no = business.user_no
                 where business.user_no is null

                 union  all

                 -- t30- 登录,完件数据
                 select udata.batch_no,
                        udata.sub_batch_no,
                        udata.user_no,
                        udata.phone_no_encryptx,
                        udata.channel_no,
                        udata.serial_no as unique_no,
                        if(business.first_wj_time is not null and business.first_wj_time!='',business.first_wj_time,'') as date_appl_submit,
                        if(business.credit_time is not null and business.credit_time!='',business.credit_time,'') as date_credit_succ,
                        'N' as fqdz_user_cnt_t0,
                        'N' as dz_user_cnt_t0,
                        '0' as dz_amt_t0,
                        '0' as cwj_grp,
                        udata.date_created as reach_time,
                        now() as date_created,
                        case when hash(udata.phone_no_encryptx) %2 ==0 then 'a' else 'b' end as cluster_type,
                        substring(ulogin.time_login,0,19) as time_login
                 from t30subtract business
                          inner join push_partner_data_t30subtract udata
                                     on udata.user_no = business.user_no
                          left join last_login ulogin
                                    on business.user_no = ulogin.user_no
                 where (substring(business.first_wj_time,0,10) >= substring(udata.date_created,0,10)) or (substring(business.credit_time,0,10) >=  substring(udata.date_created,0,10))

                 union all

                 -- t30- 最近45天登录未完件
                 select udata.batch_no,
                        udata.sub_batch_no,
                        udata.user_no,
                        udata.phone_no_encryptx,
                        udata.channel_no,
                        udata.serial_no as unique_no,
                        '' as date_appl_submit,
                        '' as date_credit_succ,
                        'N' as fqdz_user_cnt_t0,
                        'N' as dz_user_cnt_t0,
                        '0' as dz_amt_t0,
                        '0' as cwj_grp,
                        udata.date_created as reach_time,
                        now() as date_created,
                        case when hash(udata.phone_no_encryptx) %2 ==0 then 'a' else 'b' end as cluster_type,
                        substring(ulogin.time_login,0,19) as time_login
                 from last_45_login ulogin
                          inner join push_partner_data_t30subtract udata
                                     on udata.user_no = ulogin.user_no
                          left join t30subtract business
                                    on ulogin.user_no = business.user_no
                 where business.user_no is null
             )
        having rn=1
    )tj1
left join(
    select
        unique_no
         ,time_login
         ,date_credit_succ
         ,date_appl_submit
         ,row_number()over(partition by unique_no order by date_created desc) as rn
    -- from credit_data.test_ots_man_machine_effect_sync_pdi
    from credit_data.ots_man_machine_effect_sync_pdi
    where pday < '${yesterday_p}'
    having rn=1
)tj2
on tj1.unique_no=tj2.unique_no
where tj2.unique_no is null or (tj2.date_credit_succ='' and (tj1.time_login>tj2.time_login or tj1.date_credit_succ>tj2.date_credit_succ or tj1.date_appl_submit>tj2.date_appl_submit))
