truncate table credit_data.prd_gns_push_push_notice_today_180day;
insert overwrite table credit_data.prd_gns_push_push_notice_today_180day
select 
    user_no        
    ,msg_id         
    ,registration_id
    ,result         
    ,pn_no          
    ,date_gen       
    ,template_name 
from 
    ods.prd_gns_push_push_notice_today
where 
    date_format(date_created, 'yyyy-MM-dd') >= date_sub(now(), 180);