-- create_table 创建表
CREATE TABLE IF NOT EXISTS credit_data.prd_telemkt_tmk_auto_call_history_180day(
    id bigint COMMENT '',
    batch_number string COMMENT '批次号',
    mobile string COMMENT '手机号',
    label string COMMENT '意向标签',
    vendor string COMMENT '呼叫厂商',
    task_id string COMMENT '慧捷任务id',
    file_url string COMMENT '录音地址',
    call_time_length bigint COMMENT '通话时长',
    start_time string COMMENT '拨打时间',
    answer_time string COMMENT '接听时间',
    end_time string COMMENT '通话结束时间',
    create_by string COMMENT '创建者',
    create_date string COMMENT '创建时间',
    update_date string COMMENT '更新时间',
    cust_name string COMMENT '客户姓名',
    ai_content string COMMENT '音频转文本',
    model_id string COMMENT '话术编号',
    mobile_md5x string COMMENT '',
    mobile_encryptx string COMMENT '',
    cust_name_md5x string COMMENT '',
    cust_name_encryptx string COMMENT '',
    ring_time string COMMENT '振铃时长'
)
COMMENT '机器人自动外呼历史记录180天数据'
STORED AS textfile;
