--INSERT INTO TABLE credit_data.test_ots_touch_effect_sync_pdi PARTITION(pday = '${yesterday_p}')
  INSERT INTO TABLE credit_data.ots_touch_effect_sync_pdi PARTITION(pday = '${yesterday_p}')

select  t.batch_no,-- 批次号
        t.sub_batch_no,-- 子批次号
        t.user_no,-- 用户号
        t.mobile_no_encryptx,-- aes加密手机号
        t.agent_operator,-- 供应商
        t.unique_no,-- 唯一标识ID
        t.date_appl_submit,-- 完件时间
        t.date_credit_succ, -- 授信时间
        t.dz_user_cnt_t0 ,-- T0是否动支
        now() as date_created,-- 当前时间
        case when hash(t.mobile_no_encryptx) %2 ==0 then 'a'
             else 'b' -- 集群
            end as cluster_type,
        substring(t.time_login,0,19) as last_login_time, -- 登录时间
        t.sx_success, -- 授信是否成功
        t.operation_scene,
        t.mobile_no_encryptx_msf,
        t.credit_cut_off_node,
        t.is_attribution
from (
         select
             batch_no,
             sub_batch_no,
             user_no,
             mobile_no_encryptx,
             agent_operator,
             unique_no,
             substring(date_appl_submit,0,19) as date_appl_submit,
             date_credit_succ,
             dz_user_cnt_t0,
             time_login,
             sx_success,
             operation_scene,
             mobile_no_encryptx_msf,
             credit_cut_off_node,
             is_attribution,
             row_number()over(partition by unique_no order by time_login desc,date_appl_submit desc) as rn
         from(
                 -- t30+ 已完件,授信或未授信
                 select * from credit_data.prd_mid_touch_effect_sync_pdi
                 where pday = '${yesterday_p}' and type='t30add' and completion_type='Y'

                 union all

                 -- t30+ 最近45天登录 未完件
                 select * from credit_data.prd_mid_touch_effect_sync_pdi
                 where pday = '${yesterday_p}' and type='t30add' and completion_type='N'

                 union all

                 -- t30- 已完件,授信或未授信
                 select * from credit_data.prd_mid_touch_effect_sync_pdi
                 where pday = '${yesterday_p}' and type='t30subtract' and completion_type='Y'

                 union all

                 -- t30- 最近45天登录 未完件
                select * from credit_data.prd_mid_touch_effect_sync_pdi
                where pday = '${yesterday_p}' and type='t30subtract' and completion_type='N'

                union all

                 -- 拒绝授信 已完件,授信或未授信
                 select * from credit_data.prd_mid_touch_effect_sync_pdi
                 where pday = '${yesterday_p}' and type='reject_credit' and completion_type='Y'

                 union all

                 -- 拒绝授信 最近45天登录 未完件
                 select * from credit_data.prd_mid_touch_effect_sync_pdi
                 where pday = '${yesterday_p}' and type='reject_credit' and completion_type='N'
             )
         having rn=1
)t
left join (
    -- 增量表,所以这里不进行限制
    select
        unique_no
         ,sx_success
         ,date_appl_submit
         ,last_login_time
         ,is_attribution
         ,row_number()over(partition by unique_no order by date_created desc) as rn
--    from credit_data.test_ots_touch_effect_sync_pdi
     from credit_data.ots_touch_effect_sync_pdi
    where  pday < '${yesterday_p}'
    having rn=1
)y
on t.unique_no=y.unique_no
where y.unique_no is null
    or
    (
        -- 非t30+场景,维持不变
        (t.operation_scene!='credit' and y.sx_success='N' and (t.time_login>y.last_login_time or t.sx_success!=y.sx_success or t.date_appl_submit>y.date_appl_submit))
        or
        -- t30+场景 改为归因口径
        (
         t.operation_scene='credit'
         and (
                ( ((y.is_attribution='N' and y.sx_success='N') or y.sx_success='N') and (t.time_login>y.last_login_time or t.sx_success!=y.sx_success or t.is_attribution!=y.is_attribution or t.date_appl_submit>y.date_appl_submit))
                or
                (t.is_attribution='Y' and CURRENT_DATE()='2025-06-13')
             )
        )
    )
