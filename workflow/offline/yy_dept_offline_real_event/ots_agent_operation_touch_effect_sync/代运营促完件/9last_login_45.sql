INSERT overwrite TABLE credit_data.prd_user_last_login PARTITION(pday = '${yesterday_p}', type = 'last45')
select
    user_no,
    time_login
from (
    select 
        user_no,
        time_login,
        row_number()over(partition by user_no order by time_login desc)as rn
    from fin_dw.dwd_user_login_operate_pdi
    where pday>='20230801'
        and time_login >= date_format(date_sub(current_date,45),'yyyy-MM-dd HH:mm:ss')
    having rn=1
)