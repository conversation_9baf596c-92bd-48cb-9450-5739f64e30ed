INSERT overwrite TABLE credit_data.prd_agent_finish_data PARTITION(pday = '${yesterday_p}',type = 't30subtract')
select
    distinct t1.user_no
    ,'' as mobile_no_md5x
    ,t1.first_wj_time as appl_finished_time -- 首完件时间
    ,t1.credit_time as date_credit_succ -- 授信时间
from (
    select distinct user_no
        ,first_wj_time
        ,case when risk_price_class in ('P18','P24','新P36') and first_wj_time is not null then credit_time else null end as credit_time
    from fin_dw_zz.dwt_user_no_p5_reg_to_wj_sx_a -- P5首授信逻辑
    where reg_channel_class in ('APP','H5')
    and  date(register_time)>='2024-03-31'
    ) t1
where (t1.first_wj_time is not null or t1.first_wj_time!='') or (t1.credit_time is not null or t1.credit_time!='')