INSERT overwrite TABLE credit_data.prd_user_attribution_data PARTITION(pday = '${yesterday_p}')

SELECT 
    user_no,
    cwj_busi_type,
    case when channel_no in ('bairong', 'bingjian', 'tianchuang', 'jingdong','dianhuabang<PERSON>','shenjun') then 
        channel_no 
    else 'ziying' 
    end as channel_no,
    sx_user_cnt_1st_prd
from fin_dim.dim_cwj_user_no_jyfx_gy_obs_jwm_pdi 
where pday >= date_format(trunc(date_sub(current_date, 1), 'MM'), 'yyyyMMdd')
        and pday <= date_format(last_day(date_sub(current_date, 1)), 'yyyyMMdd') 
        and cwj_busi_type = 'T30+'
group by 1, 2, 3, 4