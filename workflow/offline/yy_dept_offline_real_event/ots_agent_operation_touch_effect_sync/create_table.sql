-- 建表
CREATE TABLE IF NOT EXISTS credit_data.ots_touch_effect_sync_pdi (
    batch_no string COMMENT '批次号',
    sub_batch_no string COMMENT '子批次号',
    user_no string COMMENT '用户号',
    mobile_no_encryptx string COMMENT '加密手机号',
    agent_operator string COMMENT '运营商',
    unique_no string COMMENT '唯一标识ID',
    date_appl_submit string COMMENT '完件时间',
    date_credit_succ string COMMENT '授信时间',
    dz_user_cnt_t0 string COMMENT 'T0是否动支',
    date_created string COMMENT '创建时间',
    cluster_type string COMMENT '集群类型'
) COMMENT 'ots触达效果表'
PARTITIONED BY (`pday` string COMMENT '增量天分区')
STORED AS ORC;