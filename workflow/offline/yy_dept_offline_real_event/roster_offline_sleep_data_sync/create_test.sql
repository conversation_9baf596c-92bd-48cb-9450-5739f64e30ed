CREATE TABLE IF NOT EXISTS  `credit_data`.`roster_offline_sleep_data_result`(
    `user_no` STRING COMMENT '用户号',
    `cust_no` STRING COMMENT '客户号',
    `credit_amt` STRING COMMENT '授信额度',
    `loan_req_no` STRING COMMENT '动支流水号',
    `fq_dz_result` STRING COMMENT '发起动支结果',
    `fq_dz_time` STRING COMMENT '发起动支时间',
    `fq_dz_amt` STRING COMMENT '发起动支金额',
     `dz_result` STRING COMMENT '动支结果',
    `dz_success_time` STRING COMMENT '成功动支时间',
    `dz_success_amt` STRING COMMENT '成功动支金额'
    )
COMMENT'老客结清睡眠离线结果表'
    PARTITIONED BY(pday STRING COMMENT'天分区')
    STORED AS ORC;