insert overwrite table credit_data.roster_offline_sleep_data_result partition(pday='${yesterday_p}')
SELECT
  b.user_no,
  a.cust_no,
  b.p5_max_total_credit_amt as credit_amt,
  a.loan_req_no,
  a.loan_state as fq_dz_result,
  substring(a.date_loan,1,19) as fq_dz_time,
  a.loan_amt as fq_dz_amt,
  a.loan_state as dz_result,
  substring(a.date_loan,1,19) as dz_success_time,
  a.loan_amt as dz_success_amt
from
  (
    -- 6121169
    -- 6121163
     select
      a.cust_no,
      a.date_loan,-- 发起时间
      a.loan_amt,
      a.loan_req_no,-- 订单号
      b.loan_state,-- 'DS'是成功
	  replace(substring(a.date_loan,1,10), '-') as aday,-- 发起时间
      row_number() over(partition by a.cust_no,a.loan_req_no order by a.date_loan desc ) as rn
    from
      (
		select * from safe3_copy.brc_lps_i_iou_d
        where product_code in ('360JIETIAO', '360JINXIAO', '360YINGJI', '360BIG')
        and unix_timestamp(current_date, 'yyyy-MM-dd') - unix_timestamp(date_loan, 'yyyy-MM-dd') <= (45 * 86400)
      ) a
      left join safe3_copy.brc_lps_i_iou_d b -- 排除转推
      on a.last_loan_req_no = b.loan_req_no
    where
      a.first_loan_req_no = a.loan_req_no
      and b.loan_req_no is not null
  ) a
  INNER join (
    SELECT
        p5_max_total_credit_amt,
        cust_no,
        user_no,
        pday
    from fin_dw_cp.dwt_user_no_credit_succ_all_pda
  ) b on a.aday = b.pday and a.cust_no = b.cust_no