from word_document_parser import WordDocumentParser

word_file_path = "D:\\工作文档\\20250116华为试算临价展示优化\\借贷接口规范_V2 --华为.docx"
# word_file_path = "D:\\工作文档\\20220303华为渠道对接\\借贷接口规范_V1_20220107.docx"
# word_file_path = "D:\\工作文档\\20230629海尔消金\\接口文档（海尔(云闪付)360融担）v1.2(1).docx"
# word_file_path = "D:\\工作文档\\20250227宁银消金拒量API\\【接口文档】宁银消金&融担公司交互接口文档V12标准版2.docx"
# word_file_path = "https://ai-studio-dev.daikuan.qihoo.net/files/3d78f56c-37b4-4582-b47a-baaaff20eada/file-preview?timestamp=1751871676&nonce=d2491717bebed9220ae408c12006ccb8&sign=fj6bnCIAjv-2MZG9gHtUHl2JYbNllXZhGuxVxCdc-ng="
# word_file_path = "https://ai-studio-dev.daikuan.qihoo.net/files/f1ead94a-f588-4703-b7e8-4e21b3101139/file-preview?timestamp=1752834092&nonce=c0c32d23722654c14f8494bd6865d890&sign=utv6thIX7r4rKvIBU5Xs3QMRLXhkC4opHgxJUah2KfM="
word_parser = WordDocumentParser(word_file_path)
all_headings = word_parser.get_all_headings()
original_content = word_parser.get_original_content()
markdown_content = word_parser.get_markdown_content()
# document_info = word_parser.get_document_info()
split_list = word_parser.split_markdown_by_headings()
# split_test_list = ["3.1.1 授信审核申请接口","【LP30041】主动还款（晨风）","【LP10010】额度申请进度查询","【LP20005】贷款信息/审批状态查询","【LP40008】额度/贷款审批状态推送【消金to三方】","【LP40029】客户撞库接口【海尔to三方】"]
# split_test_list = ["公共数据对象","0101 – 用户准入接口(account.check)","0110 – 身份证上传接口(eid.upload)","0212 – 分期购订单确认（borrow.confirm.purchase）"]
# split_test_list = [
#     "借贷接口规范 V1",
#     "准入与授信管理接口 (借贷平台提供)",
#     "0101 – 用户准入接口(account.check)",
#     "0102 – 授信检查接口(credit.check)",
#     "0103 – 授信确认接口(credit.confirm)",
#     "0104 – 授信结果查询接口(credit.status.query)",
#     "0105 – 授信关系查询接口(credit.account.query)",
#     "0106 – 用户开具证明接口(account.settlement.query)",
#     "0107 – 发送开具证明接口(account.settlement.send)",
#     "0108 – 更换手机号接口(account.phoneno.change)",
#     "0109 – 活体人脸校验接口(face.check)",
#     "0110 – 身份证上传接口(eid.upload)",
#     "0111 – 联系人添加接口(contact.add)",
#     "0112 – 个人资料补充接口(userinfo.supplement)",
#     "0113 – 销户检查(account.cancel.check)",
#     "0114 – 生命周期操作(account.lifecycle)",
#     "0115 –身份证查询操作(eid.upload.query)",
#     "0116 –短信发送（otp.apply）",
#     "用信接口(借贷平台提供)",
#     "0201 - 默认试算（borrow.trial.default）",
#     "0202 - 借贷试算（borrow.trial）",
#     "0203 – 创建订单（borrow.apply）",
#     "0204 – 订单确认（borrow.confirm）",
#     "0205 – offer变更确认（borrow.offer.confirm）",
#     "0206 - 查询用信申请结果（borrow.status.query）",
#     "0207 - 查询借钱记录（borrow.order.query）",
#     "0208 – 取消未完成订单（borrow.order.cancel）",
#     "0209 – 查询借款订单详情（borrow.order.detail）",
#     "还款接口(借贷平台提供)",
#     "0301 - 查询月账单/所有借贷（repay.plan.query）",
#     "0302 – 提前还款试算（repay.trial）",
#     "0303 - 确认还款（repay.confirm）",
#     "0304 - 查询还款结果（repay.status.query）",
#     "0305 - 查询还款记录（repay.order.query）",
#     "0306 - 查询还款记录详情（repay.order.detail.query）",
#     "绑卡接口(借贷平台提供)",
#     "0401 - 查询支持的银行列表（bank.support.list）[公开]",
#     "0402 - 请求绑卡（bindcard.apply）",
#     "0403 - 绑卡确认（bindcard.confirm）",
#     "0404 - 用户自动还款设置（account.repay.set）",
#     "0405 - 绑卡查询（bindcard.query）",
#     "0406 – 删除绑定卡（bindcard.delete）",
#     "0407 - 卡Bin校验（card.bin.check）",
#     "回调接口（华为提供）",
#     "0501 - 通知借贷结果（order.notify）",
#     "0602 - 通知还款结果（repay.notify）",
#     "0603 - 通知授信结果（credit.status.notify）",
#     "0604 – 推送优惠券（coupon.send）",
#     "0605 – 调额通知（credit.change.notify）",
#     "0606 – 营销通知（promotion.notify）",
#     "0607 – 手机号修改通知（phoneno.change.notify）",
#     "0608 – 销户回调通知（account.cancel.notify）",
#     "优惠券",
#     "0701 – 查询优惠券（coupon.query）",
#     "其他",
#     "0801 – 客服凭证查询（sessionid.query）",
#     "报文结构说明",
#     "报文结构",
#     "请求消息",
#     "公共数据对象",
#     "UserInfo 用户信息",
#     "ImageInfo 身份证影像信息",
#     "FaceImageInfo 活体人脸信息",
#     "ContactInfo 联系人信息",
#     "OtpInfo 短信信息",
#     "CreditSignInfo 授信签约关系",
#     "RiskInfo（风控信息）",
#     "RepayTypeInfo（还款方式宣传信息）",
#     "RepayFqInfo（分期宣传信息）",
#     "RepayPlanInfo（还款计划信息）",
#     "BorrowOrderInfo（借据信息）",
#     "RepayTrialInfo（提前还款试算订单信息）",
#     "RepayOrderInfo（还款记录）",
#     "RepayOrderDetail（还款订单详情）",
#     "BankInfo（银行信息）",
#     "CardInfo（绑卡信息）",
#     "CouponInfo（优惠券信息）",
#     "ChangeInfo（授信内容变化信息）",
#     "AddressInfo（地址信息）",
#     "NotifyInfo（营销推广信息）",
#     "公共错误码"
# ]
ret1 = word_parser.split_markdown_by_selected_headings(split_test_list)
ret2 = word_parser.get_sequential_sections_analysis(split_test_list)
ret3 = word_parser.split_markdown_from_earliest_heading(split_test_list)
ret4 = word_parser.split_headings_by_selected_headings(split_test_list)
ret5 = word_parser.get_headings_document_blocks_dict(split_test_list)
print("1111")