from markdown_parser import MarkdownParser
import document_md
import json


"""测试MarkdownParser的功能"""

# 创建解析器实例
parser = MarkdownParser(document_md.huawei_document)

print("=== 测试1: 获取所有标题 ===")
all_headings = parser.get_all_headings()
print("所有标题:")
for i, heading in enumerate(all_headings, 1):
    print(f"{i}. {heading}")

print(f"\n总共找到 {len(all_headings)} 个标题")

print("\n=== 测试2: 获取文档信息 ===")
doc_info = parser.get_document_info()
print("文档信息:")
for key, value in doc_info.items():
    if key == 'headings':
        print(f"  {key}: {len(value)} 个标题")
    else:
        print(f"  {key}: {value}")

print("\n=== 测试3: 根据指定标题切分文档 ===")
# 测试指定部分标题
target_headings = ['借贷接口规范 V2', '4.3 0103 – 授信确认接口(credit.confirm)..', '6.1 0301 - 查询月账单/所有借贷（repay.plan.query）', '7.2.2 响应消息.', '8 回调接口（华为提供）', '4.1 0101 – 用户准入接口(account.check)', '4.2 0102 – 授信检查接口(credit.check)', '4.3 0103 – 授信确认接口(credit.confirm)', '4.4 0104 – 授信结果查询接口(credit.status.query)', '4.5 0105 – 授信关系查询接口(credit.account.query)', '4.6 0106 – 用户开具证明接口(account.settlement.query)', '4.7 0107 – 发送开具证明接口(account.settlement.send)', '4.8 0108 – 更换手机号接口(account.phoneno.change)', '4.9 0109 – 活体人脸校验接口(face.check)', '4.10 0110 – 身份证上传接口(eid.upload)', '4.11 0111 – 联系人添加接口(contact.add)', '4.12 0112 – 个人资料补充接口(userinfo.supplement)', '4.13 0113 – 销户检查(account.cancel.check)', '4.14 0114 – 生命周期操作(account.lifecycle)', '4.15 0115 –身份证查询操作(eid.upload.query)', '4.16 0116 –短信发送（otp.apply）', '4.17 0117 – 全量企业信息查询接口(enterprise.info.query)', '5.1 0201 - 默认试算（borrow.trial.default）', '5.2 0202 - 借贷试算（borrow.trial）', '5.3 0203 – 创建订单（borrow.apply）', '5.4 0204 – 订单确认（borrow.confirm）', '5.5 0205 – offer 变更确认（borrow.offer.confirm）', '5.6 0206 - 查询用信申请结果（borrow.status.query）', '5.7 0207 - 查询借钱记录（borrow.order.query）', '5.8 0208 – 取消未完成订单（borrow.order.cancel）', '5.9 0209 – 查询借款订单详情 （borrow.order.detail）', '5.11 0211 – 分期购创建订单（borrow.apply.purchase）', '5.12 0212 – 分期购订单确认（borrow.confirm.purchase）', '6.1 0301 - 查询月账单/所有借贷（repay.plan.query）', '6.2 0302 – 还款试算（repay.trial）', '6.3 0303 - 确认还款（repay.confirm）', '6.4 0304 - 查询还款结果（repay.status.query）', '6.5 0305 - 查询还款记录（repay.order.query）', '6.6 0306 - 查询还款记录详情（repay.order.detail.query）', '7.2 0402 - 请求绑卡（bindcard.apply）', '7.3 0403 - 绑卡确认（bindcard.confirm）', '7.4 0404 - 用户自动还款设置（account.repay.set）', '7.5 0405 - 绑卡查询（bindcard.query）', '7.6 0406 – 删除绑定卡（bindcard.delete）', '7.7 0407 - 卡 Bin 校验（card.bin.check）', '7.8 0408 – 查询协议列表（protocol.list）', '7.9 0409 – 添加对公账户（corporate.account.add）', '8.1 0501 - 通知借贷结果（order.notify）', '8.2 0602 - 通知还款结果（repay.notify）', '8.3 0603 - 通知授信结果（credit.status.notify）', '8.4 0604 – 推送优惠券（coupon.send）', '8.5 0605 – 调额通知（credit.change.notify）', '8.6 0606 – 营销通知（promotion.notify）', '8.7 0607 – 手机号修改通知（phoneno.change.notify）', '8.8 0608 – 销户回调通知（account.cancel.notify）', '8.9 0609 – 身份证过期回调通知（eid.expired.notify）', '8.10 0610 - 通知分期购退款结果（refund.notify.purchase）', '9.1 0701 – 查询优惠券（coupon.query）', '10.1 0801 – 客服凭证查询（sessionid.query）', '10.2 0802 – 查询结算账单（settle.bill.query）', '10.3 0803 – 外部操作地址查询（sp.operation.addr.query）', '10.5 0805 –借款意图信息提交（borrow.intention.submit）', '10.6 0806 –用户身份信息收集（user.type.collect）', '10.7 0807 –协议信息查询（protocol.info.query）', '10.8 0808 –线下提额申请（offline.raise.amount.apply）', '10.9 0809 –补录项查询（supplement.operations.query）', '10.10 0810 – 发送文件材料接口(account.material.send)', '10.11 0811 – 通用资格查询（qualification.query）', '10.12 0812 – 活动数据上报（activity.data.report）', '10.13 0813 –文件材料开具查询接口(account.material.query)']

print(f"指定标题: {target_headings}")

try:
    sections_dict = parser.get_sections_document_blocks_dict(target_headings)

    print(f"\n成功切分出 {len(sections_dict)} 个部分:")

    for heading, blocks in sections_dict.items():
        print(f"\n--- 标题: {heading} ---")
        for block in blocks:
            print(f"  级别: {block['level']}")
            print(f"  行数: {block['line_count']}")
            print(f"  字符数: {block['char_count']}")
            print(f"  段落数: {block['paragraph_count']}")
            print(f"  包含表格: {block['has_table']}")
            print(f"  包含代码: {block['has_code']}")
            print(f"  开始行: {block['start_line']}")
            print(f"  结束行: {block['end_line']}")
            print(f"  内容预览: {block['content'][:100]}...")

except ValueError as e:
    print(f"错误: {e}")

print("\n=== 测试4: 测试不存在的标题 ===")
non_existent_headings = ["不存在的标题1", "不存在的标题2"]

try:
    sections_dict = parser.get_sections_document_blocks_dict(non_existent_headings)
    print("结果:", sections_dict)
except ValueError as e:
    print(f"预期的错误: {e}")

print("\n=== 测试5: 测试空标题列表 ===")
empty_result = parser.get_sections_document_blocks_dict([])
print(f"空标题列表结果: {empty_result}")

print("\n=== 测试6: 测试parse_document方法 ===")
original_content = parser.parse_document('original')
print(f"原始内容长度: {len(original_content)}")

headings_content = parser.parse_document('headings')
print("标题列表:")
print(headings_content)
