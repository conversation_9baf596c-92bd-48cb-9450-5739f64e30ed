from graph.state.tip_chat.tip_agent_graph_state import TipAgentState
from graph.prompt.tip_chat.identify_intention_prompt import *
from graph.prompt.tip_chat.chat_prompt import *
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.graph.message import RemoveMessage, REMOVE_ALL_MESSAGES
from graph.get_llm_model import get_llm
from graph.utils.state_message_util import get_state_message, format_state_message
from services.tip.tip_api_service import get_api_partner_info
from langgraph.config import get_stream_writer
from core.logging import get_logger
from graph.schema.model_type import ModelType
from graph.tools.math_alculation_tools import add, multiply
from graph.tools.common_tools import current_date
from src.app.graph.utils.format_util import format_json
from enum import Enum
import json

logger = get_logger()

conversation_history_msg = {}


class TipChatNodeEnum(Enum):
    INIT_CHAT_STATE = ("init_chat_state", "初始化state", "custom")
    IDENTIFY_INTENTION = ("identify_intention", "意图识别", "custom")
    CHAT = ("chat", "聊天", "stream")
    PARTNER_CONFIG_QUERY = ("partner_config_query", "渠道信息查询", "stream")
    PARTNER_CONFIG_MODIFY = ("partner_config_modify", "渠道配置修改", "custom")
    TOOLS = ("tools", "工具调用", "custom")

    def __init__(self, code, desc, output_mode):
        self.code = code
        self.desc = desc
        self.output_mode = output_mode

    @classmethod
    def get_by_code(cls, code):
        for member in cls:
            if member.code == code:
                return member
        return None

    @classmethod
    def is_stream_mode(cls, code):
        tip_chat_node_enum = cls.get_by_code(code)
        if tip_chat_node_enum:
            return "stream" == tip_chat_node_enum.output_mode
        else:
            return False

def init_chat_state(state: TipAgentState):
    logger.info("PRO 初始化state节点 conversation_id:{},message_id:{},query:{}", state["conversation_id"], state["message_id"],
                state["question"])

    # 初始化state信息
    state["messages"] = [RemoveMessage(id=REMOVE_ALL_MESSAGES)]
    state["intention"] = ""
    state["fill_user_question"] = ""
    state["answer"] = ""
    state["partner_base_info_list"] = []
    return state


def identify_intention(state: TipAgentState):
    logger.info("PRO 开始进行意图识别 conversation_id:{},message_id:{},query:{}", state["conversation_id"], state["message_id"],
                state["question"])

    # 输出node开始节点信息
    writer = get_stream_writer()
    writer("\nidentify_intention start\n")

    human_question = HumanMessage(content=state["question"])
    partner_base_info = get_api_partner_info()
    # target_base_info = get_api_target_info.fetch_data_from_api()
    prompt_template = ChatPromptTemplate(
        [
            ("system", identify_intention_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )

    # 添加本轮对话的用户信息
    state["messages"].append(human_question)

    # 获取历史对话信息
    history_messages = get_history_chat_message(state)
    prompt = prompt_template.invoke({
        "messages": state["messages"],
        "history_messages": history_messages,
        "response_format": identify_intention_response_format,
        "examples": identify_intention_examples,
        "partner_base_info_list": json.dumps(partner_base_info, ensure_ascii=False)
    })

    response = get_llm(ModelType.DEEPSEEK.value).invoke(prompt)
    response_data = json.loads(format_json(response.content))
    logger.info("PRO 意图识别模型返回结果 question:{},conversation_id:{},response:{}", state["question"], state["conversation_id"], response_data)
    state["intention"] = response_data["intention"]
    if "partner_base_info_list" in response_data:
        state["partner_base_info_list"] = response_data["partner_base_info_list"]

    if "fill_user_question" in response_data:
        state["fill_user_question"] = response_data["fill_user_question"]
    else:
        state["fill_user_question"] = state["question"]

    identify_intention_content = f"用户意图:{state['intention']},用户原始提问:{state["question"]},完善后的用户提问:{state['fill_user_question']}"

    for chunk in identify_intention_content:
        writer(chunk)

    # 输出node开始结束节点信息
    writer("\nidentify_intention end\n")
    return state

def chat(state: TipAgentState):
    tools = [add, multiply, current_date]
    prompt_template = ChatPromptTemplate(
        [
            ("system", tip_chat_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    prompt = prompt_template.invoke({
        "messages": state["messages"],
        "history_messages": state["chat_history_messages"]
    })
    llm_with_tools = get_llm(ModelType.DEEPSEEK.value).bind_tools(tools)
    response = llm_with_tools.invoke(prompt)
    state["messages"].append(response)
    return state

def partner_config_query(state: TipAgentState):
    print("渠道信息查询")
    invoke_messages = get_state_message(state, 50)
    prompt_template = ChatPromptTemplate.from_messages(
        [
            SystemMessage("""你是一个渠道配置查询专家,可以根据用户的需求进行渠道信息的配置查询，如果不知道就说不知道"""),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    prompt = prompt_template.invoke(invoke_messages)
    response = get_llm(ModelType.DEEPSEEK.value).invoke(prompt)
    state["messages"].append(response)
    return state


def partner_config_modify(state: TipAgentState):
    print("渠道信息修改")
    invoke_messages = get_state_message(state, 50)
    prompt_template = ChatPromptTemplate.from_messages(
        [
            SystemMessage("""你是一个渠道配置专家,可以根据用户的需求进行渠道信息的配置，如果不知道就说不知道"""),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    prompt = prompt_template.invoke(invoke_messages)
    response = get_llm(ModelType.DEEPSEEK.value).invoke(prompt)
    state["messages"].append(response)
    return state





def get_history_chat_message(state: TipAgentState):
    if state["conversation_id"] not in conversation_history_msg:
        conversation_history_msg[state["conversation_id"]] = []
    history_msg = conversation_history_msg[state["conversation_id"]]
    if history_msg:
        if len(history_msg) > 20:
            invoke_messages = history_msg[-20:]
        else:
            invoke_messages = history_msg
    else:
        invoke_messages = []
    state["chat_history_messages"] = invoke_messages
    return invoke_messages


def save_history_msg(conversation_id: str, question: str, answer: str):
    if conversation_id not in conversation_history_msg:
        conversation_history_msg[conversation_id] = []

    history_msg = conversation_history_msg[conversation_id]
    history_msg.append(format_state_message(HumanMessage(content=question)))
    history_msg.append(format_state_message(AIMessage(content=answer)))
