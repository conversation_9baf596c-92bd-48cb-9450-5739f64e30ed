from deepbank_adk import Client
from dotenv import load_dotenv
from src.app.core.logging import get_logger

import os

llm_client = {}
logger = get_logger()

load_dotenv()
general_api_key = os.getenv("DEEPBANK_API_KEY")


def get_client():
    if "pro" == os.getenv("ENV"):
        client = Client.build(general_api_key)
    else:
        # 模型初始化
        client = Client.build(
            general_api_key,
            debug_mode=True
        )
    return client

try:
    client = get_client()

    llm_connections = client.models.llm_connections
    for conn in llm_connections:
        model_type = conn.model
        llm = client.models.chat_openai(model_type)
        llm_client[model_type] = llm
except Exception as e:
    logger.exception("ERR 初始化获取模型失败")


def get_llm(model_type: str):
    if model_type in llm_client:
        return llm_client[model_type]
    else:
        cur_client = get_client()
        cur_llm = cur_client.models.chat_openai(model_type)
        llm_client[model_type] = cur_llm
        return llm
