from typing import Annotated, List
from typing_extensions import TypedDict
from graph.schema.api_knowlege_schema import PartnerBasInfo, TargetBasInfo


class IdentifyIntentionResult(TypedDict):

    intention: Annotated[str, "用户问题的意图"]

    fill_user_question: Annotated[str, "完善后的用户提问"]

    partner_base_info_list: Annotated[List[PartnerBasInfo], "用户问题匹配的渠道编码"]

    target_base_info_list: Annotated[List[TargetBasInfo], "用户问题匹配的指标信息"]


