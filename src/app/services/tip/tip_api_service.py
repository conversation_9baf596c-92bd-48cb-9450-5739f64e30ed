import requests
from core.logging import get_logger

domain = "http://10.236.165.215"
token = "c462d9b14217489a84c70df2bcca6d62"

logger = get_logger()

def get_api_partner_info():
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": token,  # 身份验证令牌
        }
        logger.info("OUTREQ 请求获取所有渠道基本信息,header:{}", headers)
        # 发送 GET 请求
        response = requests.get(domain + "/agent/knowledge/partnerInfo/all", headers=headers)

        logger.info("OUTRESP 请求获取所有渠道基本信息响应结果,response:{}", response)
        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data and json_data["code"] == 200:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            logger.error("ERR 请求获取所有渠道基本信息请求失败，状态码:{}", response.status_code)
            return None
    except Exception as e:
        logger.error("ERR 请求获取所有渠道基本信息请求失败", exc_info=e)
        return None

def get_api_target_info():
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": token,  # 身份验证令牌
        }
        logger.info("OUTREQ 请求获取所有指标基本信息,header:{}", headers)
        # 发送 GET 请求
        response = requests.get(domain + "/agent/knowledge/targetInfo/all", headers=headers)

        logger.info("OUTRESP 请求获取所有指标基本信息响应结果,response:{}", response)
        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            logger.error("ERR 请求获取所有指标基本信息请求失败，状态码:{}", response.status_code)
            return None
    except Exception as e:
        logger.error("ERR 请求获取所有指标基本信息请求失败", exc_info=e)
        return None