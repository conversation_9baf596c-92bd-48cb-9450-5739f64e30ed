from fastapi import APIRouter
from graph.modules.tip_chat.tip_agent_graph import graph_stream

from fastapi.responses import StreamingResponse
from core.logging import get_logger
from models.request.agent.agent_chat_requests import ChatRequest

logger = get_logger()

router = APIRouter(
    prefix="/agent",
    tags=["agent"],
    responses={404: {"description": "Not found"}},
)


@router.post("/chat")
async def chat(request: ChatRequest):
    logger.info("Req 聊天请求问题接口,{query}", query=request)
    return StreamingResponse(graph_stream(request), media_type="text/event-stream")
