import json

result = {
  "text": "```json\n{\n  \"interface_name\": [\n    \"借贷接口规范 V2\",\n    \"4.3 0103 – 授信确认接口(credit.confirm)..\",\n    \"6.1 0301 - 查询月账单/所有借贷（repay.plan.query）\",\n    \"7.2.2 响应消息.\",\n    \"8 回调接口（华为提供）\",\n    \"4.1 0101 – 用户准入接口(account.check)\",\n    \"4.2 0102 – 授信检查接口(credit.check)\",\n    \"4.3 0103 – 授信确认接口(credit.confirm)\",\n    \"4.4 0104 – 授信结果查询接口(credit.status.query)\",\n    \"4.5 0105 – 授信关系查询接口(credit.account.query)\",\n    \"4.6 0106 – 用户开具证明接口(account.settlement.query)\",\n    \"4.7 0107 – 发送开具证明接口(account.settlement.send)\",\n    \"4.8 0108 – 更换手机号接口(account.phoneno.change)\",\n    \"4.9 0109 – 活体人脸校验接口(face.check)\",\n    \"4.10 0110 – 身份证上传接口(eid.upload)\",\n    \"4.11 0111 – 联系人添加接口(contact.add)\",\n    \"4.12 0112 – 个人资料补充接口(userinfo.supplement)\",\n    \"4.13 0113 – 销户检查(account.cancel.check)\",\n    \"4.14 0114 – 生命周期操作(account.lifecycle)\",\n    \"4.15 0115 –身份证查询操作(eid.upload.query)\",\n    \"4.16 0116 –短信发送（otp.apply）\",\n    \"4.17 0117 – 全量企业信息查询接口(enterprise.info.query)\",\n    \"5.1 0201 - 默认试算（borrow.trial.default）\",\n    \"5.2 0202 - 借贷试算（borrow.trial）\",\n    \"5.3 0203 – 创建订单（borrow.apply）\",\n    \"5.4 0204 – 订单确认（borrow.confirm）\",\n    \"5.5 0205 – offer 变更确认（borrow.offer.confirm）\",\n    \"5.6 0206 - 查询用信申请结果（borrow.status.query）\",\n    \"5.7 0207 - 查询借钱记录（borrow.order.query）\",\n    \"5.8 0208 – 取消未完成订单（borrow.order.cancel）\",\n    \"5.9 0209 – 查询借款订单详情 （borrow.order.detail）\",\n    \"5.11 0211 – 分期购创建订单（borrow.apply.purchase）\",\n    \"5.12 0212 – 分期购订单确认（borrow.confirm.purchase）\",\n    \"6.1 0301 - 查询月账单/所有借贷（repay.plan.query）\",\n    \"6.2 0302 – 还款试算（repay.trial）\",\n    \"6.3 0303 - 确认还款（repay.confirm）\",\n    \"6.4 0304 - 查询还款结果（repay.status.query）\",\n    \"6.5 0305 - 查询还款记录（repay.order.query）\",\n    \"6.6 0306 - 查询还款记录详情（repay.order.detail.query）\",\n    \"7.2 0402 - 请求绑卡（bindcard.apply）\",\n    \"7.3 0403 - 绑卡确认（bindcard.confirm）\",\n    \"7.4 0404 - 用户自动还款设置（account.repay.set）\",\n    \"7.5 0405 - 绑卡查询（bindcard.query）\",\n    \"7.6 0406 – 删除绑定卡（bindcard.delete）\",\n    \"7.7 0407 - 卡 Bin 校验（card.bin.check）\",\n    \"7.8 0408 – 查询协议列表（protocol.list）\",\n    \"7.9 0409 – 添加对公账户（corporate.account.add）\",\n    \"8.1 0501 - 通知借贷结果（order.notify）\",\n    \"8.2 0602 - 通知还款结果（repay.notify）\",\n    \"8.3 0603 - 通知授信结果（credit.status.notify）\",\n    \"8.4 0604 – 推送优惠券（coupon.send）\",\n    \"8.5 0605 – 调额通知（credit.change.notify）\",\n    \"8.6 0606 – 营销通知（promotion.notify）\",\n    \"8.7 0607 – 手机号修改通知（phoneno.change.notify）\",\n    \"8.8 0608 – 销户回调通知（account.cancel.notify）\",\n    \"8.9 0609 – 身份证过期回调通知（eid.expired.notify）\",\n    \"8.10 0610 - 通知分期购退款结果（refund.notify.purchase）\",\n    \"9.1 0701 – 查询优惠券（coupon.query）\",\n    \"10.1 0801 – 客服凭证查询（sessionid.query）\",\n    \"10.2 0802 – 查询结算账单（settle.bill.query）\",\n    \"10.3 0803 – 外部操作地址查询（sp.operation.addr.query）\",\n    \"10.5 0805 –借款意图信息提交（borrow.intention.submit）\",\n    \"10.6 0806 –用户身份信息收集（user.type.collect）\",\n    \"10.7 0807 –协议信息查询（protocol.info.query）\",\n    \"10.8 0808 –线下提额申请（offline.raise.amount.apply）\",\n    \"10.9 0809 –补录项查询（supplement.operations.query）\",\n    \"10.10 0810 – 发送文件材料接口(account.material.send)\",\n    \"10.11 0811 – 通用资格查询（qualification.query）\",\n    \"10.12 0812 – 活动数据上报（activity.data.report）\",\n    \"10.13 0813 –文件材料开具查询接口(account.material.query)\"\n  ],\n  \"common_structure\": [\n    \"3 报文结构说明.\",\n    \"5.4.1 请求 data 数据结构.\",\n    \"3.1 报文结构\",\n    \"3.1.1 请求消息\",\n    \"3.1.2 响应消息\",\n    \"3.2 数据对象定义\",\n    \"3.2.2 公共数据对象\",\n    \"UserInfo 用户信息\",\n    \"ImageInfo 身份证影像信息\",\n    \"FaceImageInfo 活体人脸信息\",\n    \"FaceVideoInfo 活体人脸视频信息\",\n    \"ContactInfo 联系人信息\",\n    \"CreditSignInfo 授信签约关系\",\n    \"RepayFqInfo（分期宣传信息）\",\n    \"RepayPlanInfo （还款计划信息）\",\n    \"hirePlanInfo（分期计划信息）\",\n    \"RepayPlanSummary （还款计划汇总）\",\n    \"BorrowOrderInfo（借据信息）\",\n    \"RepayTrialInfo （提前还款试算订单信息）\",\n    \"ProtocolInfo（协议条款信息）\",\n    \"CouponInfo （优惠券信息）\",\n    \"SubMallOrderInfo（商城子订单信息）\",\n    \"AmountChangeInfo（额度变化信息）\",\n    \"RateChangeInfo （利率变化信息）\",\n    \"ExternalOptInfo（外部操作信息）\",\n    \"ChangeOfferSignInfo（Offer 变更签约信息）\",\n    \"ChangeOfferOrderInfo(Offer 变更订单信息)\",\n    \"ChangeOffer（Offer 变更信息）\",\n    \"IndustyInfo （投向行业信息）\",\n    \"DynamicParam（动态参数信息）\",\n    \"EnterpriseInfo （企业信息）\"\n  ],\n  \"enum\": [\n    \"3.1.3 公共错误码\"\n  ]\n}\n```",
  "usage": {
    "prompt_tokens": 0,
    "prompt_unit_price": "0",
    "prompt_price_unit": "0",
    "prompt_price": "0",
    "completion_tokens": 0,
    "completion_unit_price": "0",
    "completion_price_unit": "0",
    "completion_price": "0",
    "total_tokens": 0,
    "total_price": "0",
    "currency": "USD",
    "latency": 66.36528190970421
  },
  "finish_reason": "stop"
}



result_content = result["text"]
json_str = result_content.replace('```json\n', ' ').replace('\n```', ' ').replace('\n', ' ').strip()
data = json.loads(json_str)

