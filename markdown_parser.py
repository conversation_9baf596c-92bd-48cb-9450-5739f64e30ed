import re
from typing import List, Dict, Any, Optional
from pathlib import Path


class MarkdownParser:
    """Markdown文档解析器，用于解析markdown文本内容"""

    def __init__(self, markdown_content: str):
        """
        初始化Markdown解析器

        Args:
            markdown_content: Markdown文本内容
        """
        self.markdown_content = markdown_content
        self._headings = None
        self._parsed_lines = None

    def _parse_lines(self) -> List[str]:
        """
        解析markdown内容为行列表
        
        Returns:
            List[str]: 行列表
        """
        if self._parsed_lines is None:
            self._parsed_lines = self.markdown_content.split('\n')
        return self._parsed_lines

    def _extract_heading_info(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """
        从行中提取标题信息
        
        Args:
            line: 文本行
            line_num: 行号（从0开始）
            
        Returns:
            Dict: 标题信息，如果不是标题则返回None
        """
        # 匹配markdown标题格式 (# ## ### 等)
        heading_match = re.match(r'^(#{1,6})\s+(.+)$', line.strip())
        if heading_match:
            level = len(heading_match.group(1))
            title = heading_match.group(2).strip()
            return {
                'title': title,
                'level': level,
                'line_num': line_num,
                'raw_line': line
            }
        return None

    def get_all_headings(self) -> List[str]:
        """
        获取所有标题，按文档原始顺序排序
        
        Returns:
            List[str]: 标题列表
        """
        if self._headings is not None:
            return self._headings

        lines = self._parse_lines()
        headings = []
        
        for i, line in enumerate(lines):
            heading_info = self._extract_heading_info(line, i)
            if heading_info:
                headings.append(heading_info['title'])
        
        self._headings = headings
        return headings

    def _get_heading_line_map(self) -> Dict[str, Dict[str, Any]]:
        """
        获取标题到行号的映射
        
        Returns:
            Dict: 标题映射信息
        """
        lines = self._parse_lines()
        heading_map = {}
        
        for i, line in enumerate(lines):
            heading_info = self._extract_heading_info(line, i)
            if heading_info:
                heading_map[heading_info['title']] = heading_info
        
        return heading_map

    def _sort_headings_by_document_order(self, target_headings: List[str]) -> List[str]:
        """
        按文档原始顺序对给定的标题列表进行排序
        
        Args:
            target_headings: 目标标题列表
            
        Returns:
            List[str]: 按文档顺序排序的标题列表
        """
        all_headings = self.get_all_headings()
        heading_order = {heading: i for i, heading in enumerate(all_headings)}
        
        # 过滤出存在于文档中的标题，并按文档顺序排序
        valid_headings = [h for h in target_headings if h in heading_order]
        valid_headings.sort(key=lambda x: heading_order[x])
        
        return valid_headings

    def _create_document_block(self, heading: str, content: str, heading_info: Dict[str, Any], 
                             start_line: int, end_line: int) -> Dict[str, Any]:
        """
        创建文档块
        
        Args:
            heading: 标题名称
            content: 内容
            heading_info: 标题信息
            start_line: 开始行号
            end_line: 结束行号
            
        Returns:
            Dict: 文档块
        """
        content_lines = content.split('\n')
        
        # 分析内容特征
        has_table = '|' in content and '---' in content
        has_code = '```' in content or (content.count('`') >= 2)
        
        # 计算段落数（非空行数，排除标题行）
        non_empty_lines = [line for line in content_lines[1:] if line.strip()]
        paragraph_count = len(non_empty_lines)
        
        return {
            'heading': heading,
            'content': content,
            'line_count': len(content_lines),
            'char_count': len(content),
            'level': heading_info['level'],
            'start_line': start_line,
            'end_line': end_line,
            'raw_title_line': heading_info['raw_line'],
            'has_table': has_table,
            'has_code': has_code,
            'paragraph_count': paragraph_count
        }

    def get_sections_document_blocks_dict(self, target_headings: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据给定标题列表切分markdown内容，返回字典格式结果
        
        Args:
            target_headings: 目标标题列表
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 字典，key为标题名称，value为对应的document_blocks列表
            
        Raises:
            ValueError: 如果没有找到任何目标标题
        """
        if not target_headings:
            return {}

        # 按文档顺序排序标题
        sorted_headings = self._sort_headings_by_document_order(target_headings)
        
        if not sorted_headings:
            raise ValueError("没有找到任何指定的标题")

        lines = self._parse_lines()
        heading_line_map = self._get_heading_line_map()
        all_headings = self.get_all_headings()
        
        # 获取所有标题的行号列表（用于确定内容边界）
        all_heading_lines = []
        for heading in all_headings:
            if heading in heading_line_map:
                all_heading_lines.append(heading_line_map[heading]['line_num'])
        all_heading_lines.sort()
        
        result_dict = {}
        
        for heading in sorted_headings:
            if heading not in heading_line_map:
                continue
                
            heading_info = heading_line_map[heading]
            start_line = heading_info['line_num']
            
            # 确定结束行：找到下一个标题行（任何级别的标题）
            end_line = len(lines) - 1  # 默认到文档末尾
            
            for heading_line in all_heading_lines:
                if heading_line > start_line:
                    end_line = heading_line - 1
                    break
            
            # 提取内容
            content_lines = lines[start_line:end_line + 1]
            content = '\n'.join(content_lines)
            
            # 创建文档块
            document_block = self._create_document_block(
                heading, content, heading_info, start_line, end_line
            )
            
            # 将文档块添加到结果字典中
            result_dict[heading] = [document_block]
        
        return result_dict

    def get_document_info(self) -> Dict[str, Any]:
        """
        获取文档基本信息
        
        Returns:
            Dict: 文档信息
        """
        lines = self._parse_lines()
        all_headings = self.get_all_headings()
        
        # 统计信息
        total_lines = len(lines)
        total_chars = len(self.markdown_content)
        heading_count = len(all_headings)
        
        # 分析内容特征
        has_tables = '|' in self.markdown_content and '---' in self.markdown_content
        has_code_blocks = '```' in self.markdown_content
        
        return {
            'content_type': 'markdown',
            'total_lines': total_lines,
            'total_chars': total_chars,
            'heading_count': heading_count,
            'headings': all_headings,
            'has_tables': has_tables,
            'has_code_blocks': has_code_blocks
        }

    def parse_document(self, output_format: str = 'original') -> str:
        """
        解析文档并返回指定格式的内容
        
        Args:
            output_format: 输出格式 ('original', 'headings')
            
        Returns:
            str: 解析后的内容
        """
        if output_format == 'original':
            return self.markdown_content
        elif output_format == 'headings':
            headings = self.get_all_headings()
            return '\n'.join(headings)
        else:
            raise ValueError(f"不支持的输出格式: {output_format}")
