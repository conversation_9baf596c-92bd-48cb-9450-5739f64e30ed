package com.work.flow.http;

import com.work.flow.service.QueryYsService;

import java.io.IOException;
import java.util.*;

/**
 * @see com.work.flow.utils.GlobalUtils
 *
 * todo :先要更改请求cookie
 *
 * @author: 田荣吉
 * @createDate: 2023-02-07 17:40
 * @version: 1.0
 */
public class RequestYs {
    //返回工作流
    public static void main(String[] args) throws IOException {
        String projectStr = "yy_dept_iop\n" +
                "yy_dept_online_data_app_task\n" +
                "jt_yy_sc_opr_opr_export_ads\n" +
                "jt_yy_sc_opr_export_ads\n" +
                "yy_dept_offline_cdp_label_dw\n" +
                "yy_dept_offline_real_result\n" +
                "yy_dept_offline_other\n" +
                "yy_dept_offline_real_event\n" +
                "sync_hive_to_other\n" +
                "yy_dept_offline_dt";
        //projectStr ="yy_dept_offline_model";
        List<String> projectList = Arrays.asList(projectStr.split("\n"));
        Map<String, String> param = new HashMap<String, String>();
        param.put("page", "1");
        param.put("pageSize", "100000");
        param.put("periodicStatusDesc", "");
        param.put("userNo", "");
        param.put("dataCenter", "SAFE_LYCC");
        param.put("periodicStatus", "RUNNING");
        param.put("showMyWorkflow", "false");
        param.put("showHighPriority", "false");
        param.put("projectType", "NORMAL");
        param.put("viewFlag", "false");
        param.put("editFlag", "false");
        param.put("runFlag", "false");
        param.put("drillStatus", "");
        //下载毓数的工作流
        QueryYsService.downLoadWorkFlow(projectList,param,"workflow/offline");
    }
}
