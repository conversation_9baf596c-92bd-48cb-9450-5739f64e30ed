package com.work.flow.service;

import com.alibaba.fastjson.JSON;
import com.work.flow.pojo.readsingleworkflow.DepFlowList;
import com.work.flow.pojo.readsingleworkflow.JsonRootBean;
import com.work.flow.pojo.readsingleworkflow.Nodes;
import com.work.flow.pojo.readsingleworkflow.ResourceItems;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.List;

/**
 * 读取桌面 workflow文件  并生成我们识别的sql
 *
 * @className: PACKAGE_NAME-> com.qihoo.finance.ipss.util.Test
 * @description:
 * @author: 田荣吉
 * @createDate: 2022-09-01 20:26
 * @version: 1.0
 */
public class ReadYsWorkJSON {
    public static void fileHandle(File file1,String oprojectName,String directory) {
        final File file = file1;

        System.out.println(file.getName());
        System.out.println(file.getAbsolutePath());


        final String s = readFileByLines(file.getAbsolutePath());

        final JsonRootBean jsonObject = JSON.parseObject(s, JsonRootBean.class);
        System.out.println(jsonObject);

        final String[] split = file.getName().split("\\.");
        File filex = new File(directory+"//"+oprojectName + "//" + split[0]);
        if (!filex.exists()) {
            filex.mkdirs();

        }
        if (jsonObject == null || jsonObject.getNodes().size() == 0) {
            return;
        }
        List<Nodes> list = jsonObject.getNodes();
        for (int z = 0; z < list.size(); z++) {
            Nodes nodes = list.get(z);



            //脚本节点
            if ("script-task-node".equals(nodes.getShape())) {
                final String s1 = StringUtils.isNotBlank(nodes.getExecuteSql()) ? nodes.getExecuteSql() : "";
                extracted(directory+"//"+oprojectName,file.getName(), nodes.getLabel(), s1, z);
            }
            //依赖节点
            if ("depTask-task-node".equals(nodes.getShape())) {
                final StringBuilder s1 = new StringBuilder();
                List<ResourceItems> resourceItems = nodes.getResourceItems();
                for (ResourceItems resourceItem : resourceItems) {
                    final String cycle = resourceItem.getCycle();
                    final String dateValue = resourceItem.getDateValue();
                    final String depTasks = resourceItem.getDepTasks();
                    String projectName = resourceItem.getProjectName();
                    String name = resourceItem.getProjectName();

                    if (!CollectionUtils.isEmpty(resourceItem.getDepFlowList())) {
                        final DepFlowList depFlowList = resourceItem.getDepFlowList().get(0);
                        projectName = depFlowList.getProjectName();
                        name = depFlowList.getName();
                    }

                    s1.append("projectName:").append(projectName)
                            .append(",  workName:").append(name)
                            .append(",  depTasks:").append(depTasks)
                            .append(",  cycle:").append(cycle)
                            .append(",  dateValue:").append(dateValue).append("\r\n");
                }
                extracted(directory+"//"+oprojectName,file.getName(), nodes.getLabel(), s1.toString(), z);
            }

            //依赖节点
            if ("shell-task-node".equals(nodes.getShape())) {
                final String s1 = StringUtils.isNotBlank(nodes.getScriptContent()) ? nodes.getScriptContent() : "";
               extracted(directory+"//"+oprojectName,file.getName(), nodes.getLabel(), s1, z);
            }

            //依赖节点
            if ("sink-task-node".equals(nodes.getShape())) {
                final StringBuilder s1 = new StringBuilder();
                String brokers = nodes.getBrokers();
                String topic = nodes.getTopic();
                String clazz = nodes.getClazz();
                String cycle = nodes.getCycle();
                String hiveDbName = nodes.getHiveDbName();
                String hiveTableName = nodes.getHiveTableName();

                s1.append("brokers:").append(brokers).append("\r\n")
                        .append("topic:").append(topic).append("\r\n")
                        .append("clazz:").append(clazz).append("\r\n")
                        .append("cycle:").append(cycle).append("\r\n")
                        .append("hiveDbName:").append(hiveDbName).append("\r\n")
                        .append("hiveTableName:").append(hiveTableName)
                        .append("\r\n");

                extracted(directory+"//"+oprojectName,file.getName(), nodes.getLabel(), s1.toString(), z);
            }



        }
    }

    private static void extracted(String projectName,String directory, String name, String content, int i) {
        try {
            final String[] split = directory.split("\\.");
            File file1 = new File(projectName + "//" + split[0]);
            if (!file1.exists()) {
                file1.mkdirs();
            }


            File file = new File(projectName + "//" + split[0] + "//" + name + ".sql");
            if (!file.exists()) {
                file.createNewFile();
            }
            //使用true，即进行append file
            FileWriter fileWritter = new FileWriter(projectName + "//" + split[0] + "//" + name + ".sql", true);
            fileWritter.write(content);
            fileWritter.close();
            System.out.println("finish");
        } catch (IOException e) {

            e.printStackTrace();
            System.out.println("异常数据");

        }
    }

    public static String readFileByLines(String fileName) {
        StringBuilder stringBuilder = new StringBuilder();

        File file = new File(fileName);
        BufferedReader reader = null;
        try {


            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                stringBuilder.append(tempString);
                // 显示行号
                line++;
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
        System.out.println(stringBuilder);
        return stringBuilder.toString();

    }
}