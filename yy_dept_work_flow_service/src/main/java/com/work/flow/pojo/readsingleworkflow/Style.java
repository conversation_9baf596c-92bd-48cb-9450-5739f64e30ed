/**
  * Copyright 2022 bejson.com 
  */
package com.work.flow.pojo.readsingleworkflow;

/**
 * Auto-generated: 2022-09-06 19:15:8
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Style {

    private String stroke;
    private int lineWidth;
    private EndArrow endArrow;
    public void setStroke(String stroke) {
         this.stroke = stroke;
     }
     public String getStroke() {
         return stroke;
     }

    public void setLineWidth(int lineWidth) {
         this.lineWidth = lineWidth;
     }
     public int getLineWidth() {
         return lineWidth;
     }

    public void setEndArrow(EndArrow endArrow) {
         this.endArrow = endArrow;
     }
     public EndArrow getEndArrow() {
         return endArrow;
     }

}