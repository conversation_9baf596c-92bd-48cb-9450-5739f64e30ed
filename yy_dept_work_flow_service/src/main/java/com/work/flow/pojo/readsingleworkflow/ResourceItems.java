package com.work.flow.pojo.readsingleworkflow;


import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Auto-generated: 2022-11-07 10:39:13
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */

@Getter
@Setter
public class ResourceItems {

    private String depTasks;
    private String cycle;
    private String dateValue;
    private String projectName;
    private List<CycleValueList> cycleValueList;
    private int definitionId;
    private List<DepFlowList> depFlowList;
    private String status;
    private List<DepTaskList> depTaskList;

}
