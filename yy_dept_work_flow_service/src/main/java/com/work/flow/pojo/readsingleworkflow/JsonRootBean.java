/**
  * Copyright 2022 bejson.com 
  */
package com.work.flow.pojo.readsingleworkflow;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Auto-generated: 2022-09-06 19:15:8
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Getter
@Setter
public class JsonRootBean {

    private List<Nodes> nodes;
    private List<Edges> edges;
    private List<String> combos;
    private List<String> groups;

}