package com.work.flow.pojo;

/**
 * @className: com.xiaoying.web.getwork.pojo-> GlobalParams
 * @description:
 * @author: 田荣吉
 * @createDate: 2023-01-11 15:29
 * @version: 1.0
 */
/**
 * Copyright 2023 bejson.com
 */
/**
 * Copyright 2023 bejson.com
 */


/**
 * Auto-generated: 2023-01-11 15:28:28
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class GlobalParams {

    private String prop;
    private String direct;
    private String type;
    private String value;
    public void setProp(String prop) {
        this.prop = prop;
    }
    public String getProp() {
        return prop;
    }

    public void setDirect(String direct) {
        this.direct = direct;
    }
    public String getDirect() {
        return direct;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getType() {
        return type;
    }

    public void setValue(String value) {
        this.value = value;
    }
    public String getValue() {
        return value;
    }

}