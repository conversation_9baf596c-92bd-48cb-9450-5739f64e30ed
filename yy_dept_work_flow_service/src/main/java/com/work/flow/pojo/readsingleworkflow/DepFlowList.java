/**
 * Copyright 2022 bejson.com
 */
package com.work.flow.pojo.readsingleworkflow;

import lombok.Getter;
import lombok.Setter;

/**
 * Auto-generated: 2022-11-07 10:39:13
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Getter
@Setter
public class DepFlowList {

    private int id;
    private String name;
    private String userName;
    private String projectName;
    private int value;
    private String label;


}