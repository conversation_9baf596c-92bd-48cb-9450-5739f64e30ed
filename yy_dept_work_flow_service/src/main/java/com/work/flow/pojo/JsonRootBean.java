/**
  * Copyright 2022 bejson.com 
  */
package com.work.flow.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Auto-generated: 2022-09-06 20:38:17
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class JsonRootBean {

    private String flag;
    private String code;
    private String msg;
    @JsonProperty("data")
    private JsonRootBeanData data;

    public void setFlag(String flag) {
         this.flag = flag;
     }
     public String getFlag() {
         return flag;
     }

    public void setCode(String code) {
         this.code = code;
     }
     public String getCode() {
         return code;
     }

    public void setMsg(String msg) {
         this.msg = msg;
     }
     public String getMsg() {
         return msg;
     }

    public void setJsonRootBeanData(JsonRootBeanData data) {
         this.data = data;
     }
     public JsonRootBeanData getJsonRootBeanData() {
         return data;
     }

}